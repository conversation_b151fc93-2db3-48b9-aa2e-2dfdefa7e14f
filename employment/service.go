package employment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	events2 "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	employmentPb "github.com/epifi/gamma/api/employment"
	consumerPb "github.com/epifi/gamma/api/employment/consumer"
	employmentEvents "github.com/epifi/gamma/api/employment/events"
	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/search"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	usersPb "github.com/epifi/gamma/api/user"
	userGroup "github.com/epifi/gamma/api/user/group"
	onPb "github.com/epifi/gamma/api/user/onboarding"
	seonPb "github.com/epifi/gamma/api/vendorgateway/appscreener/seon"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/employment/config/genconf"
	"github.com/epifi/gamma/employment/dao"
	"github.com/epifi/gamma/employment/events"
	wireTypes "github.com/epifi/gamma/employment/wire/types"
	"github.com/epifi/gamma/frontend/deposit/ui"
	pkgDl "github.com/epifi/gamma/pkg/deeplink"
	pkgEmp "github.com/epifi/gamma/pkg/employment"
)

const (
	transactionMaxRetry          = 3
	campaignNameField            = "campaign_name"
	updateEmploymentCampaignName = "CAMPAIGN_NAME_UPDATE_EMPLOYMENT"
)

var (
	processToChecksMap = map[employmentPb.ProcessName][]employmentPb.CheckName{
		employmentPb.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION: {
			employmentPb.CheckName_CHECK_NAME_WORK_EMAIL_VERIFICATION,
		},
		employmentPb.ProcessName_PROCESS_NAME_EPFO_VERIFICATION: {
			employmentPb.CheckName_CHECK_NAME_EPFO_SELECTED_COMPANY_MATCH,
			employmentPb.CheckName_CHECK_NAME_EPFO_SIMILAR_COMPANY_NAME_MATCH,
			employmentPb.CheckName_CHECK_NAME_EPFO_ENTERED_COMPANY_NAME_MATCH,
			employmentPb.CheckName_CHECK_NAME_DOMAIN_NAME_VERIFICATION,
		},
	}
	profileFieldAbbreviation = map[bankCustPb.ProfileField]string{
		bankCustPb.ProfileField_PROFILE_FIELD_OCCUPATION:   "OCC",
		bankCustPb.ProfileField_PROFILE_FIELD_INCOME_RANGE: "IR",
	}
	// list of profile update source that will be sent to bank
	profileUpdateSourceSupportedForBank = map[employmentPb.UpdateSource]bool{
		employmentPb.UpdateSource_UPDATE_SOURCE_USER_PROFILE:                         true,
		employmentPb.UpdateSource_UPDATE_SOURCE_VKYC:                                 true,
		employmentPb.UpdateSource_UPDATE_SOURCE_DEV_ACTION:                           true,
		employmentPb.UpdateSource_UPDATE_SOURCE_INCOME_OCCUPATION_DISCREPANCY_PROMPT: true,
		employmentPb.UpdateSource_UPDATE_SOURCE_RISKOPS_ACTION:                       true,
		employmentPb.UpdateSource_UPDATE_SOURCE_HOME_TOP_NOTICE_BAR:                  true,
		employmentPb.UpdateSource_UPDATE_SOURCE_PERIODIC_KYC:                         true,
	}
)

type Service struct {
	employmentPb.UnimplementedEmploymentServer
	EmploymentDataDao                        dao.EmploymentDataDao
	VgEmploymentClient                       vgEmploymentPb.EmploymentClient
	Config                                   *genconf.Config
	UsersClient                              usersPb.UsersClient
	ActorClient                              actor.ActorClient
	BrandPrefixToCompanyMapping              map[string][]*employmentPb.CompanyInfo
	EmpVerificationPublisher                 queue.Publisher
	SearchClient                             search.ActionBarClient
	EventsBroker                             events2.Broker
	EmailIdRegex                             *regexp.Regexp
	UrlValidationRegex                       *regexp.Regexp
	DomainNameRegex                          *regexp.Regexp
	OnboardingClient                         onPb.OnboardingClient
	EmplVerificationCheckDao                 dao.EmplVerificationCheckDao
	EmplVerificationProcessDao               dao.EmploymentVerificationProcessDao
	CheckHandlers                            CheckHandler
	CheckHandlerMap                          map[employmentPb.CheckName]Check
	SeonClient                               seonPb.SeonClient
	NcClient                                 namecheck.UNNameCheckClient
	EmployerDao                              dao.EmployerDao
	UpdateEmploymentPublisher                wireTypes.UpdateEmploymentPublisher
	DomainDao                                dao.DomainDetailsDao
	EmployerPiMappingDao                     dao.EmployerPiMappingDao
	IncomeUpdatePublisher                    wireTypes.IncomeUpdatePublisher
	UserGroupClient                          userGroup.GroupClient
	BankCustomerClient                       bankCustPb.BankCustomerServiceClient
	EmployerPiMappingUpdateEventSqsPublisher wireTypes.EmployerPiMappingUpdateEventSqsPublisher
}

func NewService(
	employmentDataDao dao.EmploymentDataDao, employmentClient vgEmploymentPb.EmploymentClient, conf *genconf.Config, usersClient usersPb.UsersClient,
	actorClient actor.ActorClient, empVerificationPublisher wireTypes.EmploymentVerificationPublisher, eventsBroker events2.Broker, searchClient search.ActionBarClient,
	onboardingClient onPb.OnboardingClient, emplVerificationCheckDao dao.EmplVerificationCheckDao, emplVerificationProcessDao dao.EmploymentVerificationProcessDao,
	handlers CheckHandler, seonClient seonPb.SeonClient, ncClient namecheck.UNNameCheckClient, employerDao dao.EmployerDao, updateEmploymentPublisher wireTypes.UpdateEmploymentPublisher,
	domainDao dao.DomainDetailsDao, employerPiMappingDao dao.EmployerPiMappingDao, incomeUpdatePublisher wireTypes.IncomeUpdatePublisher,
	employerPiMappingUpdateEventSqsPublisher wireTypes.EmployerPiMappingUpdateEventSqsPublisher, userGroupClient userGroup.GroupClient, bankCustomerClient bankCustPb.BankCustomerServiceClient) *Service {
	brandPrefixToCompanyMapping := getBrandPrefixToCompanyNameMappings(conf.CompanyMappingFilePath())
	return &Service{
		EmploymentDataDao:                        employmentDataDao,
		VgEmploymentClient:                       employmentClient,
		Config:                                   conf,
		BrandPrefixToCompanyMapping:              brandPrefixToCompanyMapping,
		UsersClient:                              usersClient,
		ActorClient:                              actorClient,
		EmpVerificationPublisher:                 empVerificationPublisher,
		SearchClient:                             searchClient,
		EventsBroker:                             eventsBroker,
		UrlValidationRegex:                       regexp.MustCompile(conf.URLValidationRegex()),
		EmailIdRegex:                             regexp.MustCompile(conf.EmailValidationRegex()),
		DomainNameRegex:                          regexp.MustCompile(conf.DomainNameRegex()),
		OnboardingClient:                         onboardingClient,
		EmplVerificationCheckDao:                 emplVerificationCheckDao,
		EmplVerificationProcessDao:               emplVerificationProcessDao,
		CheckHandlers:                            handlers,
		SeonClient:                               seonClient,
		NcClient:                                 ncClient,
		EmployerDao:                              employerDao,
		UpdateEmploymentPublisher:                updateEmploymentPublisher,
		DomainDao:                                domainDao,
		EmployerPiMappingDao:                     employerPiMappingDao,
		IncomeUpdatePublisher:                    incomeUpdatePublisher,
		UserGroupClient:                          userGroupClient,
		BankCustomerClient:                       bankCustomerClient,
		EmployerPiMappingUpdateEventSqsPublisher: employerPiMappingUpdateEventSqsPublisher,
	}
}

func (s *Service) ProcessEmploymentData(ctx context.Context,
	req *employmentPb.ProcessEmploymentDataRequest) (*employmentPb.ProcessEmploymentDataResponse, error) {
	actorId := req.GetActorId()

	// fetch existing data
	empData, err := s.EmploymentDataDao.GetByActorId(ctx, actorId)
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error fetching employment data for actor", zap.Error(err))
		return &employmentPb.ProcessEmploymentDataResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error fetching emp data from db: %v", err)),
		}, nil
	}

	// in EPFO we don't get employment type and salary to bye pass pre-validations we need this condition
	if req.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN &&
		req.GetEmploymentType() == employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		req.EmploymentType = empData.GetEmploymentType()
		req.OccupationType = empData.GetOccupationType()
		req.AnnualSalary = empData.GetEmploymentInfo().GetAnnualSalary()
	}

	// check if user is eligible for create or update
	statusCode, isEligible := s.checkPreValidation(ctx, req, empData)
	if !isEligible {
		return &employmentPb.ProcessEmploymentDataResponse{
			Status: statusCode,
		}, nil
	}

	newEmploymentData := s.constructEmploymentData(ctx, req, employmentPb.ProcessingIntent_DECLARATION_ONLY, empData)

	switch {
	// publish rudder event in case of epfo screen
	case req.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN:
		s.triggerEPFORudderEvent(ctx, req)
	case req.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS:
		err = s.publishSalaryDataOpsPacket(ctx, req)
		if err != nil {
			logger.Error(ctx, "failed to publish packet to update employment details", zap.Error(err))
			return &employmentPb.ProcessEmploymentDataResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("failed to publish packet to update employment details, %v", err.Error())),
			}, nil
		}
	}
	if newEmploymentData, err = s.performCreateEntryTxn(ctx, req.GetActorId(), newEmploymentData); err != nil {
		logger.Error(ctx, "txn error in updating employment data", zap.Error(err))
		return &employmentPb.ProcessEmploymentDataResponse{
			Status: rpc.StatusInternal(),
		}, err
	}
	s.updateProfileAtBank(ctx, newEmploymentData, actorId, err)
	if req.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN && req.GetEmploymentType() == employmentPb.EmploymentType_SALARIED {
		_, err = s.handleSalariedUserEmpDataCreation(ctx, req, newEmploymentData)
		if err != nil {
			logger.Error(ctx, "Error in updating epfo entry", zap.Error(err))
		}
	}
	// if new employment is updated successfully, then only we will publish the event for income update
	_ = s.publishIncomeUpdateEvent(ctx, req.GetUpdateSource(), empData, newEmploymentData)
	return &employmentPb.ProcessEmploymentDataResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) DeleteEmploymentData(ctx context.Context, req *employmentPb.DeleteEmploymentDataRequest) (*employmentPb.DeleteEmploymentDataResponse, error) {
	if req.GetActorId() == "" {
		return &employmentPb.DeleteEmploymentDataResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("empty actorId"),
		}, nil
	}
	if err := storagev2.RunCRDBIdempotentTxn(ctx, 3, func(txnCtx context.Context) error {
		if dbErr := s.EmplVerificationCheckDao.DeleteByActorId(ctx, req.GetActorId()); dbErr != nil {
			logger.Error(ctx, "failed to delete employment verification checks data by actorId", zap.Error(dbErr))
			return dbErr
		}
		if dbErr := s.EmplVerificationProcessDao.DeleteByActorId(ctx, req.GetActorId()); dbErr != nil {
			logger.Error(ctx, "failed to delete employment verification processes by actorId", zap.Error(dbErr))
			return dbErr
		}
		if dbErr := s.EmploymentDataDao.DeleteByActorId(ctx, req.GetActorId()); dbErr != nil {
			logger.Error(ctx, "failed to delete employment data by actorId", zap.Error(dbErr))
			return dbErr
		}
		return nil
	}); err != nil {
		logger.Error(ctx, "error in deleting employment data", zap.Error(err))
		return &employmentPb.DeleteEmploymentDataResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &employmentPb.DeleteEmploymentDataResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) triggerEPFORudderEvent(ctx context.Context, req *employmentPb.ProcessEmploymentDataRequest) {
	// trigger `StartedEmploymentVerificationServer` event
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.EventsBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events.NewStartedEmploymentVerificationServer(req.GetActorId(), req.GetEmploymentType().String(), req.GetCompanyInfo().GetCompanyName(),
				req.GetCompanyInfo().GetIsEpfRegistered(), req.GetCompanyInfo().GetIsManualInput()))
	})
}

func (s *Service) publishSalaryDataOpsPacket(ctx context.Context, req *employmentPb.ProcessEmploymentDataRequest) error {
	// Publish packet to update employment flow
	if req.GetEmployerInfo().GetEmployerId() != "" {
		// Employer id non-empty ensures that the employer is valid. If db allows for non-verified employer udpate this.
		updateReq := &employmentPb.ProcessEmploymentUpdateEventRequest{
			ActorId: req.GetActorId(),
			EmployerInfo: &employmentPb.UpdatedEmployerInfo{
				Id:         req.GetEmployerInfo().GetEmployerId(),
				IsVerified: true,
			},
		}
		_, err := s.UpdateEmploymentPublisher.Publish(ctx, updateReq)
		return err
	}
	return nil
}

func (s *Service) performCreateEntryTxn(ctx context.Context, actorId string, empData *employmentPb.EmploymentData) (*employmentPb.EmploymentData, error) {
	var err error
	txnErr := storagev2.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(ctx context.Context) error {
		// soft delete existing entry
		if err = s.EmploymentDataDao.DeleteByActorId(ctx, actorId); err != nil && !storagev2.IsRecordNotFoundError(err) {
			logger.Error(ctx, "failed to delete employment data by actor id", zap.Error(err))
			return err
		}
		// create new entry in db
		empData, err = s.EmploymentDataDao.CreateEmploymentData(ctx, empData)
		if err != nil {
			logger.Error(ctx, "error creating employment data entry", zap.Error(err))
			return err
		}
		return nil
	})
	return empData, txnErr
}

// nolint:dupl
func (s *Service) updateProfileAtBank(ctx context.Context, empData *employmentPb.EmploymentData, actorId string, err error) {
	if s.Config.EmploymentDetailsUpdateConfig().IsEnabled() && profileUpdateSourceSupportedForBank[empData.GetUpdatedBySource()] {
		goroutine.Run(ctx, 90*time.Second, func(ctx context.Context) {
			upResp, upErr := s.BankCustomerClient.UpdateProfileAtBank(ctx, &bankCustPb.UpdateProfileAtBankRequest{
				Channel:         bankCustPb.ProfileUpdateChannel_PROFILE_UPDATE_CHANNEL_APP,
				ClientRequestId: getProfileUpdateReqId(bankCustPb.ProfileField_PROFILE_FIELD_INCOME_RANGE, empData.GetId()),
				ActorId:         actorId,
				Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
				ProfileFields:   []bankCustPb.ProfileField{bankCustPb.ProfileField_PROFILE_FIELD_INCOME_RANGE},
				EmploymentData:  empData,
			})
			if err = epifigrpc.RPCError(upResp, upErr); err != nil {
				logger.Error(ctx, "could not update profile at bank", zap.Error(err),
					zap.String(logger.VENDOR_REQUEST, getProfileUpdateReqId(bankCustPb.ProfileField_PROFILE_FIELD_INCOME_RANGE, empData.GetId())))
			}
		})
		goroutine.Run(ctx, 90*time.Second, func(ctx context.Context) {
			upResp, upErr := s.BankCustomerClient.UpdateProfileAtBank(ctx, &bankCustPb.UpdateProfileAtBankRequest{
				Channel:         bankCustPb.ProfileUpdateChannel_PROFILE_UPDATE_CHANNEL_APP,
				ClientRequestId: getProfileUpdateReqId(bankCustPb.ProfileField_PROFILE_FIELD_OCCUPATION, empData.GetId()),
				ActorId:         actorId,
				Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
				ProfileFields:   []bankCustPb.ProfileField{bankCustPb.ProfileField_PROFILE_FIELD_OCCUPATION},
				EmploymentData:  empData,
			})
			if err = epifigrpc.RPCError(upResp, upErr); err != nil {
				logger.Error(ctx, "could not update profile at bank", zap.Error(err),
					zap.String(logger.VENDOR_REQUEST, getProfileUpdateReqId(bankCustPb.ProfileField_PROFILE_FIELD_OCCUPATION, empData.GetId())))
			}
		})
	}
}

func getProfileUpdateReqId(field bankCustPb.ProfileField, employmentId string) string {
	dateToday := datetime.TimeToDateInLoc(time.Now(), datetime.IST)
	dateStr := datetime.DateToString(dateToday, "********", datetime.IST)
	employmentIdCleaned := strings.ReplaceAll(employmentId, "-", "")
	return dateStr + profileFieldAbbreviation[field] + employmentIdCleaned
}

// validate request for process employment data v2 api
func (s *Service) checkPreValidation(ctx context.Context, req *employmentPb.ProcessEmploymentDataRequest, empData *employmentPb.EmploymentData) (*rpc.Status, bool) {

	// this case is allowed without employment type
	if req.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_RISKOPS_ACTION {
		if empData.GetEmploymentInfo().GetIncOccDiscrepancyAction() == req.GetIncOccDiscrepancyAction() {
			logger.Info(ctx, fmt.Sprintf("Skipping employment update since income discrepanancy action is same, %v", req.GetIncOccDiscrepancyAction()))
			return rpc.StatusOk(), false
		}
		return nil, true
	}

	// employment type is compulsory for every request except riskops action since that just mark if income occupation discrepancy is verified or not
	if req.GetEmploymentType() == employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		logger.Info(ctx, "debug: employment type unspecified")
		return rpc.StatusInvalidArgumentWithDebugMsg("employment type unspecified"), false
	}
	// occupation needs to be mentioned along with employment if feature is enabled and source is not dev action or epfo screen
	if !s.isOccupationValidFromCtx(ctx, req.GetEmploymentType(), req.GetOccupationType(), req.GetUpdateSource()) {
		logger.Info(ctx, "occupation type not mentioned")
		return rpc.StatusInvalidArgumentWithDebugMsg("occupation type not mentioned"), false
	}

	salRange := req.GetAnnualSalary().GetRange()
	if salRange != nil {
		if !pkgEmp.IsCurrencyCodeSupported(salRange.GetCurrencyCode()) {
			logger.Error(ctx, "unsupported currency", zap.String(logger.COUNTRY, salRange.GetCurrencyCode().String()))
			return rpc.StatusInvalidArgumentWithDebugMsg("invalid currency"), false
		}
		if isAlternateDisplayedEmpty(salRange.GetAlternateDisplayed()) {
			req.GetAnnualSalary().GetRange().AlternateDisplayed = nil
		}
		if salRange.GetAlternateDisplayed() != nil && salRange.GetAlternateDisplayed().GetCurrencyCode() == types.CurrencyCode_CURRENCY_CODE_UNSPECIFIED {
			logger.Error(ctx, "no currency mentioned in alternate display")
			return rpc.StatusInvalidArgumentWithDebugMsg("no currency mentioned in alternate display"), false
		}
	}

	switch req.GetUpdateSource() {
	case employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN:
		if req.GetEmploymentType() == employmentPb.EmploymentType_SALARIED && (req.GetCompanyInfo().GetCompanyName() == "") {
			logger.Info(ctx, "debug: company info not present for salaried user")
			return rpc.StatusInvalidArgumentWithDebugMsg("company info cant be nil"), false
		}
	case employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_INAPP,
		employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_REG_EMPLOYER_STAGE_SCRIPT,
		employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_ONB_STAGE_UPDATE_CONSUMER,
		employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS:
		if req.GetEmploymentType() != employmentPb.EmploymentType_SALARIED {
			return rpc.StatusInvalidArgumentWithDebugMsg("employment type can only be salaried"), false
		}
		if req.GetEmployerInfo().GetEmployerId() == empData.GetEmployerId() {
			logger.Info(ctx, "Employer id same skipping update")
			return rpc.StatusOk(), false
		}
	case employmentPb.UpdateSource_UPDATE_SOURCE_USER_PROFILE:
	case employmentPb.UpdateSource_UPDATE_SOURCE_VKYC,
		employmentPb.UpdateSource_UPDATE_SOURCE_HOME_TOP_NOTICE_BAR,
		employmentPb.UpdateSource_UPDATE_SOURCE_PERIODIC_KYC:
		if empData.GetEmploymentType() == req.GetEmploymentType() &&
			isIncomeSame(empData, req.GetAnnualSalary()) &&
			empData.GetEmployerId() == req.GetEmployerInfo().GetEmployerId() {
			logger.Info(ctx, fmt.Sprintf("Skipping employment update since all info is same, %v, %v, %v", req.GetEmploymentType(),
				req.GetAnnualSalary().GetAbsolute(), req.GetEmployerInfo().GetEmployerId()))
			return rpc.StatusOk(), false
		}
	case employmentPb.UpdateSource_UPDATE_SOURCE_DEV_ACTION:
		if empData.GetEmploymentType() == req.GetEmploymentType() &&
			isIncomeSame(empData, req.GetAnnualSalary()) && empData.GetOccupationType() == req.GetOccupationType() {
			logger.Info(ctx, fmt.Sprintf("Skipping employment update since params have not changed, %v, %v, %v", req.GetEmploymentType(),
				req.GetAnnualSalary().GetAbsolute(), req.GetEmployerInfo().GetEmployerId()))
			return rpc.StatusOk(), false
		}
	default:
		return nil, true
	}
	return nil, true
}

func isAlternateDisplayedEmpty(alt *screening.AnnualSalaryRange_AlternateDisplayed) bool {
	return alt == nil || (alt.GetMinVal() == 0 && alt.GetMaxVal() == 0 && alt.GetCurrencyCode() == types.CurrencyCode_CURRENCY_CODE_UNSPECIFIED)
}

// compare absolute value and range for given salaries
func isIncomeSame(empData *employmentPb.EmploymentData, screeningSalary *screening.AnnualSalary) bool {
	if empData.GetEmploymentInfo().GetAnnualSalary().GetAbsolute() == screeningSalary.GetAbsolute() &&
		empData.GetEmploymentInfo().GetAnnualSalary().GetRange().GetMinValue() == screeningSalary.GetRange().GetMinValue() &&
		empData.GetEmploymentInfo().GetAnnualSalary().GetRange().GetMaxValue() == screeningSalary.GetRange().GetMaxValue() &&
		isCurrencySame(empData.GetEmploymentInfo().GetAnnualSalary().GetRange().GetCurrencyCode(), screeningSalary.GetRange().GetCurrencyCode()) &&
		proto.Equal(empData.GetEmploymentInfo().GetAnnualSalary().GetRange().GetAlternateDisplayed(), screeningSalary.GetRange().GetAlternateDisplayed()) {
		return true
	}
	return false
}

func isCurrencySame(cur1, cur2 types.CurrencyCode) bool {
	// empty currency means it is INR
	isCurr1Inr := cur1 == types.CurrencyCode_CURRENCY_CODE_UNSPECIFIED || cur1 == types.CurrencyCode_INR
	isCurr2Inr := cur2 == types.CurrencyCode_CURRENCY_CODE_UNSPECIFIED || cur2 == types.CurrencyCode_INR
	if isCurr1Inr && isCurr2Inr {
		return true
	}
	return cur1 == cur2
}

func (s *Service) handleSalariedUserEmpDataCreation(ctx context.Context,
	req *employmentPb.ProcessEmploymentDataRequest, empData *employmentPb.EmploymentData) (*employmentPb.EmploymentData, error) {
	companyInfo := req.GetCompanyInfo()
	epfoProcess := &employmentPb.EmploymentVerificationProcess{
		ActorId:                   req.ActorId,
		ProcessName:               employmentPb.ProcessName_PROCESS_NAME_EPFO_VERIFICATION,
		VerificationProcessStatus: getEpfoProcessStatus(employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_SKIPPED),
		VerificationResult:        employmentPb.EmploymentVerificationResult_EMPLOYMENT_VERIFICATION_RESULT_HOLD,
	}
	switch {
	case companyInfo.GetIsManualInput():
		// keep user in HOLD state if they have manually added company name
		s.triggerEmplVerificationServerRespEvent(ctx, req.ActorId, "", "", "", "", "", "", "", nil, events.VerificationStatusHold,
			req.EmploymentType.String())
		epfoProcess.HoldReason = employmentPb.HoldReason_COMPANY_NAME_MANUAL_INPUT

	case !companyInfo.GetIsEpfRegistered():
		s.triggerEmplVerificationServerRespEvent(ctx, req.ActorId, "", "", "", "", "", "", "", nil, events.VerificationStatusAccepted,
			req.EmploymentType.String())

		epfoProcess.VerificationProcessStatus = getEpfoProcessStatus(employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_COMPLETED)
		epfoProcess.VerificationResult = employmentPb.EmploymentVerificationResult_EMPLOYMENT_VERIFICATION_RESULT_REJECTED
		epfoProcess.RejectionReason = employmentPb.RejectionReason_COMPANY_NOT_PF_REGISTERED

		if s.Config.AllowUserIfCompanyNotEpfoRegistered() {
			// mark user as ACCEPTED if their company is not registered with EPFO
			epfoProcess.VerificationResult = employmentPb.EmploymentVerificationResult_EMPLOYMENT_VERIFICATION_RESULT_ACCEPTED
			epfoProcess.AcceptedReason = employmentPb.AcceptedReason_ACCEPTED_REASON_PASS_NON_EPFO_REG_COMPANY
		}

	default:
		// if user has selected a company name from the suggestions and their company is EPFO registered
		epfoProcess.VerificationProcessStatus = getEpfoProcessStatus(employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_STATUS_INITIATED)
		epfoProcess.VerificationResult = employmentPb.EmploymentVerificationResult_EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED
	}
	var err error

	epfoProcess.EmploymentDataId = empData.Id
	epfoProcess.ClientReqId = req.ClientReqId
	epfoProcess, err = s.EmplVerificationProcessDao.Create(ctx, epfoProcess)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error creating epfo process entry for epfo value: %v", epfoProcess), zap.Error(err))
		return nil, err
	}
	if companyInfo.GetIsEpfRegistered() {
		err = s.publishEmpVerificationMessage(ctx, req.GetActorId(), req.GetClientReqId())
		if err != nil {
			return nil, err
		}
	}
	return empData, nil
}

func (s *Service) GetEmploymentInfo(ctx context.Context,
	req *employmentPb.GetEmploymentInfoRequest) (res *employmentPb.GetEmploymentInfoResponse, err error) {
	res = &employmentPb.GetEmploymentInfoResponse{}
	actorId := req.GetActorId()
	employmentData, empProcessChecks, err := GetEmploymentDataProcess(ctx, s.EmploymentDataDao, s.EmplVerificationProcessDao, s.EmplVerificationCheckDao, actorId, req.GetClientReqId(), req.GetProcessNames())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "error fetching employment data entry", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		}
		return res, nil
	}
	return &employmentPb.GetEmploymentInfoResponse{
		EmploymentData:         employmentData,
		EmploymentProcessCheck: empProcessChecks,
		Status:                 rpc.StatusOk(),
	}, nil
}

// nolint:unparam
func (s *Service) updateVerificationProcessStatusAndResult(ctx context.Context, empData *employmentPb.EmploymentData,
	processData *employmentPb.EmploymentVerificationProcess, processStatus employmentPb.EmploymentVerificationProcessStatus,
	result employmentPb.EmploymentVerificationResult, holdReason employmentPb.HoldReason,
	rejectionReason employmentPb.RejectionReason, acceptedReason employmentPb.AcceptedReason) (err error) {

	if isProcessEntryPresentInDB(processData) {
		err = s.EmplVerificationProcessDao.Update(ctx, &employmentPb.EmploymentVerificationProcess{
			Id:                        processData.Id,
			VerificationProcessStatus: getEpfoProcessStatus(convertEmplStatusToEpfoStatus(processStatus)),
			VerificationResult:        result,
			HoldReason:                holdReason,
			RejectionReason:           rejectionReason,
			AcceptedReason:            acceptedReason,
		}, []employmentPb.EmploymentVerificationProcessFieldMask{
			employmentPb.EmploymentVerificationProcessFieldMask_EMPLOYMENT_VERIFICATION_PROCESS_VERIFICATION_PROCESS_STATUS,
			employmentPb.EmploymentVerificationProcessFieldMask_EMPLOYMENT_VERIFICATION_PROCESS_VERIFICATION_RESULT,
			employmentPb.EmploymentVerificationProcessFieldMask_EMPLOYMENT_VERIFICATION_PROCESS_HOLD_REASON,
			employmentPb.EmploymentVerificationProcessFieldMask_EMPLOYMENT_VERIFICATION_PROCESS_REJECTION_REASON,
			employmentPb.EmploymentVerificationProcessFieldMask_EMPLOYMENT_VERIFICATION_PROCESS_ACCEPTED_REASON,
		})
	} else {
		err = s.EmploymentDataDao.Update(ctx, &employmentPb.EmploymentData{
			ActorId:                   empData.ActorId,
			VerificationProcessStatus: processStatus,
			VerificationResult:        result,
			HoldReason:                holdReason,
			RejectionReason:           rejectionReason,
			AcceptedReason:            acceptedReason,
		}, []employmentPb.EmploymentDataFieldMask{
			employmentPb.EmploymentDataFieldMask_VERIFICATION_PROCESS_STATUS,
			employmentPb.EmploymentDataFieldMask_VERIFICATION_RESULT,
			employmentPb.EmploymentDataFieldMask_HOLD_REASON,
			employmentPb.EmploymentDataFieldMask_REJECTION_REASON,
			employmentPb.EmploymentDataFieldMask_ACCEPTED_REASON,
		})
	}
	if err != nil {
		logger.Error(ctx, "error updating process status and result", zap.String("processStatus", processStatus.String()),
			zap.String("result", result.String()), zap.Error(err))
	} else {
		logger.Info(ctx, "updated employment verification process status and result",
			zap.String("processStatus", processStatus.String()), zap.String("result", result.String()),
			zap.String("holdReason", holdReason.String()), zap.String("rejectionReason", rejectionReason.String()),
			zap.String("acceptedReason", acceptedReason.String()))
	}
	return err
}

func (s *Service) publishEmpVerificationMessage(ctx context.Context, actorId, clientReqId string) error {
	msgId, err := s.EmpVerificationPublisher.Publish(ctx, &consumerPb.VerifyEmploymentDetailsRequest{
		ActorId:          actorId,
		ClientReqId:      clientReqId,
		ProcessStartedAt: timestamppb.Now(),
	})
	if err != nil {
		logger.Error(ctx, "error publishing message to verify employment info", zap.Error(err))
		return fmt.Errorf("error publishing message to verify employment: %w", err)
	}
	logger.Info(ctx, "successfully published message to verify employment info", zap.String("msgId", msgId))
	return nil
}

// nolint: unparam
func (s *Service) triggerEmplVerificationServerRespEvent(ctx context.Context, actorId string, vendor, reqStatus,
	reqFailedReason, validationStatus, validationFailureReason, validationType, domainName string,
	orgPfData *vgEmploymentPb.EmployeeNameSearchResponse, verificationStatus, emplType string) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.EventsBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events.NewEmploymentVerificationResponseServer(actorId, vendor, reqStatus, reqFailedReason, validationStatus,
				validationFailureReason, validationType, domainName, orgPfData.GetIsNameUnique(), orgPfData.GetIsEmployed(),
				orgPfData.GetIsNameExact(), orgPfData.GetOrganizationName(), orgPfData.GetVendorStatus().String(), verificationStatus, emplType))
	})
}

func (s *Service) validateAndGetOccupationType(ctx context.Context, req *employmentPb.ProcessEmploymentDataRequest, existingOcc employmentPb.OccupationType) employmentPb.OccupationType {
	if s.getOccupationType(ctx, req) != employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
		return s.getOccupationType(ctx, req)
	}
	return existingOcc
}

func (s *Service) getOccupationType(ctx context.Context, req *employmentPb.ProcessEmploymentDataRequest) employmentPb.OccupationType {
	empType, err := s.getEmploymentObjectFromEmploymentType(req.GetEmploymentType())
	if err != nil {
		logger.Error(ctx, "", zap.Error(err))
		return employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED
	}
	if empType.RequiresOccupation {
		return req.GetOccupationType()
	}
	if len(empType.SupportedOccupationTypes) == 1 {
		occ := employmentPb.OccupationType(employmentPb.OccupationType_value[empType.SupportedOccupationTypes[0]])
		return occ
	}
	logger.Error(ctx, "unable to find occupation for given employment", zap.String("occupation_type", req.GetOccupationType().String()))
	return employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED
}

func (s *Service) getEmploymentObjectFromEmploymentType(reqEmp employmentPb.EmploymentType) (*pkgDl.EmploymentType, error) {
	for _, empTypeObject := range s.Config.EmploymentTypeUiView().EmploymentTypes {
		if empTypeObject.Type == reqEmp.String() {
			return empTypeObject, nil
		}
	}
	return nil, fmt.Errorf("unable to find given employment type object in Supported Employments for %v", reqEmp.String())
}

// nolint: funlen, dupl
func (s *Service) constructEmploymentData(ctx context.Context, req *employmentPb.ProcessEmploymentDataRequest, intent employmentPb.ProcessingIntent, empData *employmentPb.EmploymentData) *employmentPb.EmploymentData {
	// creating a deep copy of empData to avoid overwriting
	empDataCopy := proto.Clone(empData).(*employmentPb.EmploymentData)
	// create new object
	newEmpData := &employmentPb.EmploymentData{
		ActorId:                   req.GetActorId(),
		EmploymentType:            empDataCopy.GetEmploymentType(),
		EmploymentInfo:            empDataCopy.GetEmploymentInfo(),
		VerificationResult:        empDataCopy.GetVerificationResult(),
		VerificationProcessStatus: empDataCopy.GetVerificationProcessStatus(),
		RejectionReason:           empDataCopy.GetRejectionReason(),
		HoldReason:                empDataCopy.GetHoldReason(),
		AcceptedReason:            empDataCopy.GetAcceptedReason(),
		OrgPfData:                 empDataCopy.GetOrgPfData(),
		ProcessingIntent:          intent,
		EmployerId:                empDataCopy.GetEmployerId(),
		UpdatedBySource:           req.GetUpdateSource(),
		UpdatedBySourceIdentifier: empDataCopy.GetUpdatedBySourceIdentifier(),
		OccupationType:            s.validateAndGetOccupationType(ctx, req, empDataCopy.GetOccupationType()),
	}

	if s.getOccupationType(ctx, req) != employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
		newEmpData.OccupationType = s.getOccupationType(ctx, req)
	}

	if newEmpData.GetEmploymentInfo() == nil {
		newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{}
	}

	// early return in case of riskops action
	if req.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_RISKOPS_ACTION {
		newEmpData.GetEmploymentInfo().IncOccDiscrepancyAction = req.GetIncOccDiscrepancyAction()
		return newEmpData
	}

	// update employment type and info only in case of discrepancy between existing data and new request
	if req.GetEmploymentType() != empDataCopy.GetEmploymentType() || !s.isEmploymentInfoSame(req, empData.GetEmploymentInfo()) {
		switch req.GetEmploymentType() {
		case employmentPb.EmploymentType_SALARIED:
			switch req.GetEmploymentInfoOptions().(type) {
			case *employmentPb.ProcessEmploymentDataRequest_CompanyInfo:
				newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
					CompanyName:              req.GetCompanyInfo().GetCompanyName(),
					IsEpfRegistered:          req.GetCompanyInfo().GetIsEpfRegistered(),
					IsCompanyNameManualInput: req.GetCompanyInfo().GetIsManualInput(),
					VendorId:                 req.GetCompanyInfo().GetVendorId(),
					EnteredText:              req.GetCompanyInfo().GetEnteredText(),
				}
				newEmpData.EmployerId = req.GetCompanyInfo().GetEmployerId()
			case *employmentPb.ProcessEmploymentDataRequest_EmployerInfo:
				newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
					EnteredText: req.GetEmployerInfo().GetEnteredText(),
				}
				newEmpData.EmployerId = req.GetEmployerInfo().GetEmployerId()
			}
		case employmentPb.EmploymentType_BUSINESS_OWNER:
			newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
				GstinNo: req.GetBusinessOwnerOption().GetGstinNo(),
			}
		case employmentPb.EmploymentType_WORKING_PROFESSIONAL:
			newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
				EnrollmentNo: req.GetEnrollmentNumberOption().GetEnrollmentNo(),
			}
		case employmentPb.EmploymentType_FREELANCER:
			newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
				PersonalProfileInfo: req.GetPersonalProfileInfo().GetPersonalProfileInfo(),
			}
		case employmentPb.EmploymentType_STUDENT:
			newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
				StudentGraduationYear: fmt.Sprintf("%v", req.GetStudentDetailsOption().GetYear()),
				StudentMailId:         req.GetStudentDetailsOption().GetMailId(),
			}
		default:
			newEmpData.EmploymentInfo = &employmentPb.EmploymentInfo{
				PersonalProfileInfo: req.GetPersonalProfileInfo().GetPersonalProfileInfo(),
			}
		}
		newEmpData.EmploymentType = req.GetEmploymentType()
		newEmpData.GetEmploymentInfo().AnnualSalary = empDataCopy.GetEmploymentInfo().GetAnnualSalary()
	}

	if req.GetAnnualSalary() != nil {
		newEmpData.GetEmploymentInfo().AnnualSalary = req.GetAnnualSalary()
		// if currency code is empty, fill it with indian rupee
		if req.GetAnnualSalary().GetRange() != nil && req.GetAnnualSalary().GetRange().GetCurrencyCode() == types.CurrencyCode_CURRENCY_CODE_UNSPECIFIED {
			newEmpData.GetEmploymentInfo().GetAnnualSalary().GetRange().CurrencyCode = types.CurrencyCode_INR
		}
	}

	return newEmpData
}

func (s *Service) isEmploymentInfoSame(firstSource *employmentPb.ProcessEmploymentDataRequest, secondSource *employmentPb.EmploymentInfo) bool {
	if firstSource.GetCompanyInfo().GetCompanyName() != secondSource.GetCompanyName() ||
		firstSource.GetPersonalProfileInfo().GetPersonalProfileInfo() != secondSource.GetPersonalProfileInfo() ||
		firstSource.GetCompanyInfo().GetIsEpfRegistered() != secondSource.GetIsEpfRegistered() ||
		firstSource.GetCompanyInfo().GetIsManualInput() != secondSource.GetIsCompanyNameManualInput() ||
		firstSource.GetCompanyInfo().GetVendorId() != secondSource.GetVendorId() ||
		firstSource.GetCompanyInfo().GetEnteredText() != secondSource.GetEnteredText() ||
		firstSource.GetBusinessOwnerOption().GetGstinNo() != secondSource.GetGstinNo() ||
		firstSource.GetEnrollmentNumberOption().GetEnrollmentNo() != secondSource.GetEnrollmentNo() ||
		fmt.Sprintf("%vf", firstSource.GetStudentDetailsOption().GetYear()) != (secondSource.GetStudentGraduationYear()) ||
		firstSource.GetStudentDetailsOption().GetMailId() != secondSource.GetStudentMailId() {
		return false
	}
	return true
}

func GetEmploymentDataProcess(ctx context.Context, dataDao dao.EmploymentDataDao, processDao dao.EmploymentVerificationProcessDao, checkDao dao.EmplVerificationCheckDao, actorId, clientReqId string, processNames []employmentPb.ProcessName) (*employmentPb.EmploymentData,
	[]*employmentPb.EmploymentProcessCheck, error) {
	empData, err := dataDao.GetByActorId(ctx, actorId)
	if err != nil {
		return nil, nil, err
	}
	var empInfos []*employmentPb.EmploymentProcessCheck
	for _, processName := range processNames {
		var empProcess *employmentPb.EmploymentVerificationProcess
		if clientReqId != "" {
			empProcess, err = processDao.GetByClientReqIdAndProcessName(ctx, clientReqId, processName)
		} else {
			empProcess, err = processDao.GetByActorIdAndProcessName(ctx, actorId, processName)
		}
		if err != nil {
			// not failing intentionally
			if !storagev2.IsRecordNotFoundError(err) {
				logger.Error(ctx, fmt.Sprintf("error in fetching process %v", processName), zap.Error(err))
			}
			continue
		}
		var empChecks []*employmentPb.EmploymentVerificationCheck
		checkNames, ok := processToChecksMap[processName]
		if ok {
			for _, checkName := range checkNames {
				empCheck, err := checkDao.GetByProcessIdAndName(ctx, empProcess.GetId(), checkName)
				if err != nil {
					// not failing intentionally
					if !storagev2.IsRecordNotFoundError(err) {
						logger.Error(ctx, fmt.Sprintf("error in fetching check %v", checkName), zap.Error(err))
					}
					continue
				}
				empChecks = append(empChecks, empCheck)
			}
		}
		empInfos = append(empInfos, &employmentPb.EmploymentProcessCheck{
			EmploymentVerificationProcess: empProcess,
			EmploymentVerificationCheck:   empChecks,
		})
	}
	return empData, empInfos, nil
}

func convertEmplStatusToEpfoStatus(emplStatus employmentPb.EmploymentVerificationProcessStatus) employmentPb.EpfoVerificationProcessStatus {
	switch emplStatus {
	case employmentPb.EmploymentVerificationProcessStatus_EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED:
		return employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_STATUS_INITIATED
	case employmentPb.EmploymentVerificationProcessStatus_EMPLOYMENT_VERIFICATION_PROCESS_COMPLETED:
		return employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_COMPLETED
	case employmentPb.EmploymentVerificationProcessStatus_EMPLOYMENT_VERIFICATION_PROCESS_FAILED:
		return employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_FAILED
	case employmentPb.EmploymentVerificationProcessStatus_EMPLOYMENT_VERIFICATION_PROCESS_SKIPPED:
		return employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_SKIPPED
	case employmentPb.EmploymentVerificationProcessStatus_EMPLOYMENT_VERIFICATION_PROCESS_MANUAL_INTERVENTION:
		return employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_MANUAL_INTERVENTION
	default:
		return employmentPb.EpfoVerificationProcessStatus_EPFO_VERIFICATION_PROCESS_STATUS_UNSPECIFIED
	}
}

func isProcessEntryPresentInDB(processData *employmentPb.EmploymentVerificationProcess) bool {
	return processData.Id != ""
}

func populateEmplDataForSalariedUser(actorId string, emplType employmentPb.EmploymentType,
	companyInfo *employmentPb.CompanyInfoOption) *employmentPb.EmploymentData {
	return &employmentPb.EmploymentData{
		ActorId:          actorId,
		EmploymentType:   emplType,
		ProcessingIntent: employmentPb.ProcessingIntent_VERIFICATION,
		EmploymentInfo: &employmentPb.EmploymentInfo{
			CompanyName:              companyInfo.GetCompanyName(),
			VendorId:                 companyInfo.GetVendorId(),
			IsEpfRegistered:          companyInfo.GetIsEpfRegistered(),
			IsCompanyNameManualInput: companyInfo.GetIsManualInput(),
			EnteredText:              companyInfo.GetEnteredText(),
		},
	}
}

func getEpfoProcessStatus(status employmentPb.EpfoVerificationProcessStatus) *employmentPb.ProcessStatus {
	return &employmentPb.ProcessStatus{
		ProcessStatus: &employmentPb.ProcessStatus_EpfoVerificationProcessStatus{
			EpfoVerificationProcessStatus: status,
		},
	}
}

// updateEmploymentInfo updated employment info field in employment data, minus the annual salary
// field which it fetches from the existing entry
func (s *Service) updateEmploymentInfo(ctx context.Context, empData *employmentPb.EmploymentData) (*employmentPb.EmploymentData, error) {
	// getting old entry to get the old salary
	oldData, err := s.EmploymentDataDao.GetByActorId(ctx, empData.GetActorId())
	if err != nil {
		logger.Error(ctx, "error fetching employment info", zap.Error(err), zap.String(logger.ACTOR_ID_V2, empData.GetActorId()))
		return nil, err
	}
	empData.EmploymentInfo.AnnualSalary = oldData.GetEmploymentInfo().GetAnnualSalary()
	empData.Id = oldData.GetId()

	// updating the employment info without overriding the salary
	if err = s.EmploymentDataDao.Update(ctx, empData, []employmentPb.EmploymentDataFieldMask{
		employmentPb.EmploymentDataFieldMask_EMPLOYMENT_INFO,
		employmentPb.EmploymentDataFieldMask_PROCESSING_INTENT,
	}); err != nil {
		logger.Error(ctx, "error updating employment info", zap.Error(err), zap.String(logger.ACTOR_ID_V2, empData.GetActorId()))
		return nil, err
	}
	return empData, nil
}

func (s *Service) updateOrCreateEmploymentData(ctx context.Context, empData *employmentPb.EmploymentData) error {
	_, err := s.EmploymentDataDao.GetByActorId(ctx, empData.GetActorId())
	if err != nil {
		if !storagev2.IsRecordNotFoundError(err) {
			logger.Error(ctx, "error fetching employment data for actor", zap.Error(err))
			return err
		}
		empData, err = s.EmploymentDataDao.CreateEmploymentData(ctx, empData)
		if err != nil {
			logger.Error(ctx, "error creating employment data entry", zap.Error(err))
			return err
		}
		return err
	}
	err = s.EmploymentDataDao.Update(ctx, empData, []employmentPb.EmploymentDataFieldMask{
		employmentPb.EmploymentDataFieldMask_EMPLOYMENT_INFO,
		employmentPb.EmploymentDataFieldMask_EMPLOYMENT_TYPE,
		employmentPb.EmploymentDataFieldMask_PROCESSING_INTENT,
	})
	if err != nil {
		logger.Error(ctx, "error updating employment data", zap.Error(err))
		return err
	}
	empData, err = s.EmploymentDataDao.GetByActorId(ctx, empData.GetActorId())
	return err
}

func getSalaryRange(salary float32) *user.SalaryRange {
	var maxSalary int32
	var minSalary int32
	salaryRanges := [][]int32{
		{0, 100000},
		{100000, 500000},
		{500000, 1000000},
		{1000000, 2500000},
		{2500000, 10000000},
		{10000000, 25000000},
		{25000000, 1000000000},
	}
	for _, salaryRange := range salaryRanges {
		if int32(salary) >= salaryRange[0] && int32(salary) < salaryRange[1] {
			minSalary = salaryRange[0]
			maxSalary = salaryRange[1]
			break
		}
	}
	return &user.SalaryRange{
		MinValue: minSalary,
		MaxValue: maxSalary,
	}
}

// update salary range in profile column of users while updating the employment.
func (s *Service) updateSalaryRangeInUser(ctx context.Context, actorId string, annualSalary *screening.AnnualSalary) error {

	// get user id from actor id
	actorRes, err := s.ActorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		logger.Error(ctx, "failed to get actor from id", zap.Error(te))
		return te
	}

	var userSalaryRange *user.SalaryRange

	// preference is given to annual salary
	if annualSalary != nil {
		userSalaryRange = &user.SalaryRange{
			MinValue: annualSalary.Range.GetMinValue(),
			MaxValue: annualSalary.Range.GetMaxValue(),
		}
		// if we receive only absolute value from client
		if annualSalary.Range == nil {
			userSalaryRange = getSalaryRange(annualSalary.GetAbsolute())
		}
	}

	updateUserRes, err := s.UsersClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User: &user.User{
			Id:      actorRes.GetActor().GetEntityId(),
			Profile: &user.Profile{SalaryRange: userSalaryRange},
		},
		UpdateMask: []user.UserFieldMask{user.UserFieldMask_SALARY_RANGE},
	})

	if te := epifigrpc.RPCError(updateUserRes, err); te != nil {
		logger.Error(ctx, "failed to update annual income of user", zap.Error(err))
		return err
	}
	return nil
}

// nolint: funlen
func (s *Service) UpdateNewEmploymentData(ctx context.Context, request *employmentPb.UpdateNewEmploymentDataRequest) (*employmentPb.UpdateNewEmploymentDataResponse, error) {

	var (
		actorId  = request.GetActorId()
		response = &employmentPb.UpdateNewEmploymentDataResponse{Status: rpc.StatusOk()}
		err      error
	)

	if !s.isOccupationValidFromCtx(ctx, request.GetEmploymentType(), request.GetOccupationType(), request.GetUpdateSource()) {
		return &employmentPb.UpdateNewEmploymentDataResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("occupation not mentioned"),
		}, nil
	}

	logger.Info(ctx, fmt.Sprintf("UpdateNewEmploymentData: source: %v id: %v type: %v", request.GetUpdateSource(), request.GetEmployerInfo().GetEmployerId(), request.GetEmploymentType()))
	newEmploymentData := s.getEmploymentDataUsers(request, employmentPb.ProcessingIntent_DECLARATION_ONLY)

	empData, err := s.EmploymentDataDao.GetByActorId(ctx, actorId)
	switch {
	case err != nil && !storagev2.IsRecordNotFoundError(err):
		logger.Error(ctx, "error getting employment data", zap.Error(err))
		return nil, err
	case storagev2.IsRecordNotFoundError(err):
		newEmploymentData, err = s.EmploymentDataDao.CreateEmploymentData(ctx, newEmploymentData)
		if err != nil {
			logger.Error(ctx, "error creating employment data entry", zap.Error(err))
			return nil, err
		}
		return response, nil
	default:
		break
	}

	switch {
	case request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_RISKOPS_ACTION:
		if empData.GetEmploymentInfo() == nil {
			empData.EmploymentInfo = &employmentPb.EmploymentInfo{}
		}
		empData.GetEmploymentInfo().IncOccDiscrepancyAction = request.GetIncOccDiscrepancyAction()
		daoErr := s.EmploymentDataDao.Update(ctx, empData, []employmentPb.EmploymentDataFieldMask{employmentPb.EmploymentDataFieldMask_EMPLOYMENT_INFO})
		if daoErr != nil {
			return nil, err
		}
		logger.Info(ctx, "Risk ops team verified income occupation for user")
		return response, err
	case request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_INAPP,
		request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_REG_EMPLOYER_STAGE_SCRIPT,
		request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_ONB_STAGE_UPDATE_CONSUMER,
		request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS:
		if request.GetEmploymentType() != employmentPb.EmploymentType_SALARIED {
			return &employmentPb.UpdateNewEmploymentDataResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("employment type can only be salaried"),
			}, nil
		}
		if request.GetEmployerInfo().GetEmployerId() == empData.GetEmployerId() {
			return response, nil
		}
		// Publish packet to update employment flow
		if request.GetEmployerInfo().GetEmployerId() != "" &&
			request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS {
			// Employer id non-empty ensures that the employer is valid. If db allows for non-verified employer udpate this.
			updateReq := &employmentPb.ProcessEmploymentUpdateEventRequest{
				ActorId: request.GetActorId(),
				EmployerInfo: &employmentPb.UpdatedEmployerInfo{
					Id:         request.GetEmployerInfo().GetEmployerId(),
					IsVerified: true,
				},
			}
			_, err = s.UpdateEmploymentPublisher.Publish(ctx, updateReq)
			if err != nil {
				logger.Error(ctx, "failed to publish packet to update employment details", zap.Error(err))
				return &employmentPb.UpdateNewEmploymentDataResponse{
					Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("failed to publish packet to update employment details, %v", err.Error())),
				}, nil
			}
		}
	case request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_USER_PROFILE,
		request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_HOME_TOP_NOTICE_BAR:
		// Employment type, occupation , Annual income changes, or employer changes - re-create employment row
		if empData.GetEmploymentType() == request.GetEmploymentType() &&
			empData.GetOccupationType() == request.GetOccupationType() &&
			empData.GetEmploymentInfo().GetAnnualSalary().GetAbsolute() == request.GetAnnualSalary().GetAbsolute() &&
			empData.GetEmployerId() == request.GetEmployerInfo().GetEmployerId() {
			logger.Debug(ctx, fmt.Sprintf("Skipping employment update since params have not changed, %v, %v, %v", request.GetEmploymentType(),
				request.GetAnnualSalary().GetAbsolute(), request.GetEmployerInfo().GetEmployerId()))
			return response, nil
		}
		logger.Debug(ctx, fmt.Sprintf("Updating employment since params have changed, %v, %v, %v", request.GetEmploymentType(),
			request.GetAnnualSalary().GetAbsolute(), request.GetEmployerInfo().GetEmployerId()))
	case request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_DEV_ACTION:
		if empData.GetEmploymentType() == request.GetEmploymentType() &&
			empData.GetEmploymentInfo().GetAnnualSalary().GetAbsolute() == request.GetAnnualSalary().GetAbsolute() {
			logger.Debug(ctx, fmt.Sprintf("Skipping employment update since params have not changed, %v, %v", request.GetEmploymentType(),
				request.GetAnnualSalary().GetAbsolute()))
			return response, nil
		}
		logger.Debug(ctx, fmt.Sprintf("Updating employment since params have changed, %v, %v", request.GetEmploymentType(),
			request.GetAnnualSalary().GetAbsolute()))
	// All the following cases are for VKYC employer update flows.
	// If the employment type and income did not change, we will return without update
	case empData.GetEmploymentType() == request.GetEmploymentType() &&
		empData.GetEmploymentInfo().GetAnnualSalary().GetAbsolute() == request.GetAnnualSalary().GetAbsolute():
		return response, nil
	case empData.EmploymentType != request.GetEmploymentType():
		break
	case empData.GetEmploymentInfo().GetAnnualSalary().GetAbsolute() != request.GetAnnualSalary().GetAbsolute() && request.GetAnnualSalary() != nil:
		if empData.GetEmploymentInfo() == nil {
			empData.EmploymentInfo = &employmentPb.EmploymentInfo{}
		}
		if empData.GetEmploymentInfo().AnnualSalary == nil {
			empData.GetEmploymentInfo().AnnualSalary = &screening.AnnualSalary{}
		}
		empData.GetEmploymentInfo().GetAnnualSalary().Absolute = request.GetAnnualSalary().GetAbsolute()
		daoErr := s.EmploymentDataDao.Update(ctx, empData, []employmentPb.EmploymentDataFieldMask{employmentPb.EmploymentDataFieldMask_EMPLOYMENT_INFO})
		if daoErr != nil {
			return nil, err
		}
		return response, err
	}

	if txnErr := storagev2.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(ctx context.Context) error {

		// When new row is created and annual income was not provided, we will update with the old value
		if newEmploymentData.GetEmploymentInfo().GetAnnualSalary() == nil {
			if newEmploymentData.GetEmploymentInfo() == nil {
				newEmploymentData.EmploymentInfo = &employmentPb.EmploymentInfo{}
			}
			newEmploymentData.EmploymentInfo.AnnualSalary = empData.GetEmploymentInfo().GetAnnualSalary()
		}

		// if occupation is not mentioned in request, we carry forward the old one
		if newEmploymentData.GetOccupationType() == employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
			newEmploymentData.OccupationType = empData.GetOccupationType()
		}

		// if employment info is nil we will carry forward the old one
		if newEmploymentData.GetEmploymentInfo() == nil {
			newEmploymentData.EmploymentInfo = empData.GetEmploymentInfo()
		}

		// if employment type is nil we will carry forward the old one
		if newEmploymentData.GetEmploymentType() == employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
			newEmploymentData.EmploymentType = empData.GetEmploymentType()
		}

		if err = s.EmplVerificationCheckDao.DeleteByActorId(ctx, actorId); err != nil {
			logger.Error(ctx, "failed to delete employment check data by actor id", zap.Error(err))
			return err
		}

		if err = s.EmplVerificationProcessDao.DeleteByActorId(ctx, actorId); err != nil {
			if !storagev2.IsRecordNotFoundError(err) {
				logger.Error(ctx, "failed to delete employment process data by actor id", zap.Error(err))
				return err
			}
		}

		if err = s.EmploymentDataDao.DeleteByActorId(ctx, actorId); err != nil {
			logger.Error(ctx, "failed to delete employment data by actor id", zap.Error(err))
			return err
		}

		if request.GetUpdateSource() == employmentPb.UpdateSource_UPDATE_SOURCE_DEV_ACTION {
			newEmploymentData.UpdatedBySource = request.GetUpdateSource()
			if errSalary := s.updateSalaryRangeInUser(ctx, actorId, request.AnnualSalary); errSalary != nil {
				return errSalary
			}
		}

		newEmploymentData, err = s.EmploymentDataDao.CreateEmploymentData(ctx, newEmploymentData)
		if err != nil {
			logger.Error(ctx, "error creating employment data entry", zap.Error(err))
			return err
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "txn error in updating employment data", zap.Error(txnErr))
		response.Status = rpc.StatusInternal()
		return response, nil
	}

	return response, nil
}

// isOccupationValidFromCtx returns true if occupation that needs to be collected along with employment is valid
func (s *Service) isOccupationValidFromCtx(ctx context.Context, employmentType employmentPb.EmploymentType, occupationType employmentPb.OccupationType, source employmentPb.UpdateSource) bool {
	switch {
	// If it's a B2B user we don't need to validate employment or occupation
	// because we initially create dummy employment data with just employment type
	case source == employmentPb.UpdateSource_UPDATE_SOURCE_B2B_ONBOARDING:
		return true
	// If Occupation collection is disabled from CTX then we can just check for employment
	case !s.isOccupationCollectionEnabledFromCtx(ctx, source):
		return true
	}
	// Fetch employment type object
	empTypeObject, err := s.getEmploymentObjectFromEmploymentType(employmentType)
	if err != nil {
		logger.Error(ctx, "error in getting employment object from employment type", zap.Error(err))
		return false
	}
	// Check if employment type object requires occupation
	if empTypeObject.RequiresOccupation {
		return employmentType != employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED && occupationType != employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED
	}
	// Else just return true
	return true
}

func (s *Service) isOccupationCollectionEnabledFromCtx(ctx context.Context, source employmentPb.UpdateSource) bool {
	// TODO (eswar): remove cx flows once occupation is added there (https://monorail.pointz.in/p/fi-app/issues/detail?id=41576)
	if source == employmentPb.UpdateSource_UPDATE_SOURCE_RISKOPS_ACTION {
		// not using IsFeatureEnabledFromCtxDynamic since dev actions do not populate either
		return !s.Config.CollectOccupation().DisableFeature()
	}
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.Config.CollectOccupation()) &&
		source != employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS &&
		source != employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_INAPP &&
		source != employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN &&
		source != employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_REG_EMPLOYER_STAGE_SCRIPT &&
		source != employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_ONB_STAGE_UPDATE_CONSUMER
}

// nolint: funlen, dupl
func (s *Service) getEmploymentDataUsers(req *employmentPb.UpdateNewEmploymentDataRequest, intent employmentPb.ProcessingIntent) *employmentPb.EmploymentData {
	var empData *employmentPb.EmploymentData
	switch req.GetEmploymentType() {
	case employmentPb.EmploymentType_SALARIED:
		switch req.GetEmploymentInfoOptions().(type) {
		case *employmentPb.UpdateNewEmploymentDataRequest_CompanyInfo:
			empData = &employmentPb.EmploymentData{
				ActorId:        req.ActorId,
				EmploymentType: req.EmploymentType,
				EmploymentInfo: &employmentPb.EmploymentInfo{
					CompanyName:              req.GetCompanyInfo().GetCompanyName(),
					VendorId:                 req.GetCompanyInfo().GetVendorId(),
					IsEpfRegistered:          req.GetCompanyInfo().GetIsEpfRegistered(),
					IsCompanyNameManualInput: req.GetCompanyInfo().GetIsManualInput(),
					EnteredText:              req.GetCompanyInfo().GetEnteredText(),
				},
			}
		case *employmentPb.UpdateNewEmploymentDataRequest_EmployerInfo:
			empData = &employmentPb.EmploymentData{
				ActorId:        req.ActorId,
				EmploymentType: req.EmploymentType,
				EmploymentInfo: &employmentPb.EmploymentInfo{
					EnteredText: req.GetEmployerInfo().GetEnteredText(),
				},
				EmployerId: req.GetEmployerInfo().GetEmployerId(),
			}
		default:
			// In all other cases we just want to populate the employment type
			empData = &employmentPb.EmploymentData{
				ActorId:        req.ActorId,
				EmploymentType: req.EmploymentType,
			}
		}

	case employmentPb.EmploymentType_BUSINESS_OWNER:
		empData = &employmentPb.EmploymentData{
			ActorId:        req.ActorId,
			EmploymentType: req.EmploymentType,
			EmploymentInfo: &employmentPb.EmploymentInfo{
				GstinNo: req.GetBusinessOwnerOption().GetGstinNo(),
			},
		}
	case employmentPb.EmploymentType_WORKING_PROFESSIONAL:
		empData = &employmentPb.EmploymentData{
			ActorId:        req.ActorId,
			EmploymentType: req.EmploymentType,
			EmploymentInfo: &employmentPb.EmploymentInfo{
				EnrollmentNo: req.GetEnrollmentNumberOption().GetEnrollmentNo(),
			},
		}
	case employmentPb.EmploymentType_FREELANCER:
		empData = &employmentPb.EmploymentData{
			ActorId:        req.ActorId,
			EmploymentType: req.EmploymentType,
			EmploymentInfo: &employmentPb.EmploymentInfo{
				PersonalProfileInfo: req.GetPersonalProfileInfo().GetPersonalProfileInfo(),
			},
		}
	case employmentPb.EmploymentType_HOMEMAKER:
		empData = &employmentPb.EmploymentData{
			ActorId:        req.ActorId,
			EmploymentType: req.EmploymentType,
		}
	case employmentPb.EmploymentType_STUDENT:
		empData = &employmentPb.EmploymentData{
			ActorId:        req.ActorId,
			EmploymentType: req.EmploymentType,
			EmploymentInfo: &employmentPb.EmploymentInfo{
				StudentGraduationYear: fmt.Sprintf("%v", req.GetStudentDetailsOption().GetYear()),
				StudentMailId:         req.GetStudentDetailsOption().GetMailId(),
			},
		}
	default:
		empData = &employmentPb.EmploymentData{
			ActorId:        req.ActorId,
			EmploymentType: req.EmploymentType,
			EmploymentInfo: &employmentPb.EmploymentInfo{
				PersonalProfileInfo: req.GetPersonalProfileInfo().GetPersonalProfileInfo(),
			},
		}
	}

	if empData == nil {
		empData = &employmentPb.EmploymentData{}
	}
	if empData.GetEmploymentInfo() == nil {
		empData.EmploymentInfo = &employmentPb.EmploymentInfo{}
	}
	empData.GetEmploymentInfo().AnnualSalary = req.GetAnnualSalary()
	empData.ProcessingIntent = intent
	empData.OccupationType = req.GetOccupationType()
	empData.UpdatedBySource = req.GetUpdateSource()
	return empData
}

// UpdateAnnualSalary RPC is used to update annual income only in employment info
func (s *Service) UpdateAnnualSalary(ctx context.Context, request *employmentPb.UpdateAnnualSalaryRequest) (*employmentPb.UpdateAnnualSalaryResponse, error) {
	var (
		res = &employmentPb.UpdateAnnualSalaryResponse{Status: rpc.StatusOk()}
	)
	if err := storagev2.RunCRDBTxn(ctx, func(ctx context.Context) error {
		empData, err := s.EmploymentDataDao.GetByActorId(ctx, request.GetActorId())
		if err != nil {
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				// return err in case of record not found also
				logger.Error(ctx, "Error in fetch employment data", zap.Error(err))
				return err
			}
			// create employment data. Old users who onboarded before introduction of employment data collection would not have an entry in employment_data table.
			// We will create entry here for those users and not
			employmentData := &employmentPb.EmploymentData{ActorId: request.GetActorId()}
			var err1 error
			empData, err1 = s.EmploymentDataDao.CreateEmploymentData(ctx, employmentData)
			if err1 != nil {
				logger.Error(ctx, "failed to create employment data", zap.Error(err1))
				return err1
			}
		}
		if empData.GetEmploymentInfo() == nil {
			empData.EmploymentInfo = &employmentPb.EmploymentInfo{}
		}
		// update annual salary keeping other fields same
		empData.GetEmploymentInfo().AnnualSalary = request.GetAnnualSalary()
		daoErr := s.EmploymentDataDao.Update(ctx, empData, []employmentPb.EmploymentDataFieldMask{employmentPb.EmploymentDataFieldMask_EMPLOYMENT_INFO})
		if daoErr != nil {
			logger.Error(ctx, "Error in updating annual income", zap.Error(err))
			return err
		}
		return nil
	}); err != nil {
		logger.Error(ctx, "txn error in updating employment data", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	return res, nil
}

func (s *Service) GetEmploymentDeclarationDL(ctx context.Context, req *employmentPb.GetEmploymentDeclarationDLRequest) (*employmentPb.GetEmploymentDeclarationDLResponse, error) {
	// TODO(rishu): move pkg impl to employment service (https://monorail.pointz.in/p/fi-app/issues/detail?id=35989)
	var age = s.getUserAge(ctx, req.GetActorId())
	nrResp, nrErr := s.UsersClient.IsNonResidentUser(ctx, &usersPb.IsNonResidentUserRequest{
		Identifier: &usersPb.IsNonResidentUserRequest_ActorId{
			ActorId: req.GetActorId(),
		},
	})
	if err := epifigrpc.RPCError(nrResp, nrErr); err != nil {
		logger.Error(ctx, "error while getting nr status of user", zap.Error(err))
		return &employmentPb.GetEmploymentDeclarationDLResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	isNrUser := nrResp.GetIsNonResidentUser() == commontypes.BooleanEnum_TRUE
	countryCode := getCountryCodeFromNrStatus(isNrUser, nrResp.GetResidentCountryCode())

	empDlGen := pkgDl.EmploymentDeclarationDLGen(ctx, &pkgDl.EmploymentDeclarationDLGenRequest{
		TypeConf:              s.getTypeUiViewFromCountryCode(countryCode),
		ProofConf:             s.Config.EmploymentProofUiView(),
		AnnualSalaryUIView:    s.getAnnualSalaryListUiViewFromCountryCode(countryCode),
		AnnualSalaryUIView2:   s.Config.AnnualSalaryUIView2(),
		IncomeDiscrepancyView: s.Config.IncomeDiscrepancyView(),
		Source:                req.GetUpdateSource(),
		OccViewConfig:         s.Config.OccupationTypeUiView(),
		CollectOccupation:     s.isOccupationCollectionEnabledFromCtx(ctx, req.GetUpdateSource()),
		Age:                   age,
		EnableSaNewFields:     s.Config.EnableSaNewFields(),
	})
	empUpdateDL, empErr := empDlGen()
	if empErr != nil {
		logger.Error(ctx, "Error while generate employment deeplink", zap.Error(empErr), zap.String(logger.ONBOARDING_STAGE, req.GetUpdateSource().String()))
		return &employmentPb.GetEmploymentDeclarationDLResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	if req.GetScreenView() == deeplink.EmploymentDeclarationOptions_EMPLOYMENT_SCREEN_VIEW_UNSPECIFIED {
		logger.Info(ctx, "employment screen option is unspecified")
		req.ScreenView = deeplink.EmploymentDeclarationOptions_BOTTOM_SHEET
	}
	empUpdateDL.GetEmploymentDeclarationOptions().ScreenView = req.GetScreenView()
	editable, err := s.isEmploymentDetailsEditable(ctx, req.GetActorId(), isNrUser)
	if err != nil {
		return &employmentPb.GetEmploymentDeclarationDLResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &employmentPb.GetEmploymentDeclarationDLResponse{
		Status:        rpc.StatusOk(),
		Deeplink:      empUpdateDL,
		IsNotEditable: !editable,
	}, nil
}

func getCountryCodeFromNrStatus(isNrUser bool, residentCountryCode types.CountryCode) types.CountryCode {
	switch {
	case isNrUser:
		return residentCountryCode
	default:
		return types.CountryCode_COUNTRY_CODE_IND
	}
}

func (s *Service) getAnnualSalaryListUiViewFromCountryCode(country types.CountryCode) *pkgDl.AnnualSalaryUIView {
	switch country {
	case types.CountryCode_COUNTRY_CODE_ARE:
		return s.Config.AnnualSalaryUIViewAED()
	case types.CountryCode_COUNTRY_CODE_QAT:
		return s.Config.AnnualSalaryUIViewQAR()
	default:
		return s.Config.AnnualSalaryUIView()
	}
}

func (s *Service) getTypeUiViewFromCountryCode(country types.CountryCode) *pkgDl.EmploymentTypeUiView {
	switch country {
	case types.CountryCode_COUNTRY_CODE_ARE:
		return s.Config.EmploymentTypeUiViewAED()
	case types.CountryCode_COUNTRY_CODE_QAT:
		return s.Config.EmploymentTypeUiViewQAR()
	default:
		return s.Config.EmploymentTypeUiView()
	}
}

func (s *Service) getUserAge(ctx context.Context, actorId string) int {
	userDetail, userErr := s.UsersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(userDetail, userErr); te != nil {
		// not returning error since age helps to take decision in occupation list
		// for error case we can take whatever occupation list we have for age 0 as per employment type
		logger.Error(ctx, "Error while fetching actor id", zap.Error(te))
		return 0
	}
	dob := userDetail.GetUser().GetProfile().GetDateOfBirth()
	if dob != nil {
		return datetime.Age(time.Date(int(dob.GetYear()), time.Month(dob.GetMonth()), int(dob.GetDay()), 0, 0, 0, 0, time.Local))
	}
	return 0
}

func (s *Service) isEmploymentDetailsEditable(ctx context.Context, actorId string, isNrUser bool) (bool, error) {
	id, err := s.EmploymentDataDao.GetByActorId(ctx, actorId)
	if storagev2.IsRecordNotFoundError(err) {
		return true, nil
	}
	if err != nil {
		logger.Error(ctx, "error while getting employment details for actor", zap.Error(err))
		return false, err
	}
	config := s.Config.EmploymentDetailsUpdateConfig()
	// employment details is editable if:
	// 1. flag is enabled, and
	// 2. "do not update" time has passed
	// employment details is not editable if:
	// 1. user is of NR type
	return config.IsEnabled() && time.Since(id.GetCreatedAt().AsTime()) > config.PermissibleUpdateInterval() && !isNrUser, nil
}

func (s *Service) GetEmployerPiMappingByPiId(ctx context.Context, req *employmentPb.GetEmployerPiMappingByPiIdRequest) (*employmentPb.GetEmployerPiMappingByPiIdResponse, error) {
	employerPiMapping, err := s.EmployerPiMappingDao.GetByPiId(ctx, req.GetPiId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &employmentPb.GetEmployerPiMappingByPiIdResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching employer pi mapping from db", zap.String(logger.PI_ID, req.GetPiId()), zap.Error(err))
		return &employmentPb.GetEmployerPiMappingByPiIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error fetching employer pi mapping from db, err: %s", err.Error())),
		}, nil
	}

	return &employmentPb.GetEmployerPiMappingByPiIdResponse{
		Status:            rpc.StatusOk(),
		EmployerPiMapping: employerPiMapping,
	}, nil
}

func (s *Service) CreateEmployerPiMapping(ctx context.Context, req *employmentPb.CreateEmployerPiMappingRequest) (*employmentPb.CreateEmployerPiMappingResponse, error) {
	err := s.EmployerPiMappingDao.CreateMapping(ctx, &employmentPb.EmployerPiMapping{
		EmployerId: req.GetEmployerId(),
		PiId:       req.GetPiId(),
		Source:     req.GetSource(),
	})
	if err != nil {
		logger.Error(ctx, "error creating employerId - piId mapping in db", zap.String(logger.EMPLOYER_ID, req.GetEmployerId()),
			zap.String(logger.PI_ID, req.GetPiId()), zap.Error(err))
		return &employmentPb.CreateEmployerPiMappingResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error creating employerId - piId mapping in db, err: %s", err.Error())),
		}, nil
	}
	s.publishEmployerPiMappingEvent(ctx, req.GetPiId(), req.GetEmployerId())
	return &employmentPb.CreateEmployerPiMappingResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) publishEmployerPiMappingEvent(ctx context.Context, piId string, employerId string) {
	messageId, err := s.EmployerPiMappingUpdateEventSqsPublisher.Publish(ctx, &employmentEvents.EmployerPiMappingCreationEvent{
		PiId:       piId,
		EmployerId: employerId,
	})
	if err != nil {
		logger.Error(ctx, "error in publishing employer pi mapping event", zap.Error(err), zap.String(logger.PI_ID, piId), zap.String(logger.EMPLOYER_ID, employerId))
		return
	}
	logger.Info(ctx, "successfully published employer pi mapping update event", zap.String(logger.PI_ID, piId), zap.String(logger.EMPLOYER_ID, employerId), zap.String(logger.QUEUE_MESSAGE_ID, messageId))
}

func (s *Service) publishIncomeUpdateEvent(ctx context.Context, updateSource employmentPb.UpdateSource, oldEmpData *employmentPb.EmploymentData, newEmpData *employmentPb.EmploymentData) error {
	// if source is profile and new income is different form old one
	if !(updateSource == employmentPb.UpdateSource_UPDATE_SOURCE_USER_PROFILE ||
		updateSource == employmentPb.UpdateSource_UPDATE_SOURCE_HOME_TOP_NOTICE_BAR) ||
		proto.Equal(oldEmpData.GetEmploymentInfo().GetAnnualSalary(), newEmpData.GetEmploymentInfo().GetAnnualSalary()) {
		logger.Info(ctx, "since income of user doesn't change we won't publish packet")
		return nil
	}
	msgId, err := s.IncomeUpdatePublisher.Publish(ctx, &consumerPb.IncomeUpdateEvent{
		ActorId:         newEmpData.GetActorId(),
		IncomeUpdatedAt: timestamppb.Now(),
		AnnualSalary:    newEmpData.GetEmploymentInfo().GetAnnualSalary(),
	})
	if err != nil {
		logger.Error(ctx, "error in publishing the packet for income update", zap.String(logger.QUEUE_MESSAGE_ID, msgId), zap.Error(err))
		return err
	}
	logger.Info(ctx, "published the event for income update")
	return nil
}

func (s *Service) UpdateEmployerDetails(ctx context.Context, req *employmentPb.UpdateEmployerDetailsRequest) (*employmentPb.UpdateEmployerDetailsResponse, error) {
	// Validate employer ID
	if req.GetEmployerData().GetId() == "" {
		return &employmentPb.UpdateEmployerDetailsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("Employer ID cannot be empty"),
		}, nil
	}

	// Directly attempt to update the employer data
	updatedEmployer, err := s.EmployerDao.Update(ctx, req.GetEmployerData(), req.GetColumnsFieldMasks())
	if err != nil {
		// Handle specific error cases
		switch {
		case errors.Is(err, dao.EmptyEmployerIdError):
			return &employmentPb.UpdateEmployerDetailsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("Employer ID cannot be empty"),
			}, nil
		case errors.Is(err, dao.EmptyFieldMask):
			return &employmentPb.UpdateEmployerDetailsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("At least one field must be specified for update"),
			}, nil
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &employmentPb.UpdateEmployerDetailsResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(fmt.Sprintf("Employer with ID '%s' not found", req.GetEmployerData().GetId())),
			}, nil
		default:
			logger.Error(ctx, "failed to update employer data", zap.Error(err), zap.String("employerId", req.GetEmployerData().GetId()))
			return &employmentPb.UpdateEmployerDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("Failed to update employer data: %s", err.Error())),
			}, nil
		}
	}

	return &employmentPb.UpdateEmployerDetailsResponse{
		Status:              rpc.StatusOk(),
		UpdatedEmployerData: updatedEmployer,
	}, nil
}

// nolint: funlen
func (s *Service) FetchDynamicElements(ctx context.Context, req *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	if !s.Config.EnableHomeUpdateEmploymentNoticeBar() {
		logger.Debug(ctx, "flag to enable notice bar to update income is disabled")
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	temp, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dePb.ClientContext_HomeInfo)
	if !ok {
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if temp.HomeInfo.GetSection() != dePb.HomeScreenAdditionalInfo_SECTION_TOP_BAR {
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	// TODO (Rishu Sahu): when employment data is cached then check for employment first then user group
	isEligible, err := s.checkIfUserIsEligibleForNoticeBar(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in checking user group for actor", zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	if !isEligible {
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	logger.Debug(ctx, "flag to enable notice bar to update income is enabled")
	empData, err := s.EmploymentDataDao.GetByActorId(ctx, req.GetActorId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in getting employment info from db", zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	if !isAnnualSalaryEmpty(empData.GetEmploymentInfo().GetAnnualSalary()) {
		logger.Debug(ctx, "user already have employment details")
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	logger.Debug(ctx, "showing income update notice bar to user")
	empDLResp, err := s.GetEmploymentDeclarationDL(ctx, &employmentPb.GetEmploymentDeclarationDLRequest{
		UpdateSource: employmentPb.UpdateSource_UPDATE_SOURCE_HOME_TOP_NOTICE_BAR,
		ScreenView:   deeplink.EmploymentDeclarationOptions_FULL_SCREEN,
		ActorId:      req.GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(empDLResp, err); rpcErr != nil {
		logger.Error(ctx, "Error in getting employment update deeplink", zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	dynEleArr := make([]*dePb.DynamicElement, 0)

	dynamicElement := &dePb.DynamicElement{
		OwnerService:  types.ServiceName_EMPLOYMENT_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_BannerV2{
				BannerV2: &dePb.BannerElementContentV2{
					Title:           commontypes.GetTextFromStringFontColourFontStyle("Your employment details require a periodic review. Please update it now.", "#FFFFFF", commontypes.FontStyle_SUBTITLE_S),
					BackgroundColor: ui.GetBackgroundColor("#AC7C44"),
					Deeplink:        empDLResp.GetDeeplink(),
					Image: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/onboarding/vkyc_warning.png",
					},
				},
			},
		},
		BizAnalyticsData: map[string]string{campaignNameField: updateEmploymentCampaignName},
	}
	dynEleArr = append(dynEleArr, dynamicElement)

	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: dynEleArr,
	}, nil
}

func (s *Service) checkIfUserIsEligibleForNoticeBar(ctx context.Context, actorId string) (bool, error) {
	featureDetailsResp, err := s.OnboardingClient.GetFeatureDetails(ctx, &onPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onPb.Feature_FEATURE_FI_LITE,
	})
	if rpcErr := epifigrpc.RPCError(featureDetailsResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get feature details for actor", zap.Error(rpcErr))
		return false, rpcErr
	}
	if featureDetailsResp.GetIsFiLiteUser() {
		return false, nil
	}
	return true, nil
}

func (s *Service) DynamicElementCallback(_ context.Context, _ *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	// returning a no-op okay since we don't want to register callbacks
	return &dePb.DynamicElementCallbackResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func isAnnualSalaryEmpty(annualSalary *screening.AnnualSalary) bool {
	if annualSalary == nil {
		return true
	}
	annualSal := &screening.AnnualSalary{}
	return proto.Equal(annualSal, annualSalary)
}
