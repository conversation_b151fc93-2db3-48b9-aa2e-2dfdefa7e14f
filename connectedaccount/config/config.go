package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

const (
	RudderWriteKey                     = "RudderWriteKey"
	External                           = "EXTERNAL"
	ConnectedAccountDbUserNamePassword = "ConnectedAccountDbUserNamePassword"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.CONNECTED_ACC_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = readAndSetEnv(conf)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read and set env")
	}

	if onlyStaticFiles {
		return conf, nil
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, conf.ConnectedAccountDb, conf.FeatureEngineeringDb)
	if err != nil {
		return nil, errors.Wrap(err, "error while LoadSecretsAndPrepareDBConfig")
	}
	return conf, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// Constraints with respect to dynamic config generation tool:
// 1. Struct names have to start with uppercase letters
// 2. Struct variables need to be pointers
//
//go:generate conf_gen github.com/epifi/gamma/connectedaccount/config Config
type Config struct {
	Application                               *Application
	Logging                                   *cfg.Logging
	Server                                    *Server
	DBConfigMapForAnalytics                   cfg.DbConfigMap
	ConnectedAccountDb                        *cfg.DB
	FeatureEngineeringDb                      *cfg.DB
	AWS                                       *cfg.AWS
	ProcessConsentSqsPublisher                *cfg.SqsPublisher
	ProcessConsentSqsDelayPublisher           *cfg.SqsPublisher
	FetchDataDelaySqsPublisher                *cfg.SqsPublisher
	DecryptDataSqsPublisher                   *cfg.ExtendedSqsPublisher
	ProcessDataSqsPublisher                   *cfg.ExtendedSqsPublisher
	TransactionBatchProcessPublisher          *cfg.SqsPublisher
	PurgeDataPublisher                        *cfg.SqsPublisher
	TransactionEventExternalPublisher         *cfg.SnsPublisher
	AccountDataSyncExternalPublisher          *cfg.SnsPublisher
	AccountUpdateEventExternalPublisher       *cfg.SnsPublisher
	CreateAttemptSqsPublisher                 *cfg.SqsPublisher
	ProcessConsentCallbackSqsSubscriber       *cfg.SqsSubscriber         `dynamic:"true"`
	ProcessConsentSqsSubscriber               *cfg.SqsSubscriber         `dynamic:"true"`
	FetchDataSqsSubscriber                    *cfg.SqsSubscriber         `dynamic:"true"`
	DecryptDataSqsSubscriber                  *cfg.ExtendedSqsSubscriber `dynamic:"true"`
	ProcessAccountStatusCallbackSqsSubscriber *cfg.SqsSubscriber         `dynamic:"true"`
	ProcessDataSqsSubscriber                  *cfg.ExtendedSqsSubscriber `dynamic:"true"`
	ProcessFICallbackSqsSubscriber            *cfg.SqsSubscriber         `dynamic:"true"`
	TransactionBatchProcessSubscriber         *cfg.SqsSubscriber         `dynamic:"true"`
	PurgeDataSubscriber                       *cfg.SqsSubscriber         `dynamic:"true"`
	CreateAttemptSqsSubscriber                *cfg.SqsSubscriber         `dynamic:"true"`
	Flags                                     *Flags                     `dynamic:"true"`
	RudderStack                               *cfg.RudderStackBroker
	Secrets                                   *cfg.Secrets
	Consent                                   *Consent      `dynamic:"true"`
	NextFetchInterval                         time.Duration `dynamic:"true"`
	DataFetchDelayMinutes                     time.Duration `dynamic:"true"`
	ConsentProcessDelay                       time.Duration `dynamic:"true"`
	BatchSize                                 int32         `dynamic:"true"`
	BatchProcessDelayMultiplier               time.Duration `dynamic:"true"`
	FiDataRangeOverlapInterval                time.Duration `dynamic:"true"`
	NextFetchIntervalFailureCase              time.Duration `dynamic:"true"`
	UserDataRefreshThreshold                  time.Duration `dynamic:"true"`
	AttemptCompletionThreshold                time.Duration `dynamic:"true"`
	AccountSyncCompletionThreshold            time.Duration `dynamic:"true"`
	AccountSyncCompletionPollFrequency        time.Duration `dynamic:"true"`
	SecureLogging                             *SecureLogging
	PermittedFip                              []string
	// fipId to userGroup to fiType to struct of minVersion and Percentage to rollout for
	PermittedFipAndFi                                map[string]map[string]map[string]*PercentageRollout
	AaNotifications                                  []*Parameters
	SendNotificationSqsSubscriber                    *cfg.SqsSubscriber `dynamic:"true"`
	SendNotificationSqsPublisher                     *cfg.SqsPublisher
	FetchDataSqsPublisher                            *cfg.SqsPublisher
	AaTxnBackfillPublisher                           *cfg.SnsPublisher
	ConsentDataRequestCountLimit                     int `dynamic:"true"`
	Tracing                                          *cfg.Tracing
	Profiling                                        *cfg.Profiling
	ProcessConsentDataRefreshSqsPublisher            *cfg.SqsPublisher
	ProcessConsentDataRefreshSqsSubscriber           *cfg.SqsSubscriber `dynamic:"true"`
	FipCentralIfscCodes                              *FipCentralIfscCodes
	FipLevelControl                                  map[string]*FipLevelControl
	CaptureColumnUpdateSqsPublisher                  *cfg.SqsPublisher
	CaptureColumnUpdateSqsSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	CaptureHeartbeatAndSendNotificationSqsPublisher  *cfg.SqsPublisher
	CaptureHeartbeatAndSendNotificationSqsSubscriber *cfg.SqsSubscriber                        `dynamic:"true"`
	MinAndroidVersionEligibleForAaRouting            uint32                                    `dynamic:"true"`
	MinIosVersionEligibleForAaRouting                uint32                                    `dynamic:"true"`
	UgToPlatformToAaEntityToBoundsMap                map[string]*PlatformToAaEntityToBoundsMap `dynamic:"true"`
	PlatformToEnabledAaEntitiesMap                   map[string][]string
	// minimum time to wait before consuming packets from
	// user heartbeats queue
	PublishedAaHeartbeatEventsConsumptionThreshold time.Duration `dynamic:"true"`
	FipMetaMap                                     map[string]*FipMeta
	JupiterMetaData                                *JupiterMetaData `dynamic:"true"`

	FirstDataPullDurationInDays int `dynamic:"true"`
	// Number of months to fetch data for salary estimation flow instead of FirstDataPullDurationInDays
	SalaryEstimationDataRangeMonths int `dynamic:"true"`
	// Number of years to fetch Indian Stocks data, separating Indian Stocks as we can pull maximum of 20 years of data.
	IndianStocksDataRangeYears int `dynamic:"true"`

	// aa entity to fip mapping for enabled fips, discovery will be disabled for based on each aa_entity mapping
	// and data flow will be disbaled for fips which are not present in either of them
	AaToFipMapping map[string][]string
	MaxPageSize    int `dynamic:"true"`
	// PageSize used to fetch transaction using pagination
	PageSize            uint32 `dynamic:"true"`
	MaxTransactionCount int    `dynamic:"true"`
	// FiuId represents the FIU ID required for sending the SDK deeplink
	FiuId string `dynamic:"true"`
	// V2FlowParams consist of params that decides whether the V2 Flow should be enabled or not depending on either absolute flag or app version
	V2FlowParams *V2FlowParams `dynamic:"true"`
	// LegalDocuments consists of fields for each AA required to show the TnC URLs
	LegalDocuments *LegalDocuments
	// FeatureReleaseConfig represents the configs to have control at service level
	FeatureReleaseConfig *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	// FirstDataPullDelaySqsPublisher represents delay publisher for first data pull queue
	FirstDataPullSqsPublisher *cfg.SqsPublisher
	// FirstDataPullSqsSubscriber represents subscriber for first data pull queue
	FirstDataPullSqsSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	// ProcessAuthTokenCreationEventSqsSubscriber represents subscriber for auth token creation event
	ProcessAuthTokenCreationEventSqsSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	USStocksParams                             *USStocksParams    `dynamic:"true"`
	// PermittedFIPsForTxnsBackfill represents list of all the FIPs for which txn backfill has to be performed and allowed
	PermittedFIPsForTxnsBackfill []string `dynamic:"true"`
	// NextTxnBackfillInterval represents Cool down period in between two backfill data refresh for a given consent
	NextTxnBackfillInterval time.Duration `dynamic:"true"`
	// TxnBackfillCutOffDate represents cut off date till which txn backfill has to be performed
	// This is common for all FIPs and consents associated
	TxnBackfillCutOffDate         string          `dynamic:"true"`
	AaAccountDaoCacheConfig       *DaoCacheConfig `dynamic:"true"`
	AaDepositFiDaoCacheConfig     *DaoCacheConfig `dynamic:"true"`
	AaTransactionDaoCacheConfig   *DaoCacheConfig `dynamic:"true"`
	RedisOptions                  *cfg.RedisOptions
	FinvuTokenExpiryDuration      time.Duration `dynamic:"true"`
	EnableBatchUpsertTransactions bool          `dynamic:"true"`
	// IsQueryParamsAllowedForFetchData is a boolean flag that represents if addition of query params is allowed to
	// fetch data VG API
	IsQueryParamsAllowedForFetchData bool `dynamic:"true"`
	// threshold to enable periodic data refresh reconciliation
	ConsentDataRefreshReconciliationThreshold time.Duration `dynamic:"true"`
	// FIPs with empty txn id
	EmptyTxnIdFipList []string
	// threshold to enable data fetch attempt status reconciliation
	DataFetchAttemptStatusReconciliationThreshold time.Duration `dynamic:"true"`
	ConsentRenewalThreshold                       time.Duration `dynamic:"true"`
	SegmentIds                                    *SegmentIds
	CATxnBackfillBucketName                       string            `iam:"s3-readwrite"`
	DocsBucketName                                string            `iam:"s3-readwrite"`
	QuestSdk                                      *sdkconfig.Config `dynamic:"true"`
	QuestRedisOptions                             *cfg.RedisOptions
	// adding this since we don't have any other quest enabled config. TODO[obed]: remove this when we add quest config
	DummyQuestVariable                       string `dynamic:"true" ,quest:"variable,area:ConnectedAccounts"`
	GrpcRatelimiterParams                    *cfg.GrpcRateLimiterParams
	PermittedFipConfig                       *PermittedFipConfig `dynamic:"true"`
	EnableAATxnDataUploadingToS3             bool                `dynamic:"true"`
	CaUserTxnDataBucketName                  string              `iam:"s3-readwrite"`
	FiDataRangeOverlapIntervalForCustomfetch time.Duration       `dynamic:"true"`
	// config to enable/disable the name check for ca flow
	EnableNameCheckForCADataImport     bool          `dynamic:"true"`
	CaAnalyticsBucketName              string        `iam:"s3-readwrite"`
	AuthTokenCreationLockLeaseDuration time.Duration `dynamic:"true"`
}

type DaoCacheConfig struct {
	IsCacheEnabled bool `dynamic:"true"`
	Prefix         string
	CacheTTL       time.Duration `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus             bool
	EnableConsentRenewalSegmentNonProdTest bool `dynamic:"true"`
}

type Consent struct {
	DataConsumerId      string        `dynamic:"true"`
	ConsentLifeDuration time.Duration `dynamic:"true"`
	Mode                string        `dynamic:"true"`
	FetchType           string        `dynamic:"true"`
	ConsentTypes        []string
	FITypes             []string
	DataRangeStartYears int        `dynamic:"true"`
	DataLife            *DataLife  `dynamic:"true"`
	Frequency           *Frequency `dynamic:"true"`
	Purpose             string     `dynamic:"true"`
	// DataRangeEndMonthValue specifies the number of months from the current date to set as the upper bound (DataRangeTo) for the consented data range.
	// This determines how far into the future data can be fetched or processed, but will not exceed the consent expiry date.
	DataRangeEndMonthValue int `dynamic:"true"`
}

type DataLife struct {
	Unit  string `dynamic:"true"`
	Value int32  `dynamic:"true"`
}

type Frequency struct {
	Unit  string `dynamic:"true"`
	Value int32  `dynamic:"true"`
}

type SecureLogging struct {
	EnableSecureLog bool
	SecureLogPath   string
	MaxSizeInMBs    int // megabytes
	MaxBackups      int // There will be MaxBackups + 1 total files
}

type Parameters struct {
	CommsType     string
	CommsChannels []*CommsChannel
}

type CommsChannel struct {
	Mode       string
	Enabled    bool
	Properties *Properties
}

type Properties struct {
	Type            string
	Title           string
	Body            string
	Template        string
	TemplateVersion string
}

type FipCentralIfscCodes struct {
	FipToIfscMap map[string]string
}

type FipLevelControl struct {
	IsBalanceEnabled             bool
	IsTxnsEnabled                bool
	NextFetchIntervalFailureCase time.Duration
	DataRangeStartDuration       time.Duration
}

type FipMeta struct {
	Id                           string
	Name                         string
	DisplayName                  string
	Bank                         string
	LogoUrl                      string
	OtpLength                    int32
	OtpPattern                   []string
	IsAccountLevelConsentEnabled bool
	IsPopular                    bool
	IssueType                    string
	// if preferred Aa entity is not enabled for PlatformToEnabledAaEntitiesMap, fallback to UgToPlatformToAaEntityToBoundsMap
	// if empty, fallback to UgToPlatformToAaEntityToBoundsMap to find the aa entity
	PreferredAaEntity string
	// Bank OTP header to identify specific bank otp
	// todo Add SMS Headers data for all FIPS in config,
	BankSmsHeader []string
	// ShowSkipButtonTimeOut tell timeout seconds after which client can show skip / resend otp to user
	ShowSkipButtonTimeout int32
	// FIP Account type like Bank Account, Demat Account, insurance Account
	FipAccountType string
}

type JupiterMetaData struct {
	IfscCode    string `dynamic:"true"`
	DisplayName string `dynamic:"true"`
	LogoUrl     string `dynamic:"true"`
}

type PercentageRollout struct {
	AndroidMinVersion uint32
	AndroidPercentage uint32
	IosMinVersion     uint32
	IosPercentage     uint32
}

type PlatformToAaEntityToBoundsMap struct {
	PlatformToAaEntityToBoundsMap map[string]*AaEntityToBoundsMap `dynamic:"true"`
}

type AaEntityToBoundsMap struct {
	AaEntityToBoundsMap map[string]*PercentBounds `dynamic:"true"`
}

type PercentBounds struct {
	LowerBound uint32 `dynamic:"true"`
	UpperBound uint32 `dynamic:"true"`
}

// V2FlowParams consist of params that decides whether the V2 Flow should be enabled or not depending on either absolute flag or app version
type V2FlowParams struct {
	UseV2Flow         bool   `dynamic:"true"`
	MinVersionAndroid uint32 `dynamic:"true"`
	MinVersionIos     uint32 `dynamic:"true"`
}

// LegalDocuments consists of fields for each AA required to show the TnC URLs
type LegalDocuments struct {
	AaOnemoneyTncUrl string
	AaFinvuTncUrl    string
}

type USStocksParams struct {
	// IdealDurationOfDataSyncCompletion represent ideal time for a connected account in which all the txn data should be fetched
	IdealDurationOfDataSyncCompletion time.Duration `dynamic:"true"`
}

type SegmentIds struct {
	ConsentRenewal string
}

// gets account controls for a particular fip
func (c *Config) GetAccountControlsForFip(fipId string) map[string]bool {
	accControls := make(map[string]bool)
	// if fip level control is defined in config, reset with defined values
	for _, accControl := range caPkg.AccControlsList {
		accControls[accControl.String()] = c.IsFipEligibleForAccountControl(fipId, accControl)
	}

	return accControls
}

func (c *Config) IsFipEligibleForAccountControl(fipId string, control caEnumPb.AccountControls) bool {
	fip, defined := c.FipLevelControl[fipId]
	if !defined {
		return true
	}
	switch control {
	case caEnumPb.AccountControls_ACCOUNT_CONTROLS_TRANSACTIONS_ENABLED:
		return fip.IsTxnsEnabled
	case caEnumPb.AccountControls_ACCOUNT_CONTROLS_BALANCE_ENABLED:
		return fip.IsBalanceEnabled
	default:
		return true
	}
}

// PermittedFipConfig represents the configuration of all the permitted FIPs for a specific CaFlow
// this is v2 for PermittedFipAndFi
type PermittedFipConfig struct {
	CaFlowBasedFipMapping map[string]*UgBasedFiTypeMapping `dynamic:"true"`
}

// UgBasedFiTypeMapping contains mapping of user group filter criteria for a FI type
type UgBasedFiTypeMapping struct {
	UgBasedFiTypeMapping map[string]*FiTypeBasedFipFilteringCriteriaMapping `dynamic:"true"`
}

// FiTypeBasedFipFilteringCriteriaMapping contains mapping of specific Fi type to all the filtering criteria for FIP to be permitted
type FiTypeBasedFipFilteringCriteriaMapping struct {
	FiTypeBasedFipFilteringCriteriaMapping map[string]*FipFilteringCriteria `dynamic:"true"`
}

// FipFilteringCriteria represents criteria on which FIP will get filter out:
//
//	first is FIP level app constraint and second is capability filters which is all enum from FipLevelControl served here as filters for FIP list
type FipFilteringCriteria struct {
	AllowedFipIdList     map[string]*ConstraintConfigsForFip `dynamic:"true"`
	FipCapabilityFilters []string                            `dynamic:"true"`
}
type ConstraintConfigsForFip struct {
	ConstraintConfigByPlatform map[string]*AppPlatformConstraint `dynamic:"true"`
}

type AppPlatformConstraint struct {
	MinVersion        uint32 `dynamic:"true"`
	RolloutPercentage uint32 `dynamic:"true"`
}
