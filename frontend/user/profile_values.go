package user

import (
	"fmt"

	gmoney "google.golang.org/genproto/googleapis/type/money"

	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/frontend/tiering/helper"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
)

var (
	AddFundsDeeplink = func() *deeplinkPb.Deeplink {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_TRANSFER_IN,
		}
	}

	DisplayNameWTieringFontColor  = "#FFFFFF" // Snow
	DisplayPhoneWTieringFontColor = "#FFFFFF" // Snow

	DisplayNameFontColorFilite  = "#FFFFFF" // Snow
	DisplayPhoneFontColorFilite = "#FFFFFF" // Snow

	AccountInfoColorRegularProfileScreen = "#1F1F1F" // Gray/Night

	BgColorsRegular             = []string{"#313234", "#313234"}
	BgColorBasic                = "#333333" // Gray/Night
	BgColorsPlus                = []string{"#C0B2A3", "#752A1C"}
	BgColorsInfinite            = []string{"#AEB9C2", "#5A6E81"}
	BgColorsSalary              = []string{"#CBC1A6", "#9C7F44"}
	BgColorsSalaryLite          = []string{"#CBC1A6", "#9C7F44"}
	BgColorsAaSalary            = []string{"#A6D9E9", "#0054BE"}
	BgColorGradientRadius int32 = 700

	UpiDisabledWTieringFontColor            = "#A73F4B" // Colour/Tertiary Peach
	UpiDisabledWTieringBgColor              = "#FAD0D0" // Colour/Pastel Peach
	UpiDisabledText                         = "VPA disabled. Enable to use."
	UpiDisabledContainerCornerRadius  int32 = 12
	UpiDisabledContainerHeight        int32 = 0
	UpiDisabledContainerWidth         int32 = 0
	UpiDisabledContainerPaddingTop    int32 = 9
	UpiDisabledContainerPaddingRight  int32 = 10
	UpiDisabledContainerPaddingBottom int32 = 9
	UpiDisabledContainerPaddingLeft   int32 = 10

	BgColorLinearGradientDegreeANDROID int32 = 90
	BgColorLinearGradientDegreeIOS     int32 = 0
	infoTextPitchPlus                        = "Rewards worth up to ₹1,200/year waiting!"
	infoTextPitchInfinite                    = "Rewards worth up to ₹6,000/year waiting!"
	infoTextPitchPrime                       = "Rewards worth up to ₹12,000/year waiting!"

	UpiEnabledLeftText = "UPI ID"
	UpiLabel           = "UPI"
	colorSweetPink     = "#F09696"
	colorFiGreen       = "#00B899"
	colorWhite         = "#FFFFFF"
	// colorInk                          = "#18191B"
	colorLightBrown = "#FFE9DD"
	colorDarkBrown  = "#9E6852"
	colorLightChalk = "#FFFFFF"
	colorDarkChalk  = "#AAB5C2"
	colorLightBlue  = "#BFE4FF"
	colorDarkBlue   = "#307EC6"
	// AMB specific colors
	colorAmbBg     = "#E795AE" // Cherry 200
	colorAmbInfo   = "#6D3149" // Cherry 900
	colorAmbAction = "#18191B" // Dark text

	UpiEnabledLeftTextFontColor       = "#B9B9B9" // Gray/Ash
	UpiEnabledVpaFontColor            = "#FFFFFF" // Snow
	UpiEnabledCopyIconUrl             = "https://epifi-icons.pointz.in/tiering/profile/copy_icon.png"
	UpiEnabledCopyIconHeight    int32 = 20
	UpiEnabledCopyIconWidth     int32 = 20
	UpiEnabledBgColorWoTiering        = "#991F1F1F"
	UpiEnabledBgColorPlus             = "#661F1F1F"

	ProfileImageBorderWoTieringColor = "#555555" // Snow
	ProfileImageBorderWTieringColor  = "#30FFFFFF"

	BannerIconUrl                      = "https://epifi-icons.pointz.in/tiering/profile/banner_coin.png"
	BannerIconHeight             int32 = 48
	BannerIconWidth              int32 = 48
	BannerTitlePlusText                = "Add funds to enjoy Plus"
	BannerTitleInfiniteText            = "Add funds to enjoy Infinite"
	BannerTitleSalaryText              = "Salary transaction not detected"
	BannerTitleSalaryLiteText          = "Mandate not executed yet"
	BannerTitleAaSalaryText            = "Salary transaction not detected"
	BannerTitleFontColor               = "#333333" // Gray/Night
	BannerSubTitleRegularText          = "Maintain a minimum balance of %s every month to save %s in monthly fees"
	BannerSubTitlePlusText             = "You need to maintain a minimum balance of %s for Plus. Add funds by %s to continue to enjoy 2x Fi-Coins."
	BannerSubTitleInfiniteText         = "You need to maintain a minimum balance of %s for Infinite. Add funds by %s to continue to enjoy 4x Fi-Coins."
	BannerSubTitleSalaryText           = "Once your salary arrives in your Federal account, all benefits will get activated. "
	BannerSubTitleSalaryLiteText       = "Once your salary arrives in your Federal account, all benefits will get activated. "
	BannerSubTitleAaSalaryText         = "Once your salary arrives in your Federal account, all benefits will get activated. "
	BannerSubTitleFontColor            = "#646464" // Gray/Lead
	BannerBgColor                      = "#F5F5F5" // Gray/Chalk

	SalaryTagIconUrl                     = "https://epifi-icons.pointz.in/tiering/profile/salary_star.png"
	SalaryTagIconHeight            int32 = 14
	SalaryTagIconWidth             int32 = 14
	SalaryTagText                        = "Salary"
	SalaryTextFontColor                  = "#FFFFFF" // Snow
	SalaryTagLeftPadding           int32 = 5
	SalaryTagContainerBgColor            = "#4F71AB" // Colour/Tertiary Indigo
	SalaryTagContainerCornerRadius int32 = 9
	SalaryTagContainerHeight       int32 = 0
	SalaryTagContainerWidth        int32 = 0
	SalaryTagPaddingTop            int32 = 3
	SalaryTagPaddingRight          int32 = 6
	SalaryTagPaddingBottom         int32 = 3
	SalaryTagPaddingLeft           int32 = 6
	SalaryTagBorderColor                 = "#333333" // Gray/Night
	SalaryTagBorderWidth           int32 = 1

	AccExpiryTagText               = "Account expires %s"
	AccExpiryTagFontColor          = "#F4E7BF" // Colour/Pastel Lemon
	AccExpiryTagIconUrl            = "https://epifi-icons.pointz.in/tiering/acc_expiry_info.png"
	AccExpiryTagIconHeight   int32 = 20
	AccExpiryTagIconWidth    int32 = 20
	AccExpiryTagRightPadding int32 = 4

	MinKycTagText                        = "Min KYC"
	MinKycTagFontColor                   = "#FFFFFF" // Snow
	MinKycTagContainerBgColor            = "#D3B250" // Colour/Dark Lemon
	MinKycTagContainerCornerRadius int32 = 9
	MinKycTagContainerHeight       int32 = 0
	MinKycTagContainerWidth        int32 = 0
	MinKycTagPaddingTop            int32 = 3
	MinKycTagPaddingRight          int32 = 6
	MinKycTagPaddingBottom         int32 = 3
	MinKycTagPaddingLeft           int32 = 6
	MinKycTagBorderColor                 = "#333333" // Gray/Night
	MinKycTagBorderWidth           int32 = 1

	ReKycTagText                        = "Re-KYC due"
	ReKycTagFontColor                   = "#FFFFFF" // Snow
	ReKycTagContainerBgColor            = "#E6AA2B" // Colour/Dark Lemon
	ReKycTagContainerCornerRadius int32 = 9
	ReKycTagContainerHeight       int32 = 0
	ReKycTagContainerWidth        int32 = 0
	ReKycTagPaddingTop            int32 = 3
	ReKycTagPaddingRight          int32 = 6
	ReKycTagPaddingBottom         int32 = 3
	ReKycTagPaddingLeft           int32 = 6
	ReKycTagBorderColor                 = "#333333" // Gray/Night
	ReKycTagBorderWidth           int32 = 1

	FullKycTagText                          = "Full KYC"
	FullKycTagFontColor                     = "#FFFFFF" // Snow
	FullKycTagContainerBgColor              = "#00B899" // Colour/Forest
	FullKycTagContainerCornerRadius   int32 = 9
	FullKycTagContainerHeight         int32 = 0
	FullKycTagContainerWidth          int32 = 0
	FullKycTagPaddingTop              int32 = 3
	FullKycTagPaddingRight            int32 = 6
	FullKycTagPaddingBottom           int32 = 3
	FullKycTagPaddingLeft             int32 = 6
	FullKycTagBorderColorBasicTier          = "#333333"   // Gray/Night
	FullKycTagBorderColorNonBasicTier       = "#33FFFFFF" // Gray/Night
	FullKycTagBorderWidth             int32 = 1

	TierTagRegularText                    = "Regular"
	TierTagBasicText                      = "Upgrade account"
	TierTagBasicProfileText               = "Upgrade"
	TierStandardDisplayText               = "Standard"
	TierTagPlusText                       = "Plus"
	TierTagInfiniteText                   = "Infinite"
	TierTagSalaryText                     = "Salary Program"
	TierTagSalaryBasicText                = "Salary Basic Program"
	TierTagSalaryLiteText                 = "Salary Lite Program"
	TierTagAaSalaryText                   = "Prime"
	TierTagFontColor                      = "#FFFFFF" // Snow
	TierTagRegularIconUrl                 = "https://epifi-icons.pointz.in/tiering/regular_badge.png"
	TierTagBasicIconUrl                   = "https://epifi-icons.pointz.in/tiering/upgrade_basic_badge.png"
	TierTagPlusIconUrl                    = "https://epifi-icons.pointz.in/tiering/plus_badge_3d.png"
	TierTagInfiniteIconUrl                = "https://epifi-icons.pointz.in/tiering/infinite_badge_3d_v2.png"
	TierTagSalaryIconUrl                  = "https://epifi-icons.pointz.in/tiering/salary_badge_3d.png"
	TierTagAaSalaryIconUrl                = "https://epifi-icons.pointz.in/aasalary/prime_badge_3d.png"
	TierTagLeftIconHeight           int32 = 20
	TierTagLeftIconWidth            int32 = 20
	TierTagRightIconUrl                   = "https://epifi-icons.pointz.in/tiering/chevron_right_white.png"
	TierTagRightIconHeight          int32 = 20
	TierTagRightIconWidth           int32 = 20
	TierTagLeftPadding              int32 = 8
	TierTagRightPadding             int32 = 4
	TierTagContainerBgColor               = "#661F1F1F"
	TierTagContainerCornerRadius    int32 = 13
	TierTagContainerHeight          int32 = 0
	TierTagContainerWidth           int32 = 0
	TierTagPaddingTop               int32 = 8
	TierTagPaddingRightBasicTier    int32 = 10
	TierTagPaddingRightNonBasicTier int32 = 6
	TierTagPaddingBottom            int32 = 8
	TierTagPaddingLeft              int32 = 10

	AlertComponentIconUrl                = "https://epifi-icons.pointz.in/tiering/profile/warning_triangle.png"
	AlertComponentIconHeight       int32 = 24
	AlertComponentIconWidth        int32 = 24
	AlertComponentIconLottieString       = `{"v":"4.8.0","meta":{"g":"LottieFiles AE ","a":"","k":"","d":"","tc":""},"fr":60,"ip":0,"op":91,"w":28,"h":28,"nm":"Warning","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"Vector","parent":3,"sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[0,5.422,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-10.646,0],[0,10.646],[10.646,0],[0,-10.646]],"o":[[10.646,0],[0,-10.646],[-10.646,0],[0,10.646]],"v":[[0,19.276],[19.276,0],[0,-19.276],[-19.276,0]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.96862745285,0.929411768913,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[5.469,5.469],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Vector","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":1800,"st":0,"bm":0},{"ddd":0,"ind":2,"ty":4,"nm":"Vector","parent":3,"sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[0,-0.33,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[9.572,0],[0,0],[0,-9.572],[0,0],[-9.572,0],[0,0],[0,9.572],[0,0]],"o":[[0,0],[-9.572,0],[0,0],[0,9.572],[0,0],[9.572,0],[0,0],[0,-9.572]],"v":[[1.944,-58.754],[-1.944,-58.754],[-19.276,-41.422],[-19.276,41.422],[-1.944,58.754],[1.944,58.754],[19.276,41.422],[19.276,-41.422]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":0,"k":[1,0.96862745285,0.929411768913,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[5.469,5.469],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Vector","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":1800,"st":0,"bm":0},{"ddd":0,"ind":3,"ty":4,"nm":"Vector","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":20,"s":[0]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":26.666,"s":[6]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":33.334,"s":[-6]},{"t":40,"s":[0]}],"ix":10},"p":{"a":0,"k":[14,13.703,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.552,0.552,0.667],"y":[1,1,1]},"o":{"x":[1,1,0.333],"y":[0,0,0]},"t":0,"s":[135.614,135.614,100]},{"i":{"x":[0.552,0.552,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":20,"s":[100,100,100]},{"i":{"x":[0,0,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":40,"s":[100,100,100]},{"t":60,"s":[135.614,135.614,100]}],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-2.692,4.662],[0,0],[0,0],[-4.661,2.691],[-5.382,0],[-4.661,-2.691],[-2.691,-4.661],[0,0],[0,-5.383],[2.691,-4.662],[4.662,-2.692],[5.383,0],[0,0],[4.662,2.692],[2.691,4.662],[-0.001,5.383]],"o":[[0,0],[0,0],[2.691,-4.661],[4.661,-2.691],[5.382,0],[4.661,2.691],[0,0],[2.692,4.662],[0.001,5.383],[-2.691,4.662],[-4.662,2.692],[0,0],[-5.383,0],[-4.662,-2.692],[-2.691,-4.662],[0.001,-5.383]],"v":[[-175.592,113.735],[-26.544,-144.405],[-26.544,-144.405],[-15.322,-155.627],[0.008,-159.735],[15.338,-155.627],[26.56,-144.405],[175.592,113.735],[179.702,129.068],[175.595,144.402],[164.37,155.627],[149.036,159.735],[-149.036,159.735],[-164.37,155.627],[-175.595,144.402],[-179.703,129.068]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"fl","c":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[1,0.749019622803,0.282352954149,1]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":20,"s":[0.933333337307,0.250980407,0.298039227724,1]},{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":40,"s":[0.933333337307,0.250980407,0.298039227724,1]},{"t":60,"s":[1,0.749019622803,0.282352954149,1]}],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[5.469,5.469],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Vector","np":2,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":1800,"st":0,"bm":0}],"markers":[]}`
	AlertComponentIconRepeatCount  int32 = 3
	AlertComponentText                   = "Your Federal savings account needs verification.\n Tap to start your 3 min video call"
	AlertComponentFontColor              = "#FFFFFF" // Snow
	AlertComponentRightIconUrl           = "https://epifi-icons.pointz.in/tiering/profile/chevron_right.png"
	AlertComponentRightIconHeight  int32 = 24
	AlertComponentRightIconWidth   int32 = 24
	AlertComponentBgColor                = "#282828" // Gray/Ink

	CopyIcon                    = "https://epifi-icons.pointz.in/profile/copy-icon.png"
	CopyIconDisabled            = "https://epifi-icons.pointz.in/profile/copy-disabled.png"
	AccountInfoSectionTitle     = "Federal Savings Account"
	colorChalk                  = "#F0F3F7"
	SavingsAccountCopyLeftText  = "Account No."
	AccountInfoLeftTextColor    = "#B9B9B9"
	AccountInfoCopyTextColor    = "#FFFFFF"
	AccountInfoCopyTextBGColor  = "#661F1F1F"
	colorGrey                   = "#1F1F1F"
	colorGrey40Alpha            = "#661F1F1F"
	colorGrey55Alpha            = "#8C1F1F1F"
	IfscCopyLeftText            = "IFSC"
	SwiftCodeLeftText           = "Swift Code"
	IBANLeftText                = "IBAN"
	IBANCode                    = "FDRLINBBIBD"
	FederalIconUrl              = "https://epifi-icons.pointz.in/onboarding/federal.png"
	FederalIconWidth            = 20
	FederalIconHeight           = 20
	AccountInfoViewSettingsText = "View Settings"

	TierSectionTitle          = "You are on %s"
	TierSectionTitleV2        = "You're on %s"
	RegularTierTitleColor     = "#FFFFFF"
	StandardTierTitleColour   = "#B2B5B9"
	PlusTierTitleColor        = "#CE8963"
	InfiniteTierTitleColor    = "#85A2C0"
	SalaryTierTitleColor      = "BD905C"
	AaSalaryTierTitleColor    = "#4A9BFF"
	TierSectionSubtitle       = "Active since %s"
	dateMonthFormat           = "02 January"
	colorDark                 = "#6A6D70"
	profileStandardIcon       = "https://epifi-icons.pointz.in/profile/standard-icon.png"
	profilePlusTierIcon       = "https://epifi-icons.pointz.in/profile/plus-icon.png"
	profileInfiniteTierIcon   = "https://epifi-icons.pointz.in/profile/infinite-icon.png"
	profileSalaryTierIcon     = "https://epifi-icons.pointz.in/profile/salary-icon.png"
	profileRegularTierIcon    = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/profile-icon-regular-tier.png"
	profileStandardIcon2      = "https://epifi-icons.pointz.in/profile/standard-icon2.png"
	profilePlusTierIcon2      = "https://epifi-icons.pointz.in/profile/plus-icon2.png"
	profileInfiniteTierIcon2  = "https://epifi-icons.pointz.in/profile/infinite-icon2.png"
	profileSalaryTierIcon2    = "https://epifi-icons.pointz.in/profile/salary-icon2.png"
	profileAaSalaryTierIcon2  = "https://epifi-icons.pointz.in/profile/prime-icon2.png"
	profileRegularTierIconV2  = "https://epifi-icons.pointz.in/tiering/plans-home/regularv2.png"
	profileStandardIconV2     = "https://epifi-icons.pointz.in/tiering/plans-home/standardv2.png"
	profilePlusTierIconV2     = "https://epifi-icons.pointz.in/tiering/plans-home/plusv2.png"
	profileInfiniteTierIconV2 = "https://epifi-icons.pointz.in/tiering/plans-home/infinitev2.png"
	profileAaSalaryTierIconV2 = "https://epifi-icons.pointz.in/tiering/plans-home/primev2.png"
	profileSalaryTierIconV2   = "https://epifi-icons.pointz.in/profile/salary-icon2.png"

	salaryDowngradedText               = "Salary not detected, add salary to continue enjoying all benefits"
	salaryGraceText                    = "Salary not detected, add salary to continue enjoying all benefits"
	primeToPrimeDowngradedText         = "2% back activated due to low balance"
	salaryDowngradedCtaText            = "Add Salary"
	tierGraceText                      = "Your balance is below %s. Add funds to stay on the %s Plan"
	tierGraceTextLastXDays             = "%d days left! Add funds to stay on the %s plan. "
	tierGraceCtaText                   = "Add funds"
	tierDowngradedText                 = "You lost access to %s due to low balance"
	tierDowngradeCtaText               = "Upgrade"
	tierPrimeToPrimeDowngradeCtaText   = "See Benefits"
	tierPrimeToPrimeDowngradeV2CtaText = "See benefits"
	seeBenefitsText                    = "See benefits"
	changePlanText                     = "Change plan"
	youGotUpgradedText                 = "Awesome, you just got upgraded!"
	youGotUpgradedFromText             = "Awesome, you got upgraded from %s!"
	switchedFromSalaryProgram          = "You were switched from Salary Program"
	colorNeutralsInk                   = "#18191B"
	colorNeutralsInk70Alpha            = "#B218191B"
	colorHighEmphasis50                = "#F6F9FD"
	colorHighEmphasis50Alpha55         = "#8CF6F9FD"
	infoTextDowngradeColor             = "#FF8C8C"

	trackAmbActionText        = "Track AMB"
	trackAmbActionRegularText = "Check now"
	lowAmbInfoText            = "Low AMB, don't miss out on your rewards!"
	lowAmbInfoRegularText     = "Low AMB, charges will apply"
)

func GetDisplayNameFontColor(isTieringEnabled bool) string {
	return DisplayNameWTieringFontColor
}

func GetDisplayPhoneFontColor(isTieringEnabled bool) string {
	return DisplayPhoneWTieringFontColor
}

func GetUpiDisabledWTieringFontColor(isTieringEnabled bool) string {
	return UpiDisabledWTieringFontColor
}

func GetUpiDisabledWTieringBgColor(isTieringEnabled bool) string {
	return UpiDisabledWTieringBgColor
}

func GetUpiEnabledBgColor(isTieringEnabled bool, currentTier beTieringExtPb.Tier) string {
	if isTieringEnabled {
		return UpiEnabledBgColorPlus
	}
	return UpiEnabledBgColorWoTiering
}

func GetTierTagLeftIconText(currentTier beTieringExtPb.Tier) string {
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return TierTagRegularText
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return TierTagBasicText
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return TierTagPlusText
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return TierTagInfiniteText
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return TierTagSalaryText
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return TierTagSalaryLiteText
	default:
		if currentTier.IsAaSalaryTier() {
			return TierTagAaSalaryText
		}
		return ""
	}
}

func GetTierDisplayName(currentTier beTieringExtPb.Tier) string {
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return TierTagRegularText
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return TierStandardDisplayText
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return TierTagPlusText
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return TierTagInfiniteText
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return TierTagSalaryText
	case beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return TierTagSalaryBasicText
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return TierTagSalaryLiteText
	default:
		if currentTier.IsAaSalaryTier() {
			return TierTagAaSalaryText
		}
		return ""
	}
}

func GetProfileImageBordercolor(isTieringEnabled bool) string {
	if isTieringEnabled {
		return ProfileImageBorderWTieringColor
	}
	return ProfileImageBorderWoTieringColor
}

func GetBgColors(isTieringEnabled bool, currentTier beTieringExtPb.Tier) []string {
	if !isTieringEnabled {
		return []string{}
	}
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return BgColorsRegular
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return BgColorsPlus
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return BgColorsInfinite
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return BgColorsSalary
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return BgColorsSalaryLite
	default:
		if currentTier.IsAaSalaryTier() {
			return BgColorsAaSalary
		}
		return []string{}
	}
}

func GetBannerTitleText(isTieringEnabled bool, currentTier beTieringExtPb.Tier) string {
	if !isTieringEnabled {
		return ""
	}
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return BannerTitlePlusText
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return BannerTitleInfiniteText
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return BannerTitleSalaryText
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return BannerTitleSalaryLiteText
	default:
		if currentTier.IsAaSalaryTier() {
			return BannerTitleAaSalaryText
		}
		return ""
	}
}

func GetBannerSubTitleText(isTieringEnabled bool, currentTier beTieringExtPb.Tier,
	currentTierMinBalance *gmoney.Money, gracePeriodExpiry string) string {
	if !isTieringEnabled {
		return ""
	}
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return fmt.Sprintf(BannerSubTitlePlusText,
			moneyPkg.ToDisplayStringInIndianFormat(currentTierMinBalance, 0, true),
			gracePeriodExpiry)
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return fmt.Sprintf(BannerSubTitleInfiniteText,
			moneyPkg.ToDisplayStringInIndianFormat(currentTierMinBalance, 0, true),
			gracePeriodExpiry)
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return BannerSubTitleSalaryText
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return BannerSubTitleSalaryLiteText
	default:
		if currentTier.IsAaSalaryTier() {
			return BannerSubTitleAaSalaryText
		}
		return ""
	}
}

func GetTierTagUrl(currentTier beTieringExtPb.Tier) string {
	switch currentTier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return TierTagRegularIconUrl
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return TierTagBasicIconUrl
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return TierTagPlusIconUrl
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return TierTagInfiniteIconUrl
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_LITE, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return TierTagSalaryIconUrl
	default:
		if currentTier.IsAaSalaryTier() {
			return TierTagAaSalaryIconUrl
		}
		return ""
	}
}

func GetBannerDeeplink(tier beTieringExtPb.Tier) *deeplinkPb.Deeplink {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return AddFundsDeeplink()
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return AddFundsDeeplink()
	default:
		if tier.IsSalaryRelatedTier() {
			return helper.SalaryBenefitsDeeplink()
		}
		return nil
	}
}

func GetTitleTextColor(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return PlusTierTitleColor
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return InfiniteTierTitleColor
	default:
		if tier.IsSalaryRelatedTier() {
			return SalaryTierTitleColor
		}
		return ""
	}
}

func GetProfileTierIcon(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return profileRegularTierIcon
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return profileStandardIcon2
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return profilePlusTierIcon2
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return profileInfiniteTierIcon2
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_LITE, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return profileSalaryTierIcon2
	default:
		if tier.IsSalaryRelatedTier() {
			return profileAaSalaryTierIcon2
		}
		return ""
	}
}

func GetProfileTierIconV2(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return profileRegularTierIconV2
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return profileStandardIconV2
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return profilePlusTierIconV2
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return profileInfiniteTierIconV2
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_LITE, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return profileSalaryTierIconV2
	default:
		if tier.IsSalaryRelatedTier() {
			return profileAaSalaryTierIconV2
		}
		return ""
	}
}
