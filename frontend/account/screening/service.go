package screening

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/actor"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	consentPb "github.com/epifi/gamma/api/consent"
	creditReportPb2 "github.com/epifi/gamma/api/creditreportv2"
	employmentPb "github.com/epifi/gamma/api/employment"
	pb "github.com/epifi/gamma/api/frontend/account/screening"
	uiStatePb "github.com/epifi/gamma/api/frontend/account/screening/uistate"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	screenerPb "github.com/epifi/gamma/api/screener"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/re_kyc"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	events2 "github.com/epifi/gamma/frontend/events"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/employment"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"
	header2 "github.com/epifi/gamma/pkg/frontend/header"
	"github.com/epifi/gamma/pkg/vkyc"

	"github.com/samber/lo"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

var (
	InvalidProofForSalariedUsersErr    = fmt.Errorf("invalid proof for salaried users")
	InvalidProofForNonSalariedUsersErr = fmt.Errorf("invalid proof for non-salaried users")

	DefaultErrorView = errors2.NewBottomSheetErrorView("SCRNING0013", "Request Failed", "",
		"Apologies, it looks like we’re facing some issues at our end. Can you try again in a few minutes?")

	OccupationNotMentionedErrorView = errors2.NewBottomSheetErrorView("", "Request Failed", "",
		"Please mention your occupation")

	EmploymentTypeNotMentionedErrorView = errors2.NewBottomSheetErrorView("", "Request Failed", "",
		"Please mention your employment type")

	ErrViewStatus                        = rpc.NewStatus(999, "show error view", "")
	InvalidSearchStringArgumentErrorView = errors2.NewBottomSheetErrorView("SCRNING0013", "Request Failed", "",
		"Looks like company name search has invalid characters. Can you try with valid company name")

	processEmploymentUpdateSrcToDeeplinkMap = map[employmentPb.UpdateSource]*deeplink.Deeplink{
		employmentPb.UpdateSource_UPDATE_SOURCE_ONBOARDING: {
			Screen: deeplink.Screen_EMPLOYMENT_VERIFICATION_STATUS,
		},
		employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN: {
			Screen: deeplink.Screen_EMPLOYMENT_VERIFICATION_STATUS,
		},
		employmentPb.UpdateSource_UPDATE_SOURCE_HOME_TOP_NOTICE_BAR: {
			Screen: deeplink.Screen_HOME,
		},
		employmentPb.UpdateSource_UPDATE_SOURCE_VKYC: vkyc.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
			ClientLastState: vkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION.String(),
			ShowCtaLoader:   true,
		}),
		employmentPb.UpdateSource_UPDATE_SOURCE_PERIODIC_KYC: {
			Screen: deeplink.Screen_PERIODIC_KYC_CALL_BACK_API,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&re_kyc.PeriodicKYCCallbackApiScreenOptions{
				Action: compliancePb.Action_ACTION_UPDATE_PERIODIC_KYC_DETAILS.String(),
			}),
		},
	}

	provenanceToUpdateSourceMap = map[pb.UpdateEmploymentDataRequest_Provenance]employmentPb.UpdateSource{
		pb.UpdateEmploymentDataRequest_PROVENANCE_PROFILE:                   employmentPb.UpdateSource_UPDATE_SOURCE_USER_PROFILE,
		pb.UpdateEmploymentDataRequest_PROVENANCE_INC_OCC_DISCREPANCY_POPUP: employmentPb.UpdateSource_UPDATE_SOURCE_INCOME_OCCUPATION_DISCREPANCY_PROMPT,
		pb.UpdateEmploymentDataRequest_PROVENANCE_VKYC:                      employmentPb.UpdateSource_UPDATE_SOURCE_VKYC,
		pb.UpdateEmploymentDataRequest_PROVENANCE_PERIODIC_KYC:              employmentPb.UpdateSource_UPDATE_SOURCE_PERIODIC_KYC,
	}
)

const (
	creditReportVerificationInProgress    = "Verifying your work status"
	cbSuccessEmploymentDeclaration        = "Tell us more about your profession"
	gmailVerificationInProgress           = "Verifying your details"
	gmailVerificationNextPollTime         = 3 // In seconds
	workStatusVerificationSuccess         = "Work status verified successfully!"
	workStatusVerificationFailure         = "Unable to verify work status"
	workStatusVerificationFailureSubtitle = "Let’s try out another way"
	VerifyOtpErrTitle                     = "We’re unable to verify OTP. Please retry or select ‘Verify another way’"
	SendOtpErrTitle                       = "We’re unable to send OTP. Please retry or select ‘Verify another way’"
	DefaultWorkEmailErrTitle              = "Something went wrong. Please retry or select ‘Verify another way’"
	WorkEmailNotAllowedErrTitle           = "Looks like this company email is not in our verified list yet. Please verify another way."
	WorkEmailAlreadyInUseErrTitle         = "This email is already in use. Tap 'Verify another way' if you don't have a work email."
	WorkEmailContainsKeyWordErrTitle      = "Enter your email ID that’s associated with your workplace. Tap 'Verify another way' if you don't have a work email."
	StudentEmailNotAllowedErrTitle        = "Uh-oh, the university linked to this email ID is not on our approved list yet. We're working to expand this list."
	StudentEmailAlreadyInUseErrTitle      = "This email is already in use. Tap 'Verify another way' if you don't have a student email."
	StudentEmailContainsKeyWordErrTitle   = "Enter your email ID that’s associated with your university. Tap 'Verify another way' if you don't have a student email."
	IncOccDiscrepancyConsentNotExist      = "Please provide consent for income"
	IncOccDiscrepancyConsentBEError       = "Sorry, we are facing issue, please try again"
	EmailOtpResendRequestTooSoonErrTitle  = "You are trying to resend OTP too soon, please wait for some time and try again"

	maxDaysForIncOccDiscrepancy = 30
)

func newCheckCreditReportVerificationStatusErr(rpcStatus *rpc.Status, debugMsg string) (*pb.CheckCreditReportVerificationStatusResponse, error) {
	rpcStatus.SetDebugMessage(debugMsg)
	return &pb.CheckCreditReportVerificationStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status:    rpcStatus,
			ErrorView: DefaultErrorView,
		},
	}, nil
}

func newCheckCreditReportVerificationStatusSuccess(ctx context.Context, nextPoll int32, action *deeplink.Deeplink, creditReportVerificationStatus pb.CreditReportVerificationStatus, transitionTitleText, transitionSubtitleText string,
	callInitiatedAt time.Time, pollingRequestInfo *types.PollingRequestInfo) (*pb.CheckCreditReportVerificationStatusResponse, error) {
	return &pb.CheckCreditReportVerificationStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction:                     action,
		NextPollAfter:                  nextPoll,
		CreditReportVerificationStatus: creditReportVerificationStatus,
		TransitionTitleText:            transitionTitleText,
		TransitionSubtitleText:         transitionSubtitleText,
		PollingResponseInfo:            getCreditReportStatusPollingInfo(ctx, callInitiatedAt, pollingRequestInfo, action),
	}, nil
}

func getCreditReportStatusPollingInfo(ctx context.Context, callInitiatedAt time.Time, pollingReqInfo *types.PollingRequestInfo, action *deeplink.Deeplink) *types.PollingResponseInfo {
	pollingMetrics := &creditReportStatusPollingMetrics{
		CurrentCallCounter: 1,
		FirstCallStartedAt: callInitiatedAt,
	}
	previousPollingMetrics := &creditReportStatusPollingMetrics{}
	if len(pollingReqInfo.GetBlob()) != 0 {
		if err := json.Unmarshal(pollingReqInfo.GetBlob(), &previousPollingMetrics); err != nil {
			logger.Error(ctx, "error in marshalling polling metrics", zap.Error(err))
			return nil
		}
		pollingMetrics = &creditReportStatusPollingMetrics{
			CurrentCallCounter: previousPollingMetrics.CurrentCallCounter + 1,
			FirstCallStartedAt: previousPollingMetrics.FirstCallStartedAt,
		}
	}
	metricsBytes, err := json.Marshal(pollingMetrics)
	if err != nil {
		logger.Error(ctx, "error in marshalling polling metrics", zap.Error(err))
		return nil
	}
	// todo: push metrics if required
	if !(action.GetScreen() == deeplink.Screen_CREDIT_REPORT_VERIFICATION_STATUS) {
		return nil
	}
	return &types.PollingResponseInfo{
		Blob: metricsBytes,
	}
}

type Service struct {
	pb.UnimplementedScreeningServer

	EmploymentClient     employmentPb.EmploymentClient
	Conf                 *config.Screening
	OnbClient            onboarding.OnboardingClient
	actorClient          actor.ActorClient
	consentClient        consentPb.ConsentClient
	usersClient          user.UsersClient
	scrnrClient          screenerPb.ScreenerClient
	eventBroker          events.Broker
	genConf              *genconf.Config
	frontendConfig       *config.Config
	creditReportV2Client creditReportPb2.CreditReportManagerClient
}

func NewService(empClient employmentPb.EmploymentClient, conf *config.Screening, onbClient onboarding.OnboardingClient,
	actorClient actor.ActorClient,
	consentClient consentPb.ConsentClient, usersClient user.UsersClient, scrnrClient screenerPb.ScreenerClient,
	eventBroker events.Broker, genConf *genconf.Config, frontendConfig *config.Config, creditReportV2Client creditReportPb2.CreditReportManagerClient) *Service {
	return &Service{
		EmploymentClient:     empClient,
		Conf:                 conf,
		OnbClient:            onbClient,
		actorClient:          actorClient,
		consentClient:        consentClient,
		usersClient:          usersClient,
		scrnrClient:          scrnrClient,
		eventBroker:          eventBroker,
		genConf:              genConf,
		frontendConfig:       frontendConfig,
		creditReportV2Client: creditReportV2Client,
	}
}

var _ pb.ScreeningServer = &Service{}

func (s *Service) CheckCreditReportAvailabilityStatus(ctx context.Context, request *pb.CheckCreditReportAvailabilityStatusRequest) (*pb.CheckCreditReportAvailabilityStatusResponse, error) {
	var action *deeplink.Deeplink
	var err error
	actorId := request.GetReq().GetAuth().GetActorId()
	if action, err = s.getNextOnboardingAction(ctx, actorId); err != nil {
		status := rpc.StatusInternal()
		status.DebugMessage = err.Error()
		return &pb.CheckCreditReportAvailabilityStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: DefaultErrorView,
			},
		}, nil
	}
	return &pb.CheckCreditReportAvailabilityStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextPollAfter: s.Conf.CheckCreditReportAvailabilityStatusPollIntervalInSecs,
		NextAction:    action,
	}, nil
}

func (s *Service) GetConsentAndVerifyCreditReport(ctx context.Context, request *pb.GetConsentAndVerifyCreditReportRequest) (*pb.GetConsentAndVerifyCreditReportResponse, error) {
	errRes := func(rpcStatus *rpc.Status, debugMsg string) (*pb.GetConsentAndVerifyCreditReportResponse, error) {
		rpcStatus.SetDebugMessage(debugMsg)
		return &pb.GetConsentAndVerifyCreditReportResponse{RespHeader: &header.ResponseHeader{Status: rpcStatus, ErrorView: DefaultErrorView}}, nil
	}
	successRes := func(action *deeplink.Deeplink) (*pb.GetConsentAndVerifyCreditReportResponse, error) {
		return &pb.GetConsentAndVerifyCreditReportResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()}, NextAction: action}, nil
	}
	var (
		action *deeplink.Deeplink
		err    error
	)
	actorId := request.GetReq().GetAuth().GetActorId()
	// consent given by user
	if request.GetConsent() {
		if err = s.recordReportDownloadConsent(ctx, actorId, true, request); err != nil {
			s.logChoseConsentForVerifyCreditReportServer(ctx, actorId, events2.Failure, err.Error())
			logger.Error(ctx, "failed to record positive report download consent", zap.Error(err))
			return errRes(rpc.StatusInternal(), err.Error())
		}
		s.logChoseConsentForVerifyCreditReportServer(ctx, actorId, events2.Success, "")

		resp, respErr := s.creditReportV2Client.InitiateCreditReportVerification(ctx, &creditReportPb2.InitiateCreditReportVerificationRequest{
			ActorId: actorId,
		})
		if grpcErr := epifigrpc.RPCError(resp, respErr); grpcErr != nil {
			if resp.GetStatus().GetCode() == uint32(creditReportPb2.InitiateCreditReportVerificationResponse_CREDIT_REPORT_NOT_PRESENT) {
				if action, err = s.getNextOnboardingAction(ctx, actorId); err != nil {
					return errRes(rpc.StatusInternal(), err.Error())
				}
				return successRes(action)
			}
			logger.Error(ctx, "failed to initiate credit report verification", zap.Error(grpcErr))
			return errRes(rpc.StatusInternal(), grpcErr.Error())
		}

		if action, err = s.getNextOnboardingAction(ctx, actorId); err != nil {
			return errRes(rpc.StatusInternal(), err.Error())
		}
		return successRes(action)
	}
	// consent not given
	if err = s.recordReportDownloadConsent(ctx, actorId, false, request); err != nil {
		logger.Error(ctx, "failed to record negative report download consent", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}

	if action, err = s.getNextOnboardingAction(ctx, actorId); err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}
	return successRes(action)
}

func (s *Service) CheckCreditReportVerificationStatus(ctx context.Context, request *pb.CheckCreditReportVerificationStatusRequest) (*pb.CheckCreditReportVerificationStatusResponse, error) {
	var action *deeplink.Deeplink
	var err error
	callInitiatedAt := time.Now()
	s.logCreditReportStatusPollingMetrics(ctx, request.GetPollingRequestInfo())
	actorId := request.GetReq().GetAuth().GetActorId()

	if action, err = s.getNextOnboardingAction(ctx, actorId); err != nil {
		return newCheckCreditReportVerificationStatusErr(rpc.StatusInternal(), err.Error())
	}

	checkPassed, err := s.didCreditReportOrIncomeEstimateCheckPass(ctx, actorId)
	if err != nil {
		return newCheckCreditReportVerificationStatusErr(rpc.StatusInternal(), err.Error())
	}

	if checkPassed {
		return newCheckCreditReportVerificationStatusSuccess(ctx,
			s.Conf.CheckCreditReportVerificationStatusPollIntervalInSecs, action,
			pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL, cbSuccessEmploymentDeclaration, "", callInitiatedAt, request.GetPollingRequestInfo())
	}

	switch action.GetScreen() {
	case deeplink.Screen_CREDIT_REPORT_VERIFICATION_STATUS:
		return newCheckCreditReportVerificationStatusSuccess(ctx,
			s.Conf.CheckCreditReportVerificationStatusPollIntervalInSecs, action,
			pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_IN_PROGRESS, creditReportVerificationInProgress, "", callInitiatedAt, request.GetPollingRequestInfo())
	default:
		logger.Info(ctx, fmt.Sprintf("received next screen in CheckCreditReportVerificationStatus: %s", action.GetScreen().String()))
		return newCheckCreditReportVerificationStatusSuccess(ctx,
			s.Conf.CheckCreditReportVerificationStatusPollIntervalInSecs, action,
			pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_FAILED, workStatusVerificationFailure, workStatusVerificationFailureSubtitle, callInitiatedAt, request.GetPollingRequestInfo())
	}
}

func (s *Service) logCreditReportStatusPollingMetrics(ctx context.Context, info *types.PollingRequestInfo) {
	if len(info.GetBlob()) == 0 {
		return
	}
	var pollingMetrics *creditReportStatusPollingMetrics
	if err := json.Unmarshal(info.GetBlob(), &pollingMetrics); err != nil {
		logger.Error(ctx, "unable to marshal blob to polling metrics", zap.Error(err))
		return
	}

	totalTimeTaken := time.Since(pollingMetrics.FirstCallStartedAt)
	logger.Info(ctx, "Credit Report Verification Status Polling Metrics",
		zap.Time(logger.START_TIME, pollingMetrics.FirstCallStartedAt),
		zap.Duration(logger.TIME_DURATION, totalTimeTaken),
		zap.Int(logger.COUNT, pollingMetrics.CurrentCallCounter))
}

// didCreditReportOrIncomeEstimateCheckPass checks if screener passed using credit report or income estimate check from screener service
func (s *Service) didCreditReportOrIncomeEstimateCheckPass(ctx context.Context, actorId string) (bool, error) {
	scrnrResp, scrnrErr := s.scrnrClient.GetScreenerAttemptsByActorId(ctx, &screenerPb.GetScreenerAttemptsByActorIdRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if te := epifigrpc.RPCError(scrnrResp, scrnrErr); te != nil {
		logger.Error(ctx, "failed to get details from onboarding service", zap.Error(te))
		return false, te
	}

	crCheckDetails, ok := lo.Find[*screenerPb.CheckDetails](scrnrResp.GetChecksMap(), func(check *screenerPb.CheckDetails) bool {
		return check.GetCheckType() == screenerPb.CheckType_CHECK_TYPE_CREDIT_REPORT || check.GetCheckType() == screenerPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE
	})
	if !ok {
		logger.Error(ctx, fmt.Sprintf("did not find credit report check in screener attempt"))
		return false, fmt.Errorf("did not find current check in screener attempt")
	}
	return crCheckDetails.GetCheckResult() == screenerPb.CheckResult_CHECK_RESULT_PASSED, nil
}

func (s *Service) ProcessEmploymentData(ctx context.Context,
	req *pb.ProcessEmploymentDataRequest) (res *pb.ProcessEmploymentDataResponse, err error) {
	res = &pb.ProcessEmploymentDataResponse{RespHeader: &header.ResponseHeader{}}
	logger.Info(ctx, fmt.Sprintf("Get request in process employment data source: %v", req.GetUpdateSource()),
		zap.String(logger.APP_PLATFORM, req.GetReq().GetPlatform().String()),
		zap.Uint32(logger.APP_VERSION_CODE, req.GetReq().GetAppVersionCode()))

	updateSource := employmentPb.UpdateSource(employmentPb.UpdateSource_value[req.GetUpdateSource()])
	resp, err := s.handleIncomeOccupationDiscrepancy(ctx, req)
	if err != nil && updateSource != employmentPb.UpdateSource_UPDATE_SOURCE_ONBOARDING {
		return resp, err
	}

	return s.processEmploymentData(ctx, req)

}

// performEPFOCheck fetches company details from user input, updates employment_data table and
// performs EPFO check
func (s *Service) performEPFOCheck(ctx context.Context, req *pb.ProcessEmploymentDataRequest) (*pb.ProcessEmploymentDataResponse, error) {
	updateSource := employmentPb.UpdateSource(employmentPb.UpdateSource_value[req.GetUpdateSource()])
	empInfo := &employmentPb.EmployerInfo{}
	var err error
	if !strings.EqualFold(req.GetEmployerInfo().GetId(), "") {
		// Getting employer to fill complete company information
		empInfo, err = s.getEmployerFromId(ctx, req.GetEmployerInfo().GetId())
		if err != nil {
			return &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while getting employer from id")),
					ErrorView: DefaultErrorView,
				},
			}, nil
		}
	}

	// calling process employment data to update company information in user's employment data
	// and publish message to begin EPFO check
	actorId := req.GetReq().GetAuth().GetActorId()
	empDataResp, respErr := s.EmploymentClient.ProcessEmploymentData(ctx, &employmentPb.ProcessEmploymentDataRequest{
		ActorId:     actorId,
		ClientReqId: req.GetClientReqId(),
		EmploymentInfoOptions: &employmentPb.ProcessEmploymentDataRequest_CompanyInfo{
			CompanyInfo: &employmentPb.CompanyInfoOption{
				CompanyName:     getCompanyNameElseEnteredText(empInfo.GetNameBySource(), req.GetEmployerInfo().GetEnteredText()),
				VendorId:        getVendorIdFromEmployerInfo(empInfo),
				IsEpfRegistered: empInfo.GetIsEpfRegistered(),
				IsManualInput:   empInfo.GetEmployerId() == "",
				EnteredText:     req.GetEmployerInfo().GetEnteredText(),
				EmployerId:      req.GetEmployerInfo().GetId(),
			},
		},
		UpdateSource: updateSource,
	})
	if grpcErr := epifigrpc.RPCError(empDataResp, respErr); grpcErr != nil {
		logger.Error(ctx, "error in BE rpc ProcessEmploymentData", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error calling BE ProcessEmploymentData"),
				ErrorView: DefaultErrorView,
			},
		}, nil

	}

	return &pb.ProcessEmploymentDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: processEmploymentUpdateSrcToDeeplinkMap[employmentPb.UpdateSource(employmentPb.UpdateSource_value[req.GetUpdateSource()])],
	}, nil
}

func getCompanyNameElseEnteredText(nameBySource, enteredText string) string {
	// if name by source is not present, then either it is a custom company name or
	// employer not found in db. In this case populate entered text as company name
	if !strings.EqualFold(nameBySource, "") {
		return nameBySource
	}
	return enteredText
}

func getVendorIdFromEmployerInfo(empInfo *employmentPb.EmployerInfo) string {
	switch {
	// if source is PF data, then vendor id is Karza ID
	case !strings.EqualFold(empInfo.GetMetadata().GetKid(), ""):
		return empInfo.GetMetadata().GetKid()
	// if source is MCA, then vendor id is entity ID
	case !strings.EqualFold(empInfo.GetMetadata().GetEntityId(), ""):
		return empInfo.GetMetadata().GetEntityId()
	default:
		return ""
	}
}

func (s *Service) getNextOnboardingAction(ctx context.Context, actorId string) (*deeplink.Deeplink, error) {
	onbRes, err := s.OnbClient.GetNextAction(ctx, &onboarding.GetNextActionRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(onbRes, err); err != nil {
		logger.ErrorNoCtx("error fetching next action from onboarding service", zap.Error(err))
		return nil, fmt.Errorf("error fetching next onboarding action: %w", err)
	}
	return onbRes.GetNextAction(), nil
}

func (s *Service) CheckEmploymentVerificationStatus(ctx context.Context,
	req *pb.CheckEmploymentVerificationStatusRequest) (res *pb.CheckEmploymentVerificationStatusResponse, err error) {
	res = &pb.CheckEmploymentVerificationStatusResponse{RespHeader: &header.ResponseHeader{}}
	actorId := req.GetReq().GetAuth().GetActorId()
	action, err := s.getNextOnboardingAction(ctx, actorId)
	if err != nil {
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		res.RespHeader.ErrorView = DefaultErrorView
		logger.Info(ctx, fmt.Sprintf("Final Employment verification response: %v", res))
		return res, nil
	}
	res.RespHeader.Status = rpc.StatusOk()
	res.NextAction = action
	onbRes, err := s.OnbClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
	})
	if err = epifigrpc.RPCError(onbRes, err); err != nil {
		logger.Error(ctx, "failed to get details from onboarding service", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		logger.Info(ctx, fmt.Sprintf("Final Employment verification response: %v", res))
		return res, nil
	}
	// adding both cb and emp verification passed flag from defense POV
	isScreeningPassed := onbRes.GetDetails().GetStageMetadata().GetAppScreeningData().GetCreditReportVerificationPassed() == commontypes.BooleanEnum_TRUE ||
		onbRes.GetDetails().GetStageMetadata().GetAppScreeningData().GetEmploymentVerificationPassed() == commontypes.BooleanEnum_TRUE ||
		onbRes.GetDetails().GetStageDetails().GetStageMapping()[onboarding.OnboardingStage_APP_SCREENING.String()].GetState() == onboarding.OnboardingState_SUCCESS
	if isScreeningPassed {
		feRes, err := newEmploymentVerificationStatusResponse(
			s.Conf.EmpVerificationCheckStatusPollIntervalInSecs, action,
			workStatusVerificationSuccess, "Taking you to the next step")
		logger.Info(ctx, fmt.Sprintf("Final Employment verification response: %v", feRes))
		return feRes, err
	}
	switch action.GetScreen() {
	case deeplink.Screen_EMPLOYMENT_VERIFICATION_STATUS:
		feRes, err := newEmploymentVerificationStatusResponse(
			s.Conf.EmpVerificationCheckStatusPollIntervalInSecs, action,
			creditReportVerificationInProgress, "")
		logger.Info(ctx, fmt.Sprintf("Final Employment verification response: %v", feRes), zap.String(logger.STATE, action.GetScreen().String()))
		return feRes, err
	default:
		logger.Error(ctx, fmt.Sprintf("received unexpected screen %s", action.GetScreen().String()))
		feRes, err := newEmploymentVerificationStatusResponse(
			s.Conf.EmpVerificationCheckStatusPollIntervalInSecs, action,
			creditReportVerificationInProgress, "")
		logger.Info(ctx, fmt.Sprintf("Final Employment verification response: %v", feRes), zap.String(logger.STATE, action.GetScreen().String()))
		return feRes, err
	}
}

func (s *Service) GetCompanyNames(ctx context.Context,
	req *pb.GetCompanyNamesRequest) (res *pb.GetCompanyNamesResponse, err error) {
	res = &pb.GetCompanyNamesResponse{RespHeader: &header.ResponseHeader{}}
	logger.Info(ctx, "started GetCompanyNames", zap.String("namePrefix", req.GetNamePrefix()))
	beRes, err := s.EmploymentClient.SearchCompany(ctx, &employmentPb.SearchCompanyRequest{
		CompanyName: req.GetNamePrefix(),
	})
	if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
		logger.Error(ctx, "error in calling BE search company method", zap.Error(rpcErr))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(rpcErr.Error())
		res.RespHeader.ErrorView = DefaultErrorView
		return res, nil
	}
	companies := make([]*pb.CompanyInfo, 0)
	for _, company := range beRes.Companies {
		companies = append(companies, convertToFECompanyInfo(company))
	}
	logger.Info(ctx, "successfully returning company suggestions", zap.Int("suggestionCount", len(companies)))
	res.Companies = companies
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) recordReportDownloadConsent(ctx context.Context, actorId string, consentGiven bool, request *pb.GetConsentAndVerifyCreditReportRequest) error {
	// record consent in consent service if consent is given by user
	if consentGiven {
		recordConsentResp, err := s.consentClient.RecordConsent(ctx, &consentPb.RecordConsentRequest{
			ConsentType: consentPb.ConsentType_CREDIT_REPORT_TNC,
			ActorId:     actorId,
			Device:      request.GetReq().GetAuth().GetDevice(),
			Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
		})
		if err1 := epifigrpc.RPCError(recordConsentResp, err); err1 != nil {
			return err1
		}
	}

	downloadConsentResp, err := s.creditReportV2Client.RecordReportDownloadConsent(ctx, &creditReportPb2.RecordReportDownloadConsentRequest{
		ActorId: actorId,
		Consent: consentGiven,
	})
	if rpcErr := epifigrpc.RPCError(downloadConsentResp, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching RecordReportDownloadConsent", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (s *Service) getEmployerFromId(ctx context.Context, employerId string) (*employmentPb.EmployerInfo, error) {
	// Getting employer to fill complete company information
	employerResp, getErr := s.EmploymentClient.GetEmployer(ctx, &employmentPb.GetEmployerRequest{
		Identifier: &employmentPb.GetEmployerRequest_EmployerId{
			EmployerId: employerId,
		},
	})
	if err := epifigrpc.RPCError(employerResp, getErr); err != nil &&
		!employerResp.GetStatus().IsRecordNotFound() && !storage.IsRecordNotFoundError(getErr) {
		logger.Error(ctx, "error while getting employer from id", zap.Error(err), zap.String("employer_id", employerId))
		return nil, fmt.Errorf("error while getting employer from id")
	}
	return employerResp.GetEmployerInfo(), nil
}

func updateEmploymentInfoForNonSalariedUsers(ctx context.Context,
	req *pb.ProcessEmploymentDataRequest, beReq *employmentPb.ProcessEmploymentDataRequest) (*employmentPb.ProcessEmploymentDataRequest, error) {
	logger.Debug(ctx, "creating BE request for non-salaried users")

	switch req.GetEmploymentType() {
	case uiStatePb.EmploymentType_HOMEMAKER:
	case uiStatePb.EmploymentType_WORKING_PROFESSIONAL:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_EnrollmentNumberOption{
			EnrollmentNumberOption: &employmentPb.EnrollmentNumberOption{
				EnrollmentNo: req.GetEnrollmentNumber().GetEnrollmentNo(),
			},
		}
	case uiStatePb.EmploymentType_BUSINESS_OWNER:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_BusinessOwnerOption{
			BusinessOwnerOption: &employmentPb.BusinessOwnerInfoOption{
				GstinNo: req.GetBusinessOwnerInfo().GetGstinNo(),
			},
		}
	case uiStatePb.EmploymentType_STUDENT:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_StudentDetailsOption{
			StudentDetailsOption: &employmentPb.StudentDetailsOption{
				Year:   req.GetStudentDetails().GetYear(),
				MailId: req.GetStudentDetails().GetMailId(),
			},
		}
	case uiStatePb.EmploymentType_FREELANCER, uiStatePb.EmploymentType_SELF_EMPLOYED, uiStatePb.EmploymentType_RETIRED, uiStatePb.EmploymentType_OTHERS:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_PersonalProfileInfo{
			PersonalProfileInfo: &employmentPb.PersonalProfileOption{
				PersonalProfileInfo: req.GetPersonalInfo().GetUrl(),
			},
		}
	default:
		logger.Error(ctx, "invalid employment proof type for non-salaried users",
			zap.String("proofType", req.GetEmploymentProofType().String()))
		return nil, InvalidProofForNonSalariedUsersErr
	}
	return beReq, nil
}

func convertToFECompanyInfo(beCompanyInfo *employmentPb.CompanyInfo) *pb.CompanyInfo {
	return &pb.CompanyInfo{
		Name:            beCompanyInfo.CompanyName,
		VendorId:        beCompanyInfo.VendorId,
		IsEpfRegistered: beCompanyInfo.IsEpfRegistered,
	}
}

func getBeEmploymentType(feEmploymentType uiStatePb.EmploymentType) employmentPb.EmploymentType {
	switch feEmploymentType {
	case uiStatePb.EmploymentType_SALARIED:
		return employmentPb.EmploymentType_SALARIED
	case uiStatePb.EmploymentType_SELF_EMPLOYED:
		return employmentPb.EmploymentType_SELF_EMPLOYED
	case uiStatePb.EmploymentType_RETIRED:
		return employmentPb.EmploymentType_RETIRED
	case uiStatePb.EmploymentType_OTHERS:
		return employmentPb.EmploymentType_OTHERS
	case uiStatePb.EmploymentType_FREELANCER:
		return employmentPb.EmploymentType_FREELANCER
	case uiStatePb.EmploymentType_WORKING_PROFESSIONAL:
		return employmentPb.EmploymentType_WORKING_PROFESSIONAL
	case uiStatePb.EmploymentType_HOMEMAKER:
		return employmentPb.EmploymentType_HOMEMAKER
	case uiStatePb.EmploymentType_BUSINESS_OWNER:
		return employmentPb.EmploymentType_BUSINESS_OWNER
	case uiStatePb.EmploymentType_STUDENT:
		return employmentPb.EmploymentType_STUDENT
	default:
		return employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED
	}
}

func (s *Service) logChoseConsentForVerifyCreditReportServer(ctx context.Context, actorId, requestStatus, requestFailedReason string) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewChoseConsentForVerifyCreditReportServer(actorId, requestStatus, requestFailedReason))
	})
}

func (s *Service) CheckGmailVerificationStatus(ctx context.Context, req *pb.CheckGmailVerificationStatusRequest) (*pb.CheckGmailVerificationStatusResponse, error) {
	// Populate waiting time and return
	res := &pb.CheckGmailVerificationStatusResponse{
		NextAction:             nil,
		NextPollAfter:          gmailVerificationNextPollTime,
		TransitionTitleText:    gmailVerificationInProgress,
		TransitionSubtitleText: "",
		RespHeader: &header.ResponseHeader{
			Status:    rpc.StatusOk(),
			ErrorView: nil,
		},
	}

	// Get next action
	gmailRes, err := s.OnbClient.GetNextAction(ctx, &onboarding.GetNextActionRequest{ActorId: req.GetReq().GetAuth().GetActorId()})
	if err = epifigrpc.RPCError(gmailRes, err); err != nil {
		logger.Error(ctx, "failed to fetch next action from onboarding service", zap.Error(err))
		res.RespHeader.Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.NextAction = gmailRes.GetNextAction()
	return res, err
}

func (s *Service) UpdateEmploymentData(ctx context.Context, req *pb.UpdateEmploymentDataRequest) (*pb.UpdateEmploymentDataResponse, error) {
	var (
		res = &pb.UpdateEmploymentDataResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusOk(), ErrorView: nil}}
	)

	beReq := &employmentPb.UpdateNewEmploymentDataRequest{
		ActorId:               req.GetReq().GetAuth().GetActorId(),
		EmploymentType:        getBeEmploymentType(req.GetEmploymentType()),
		EmploymentInfoOptions: nil,
		AnnualSalary:          req.GetAnnualSalary(),
		OccupationType:        getOccupationFromString(ctx, req.GetOccupationType()),
	}
	beReq, err := populateEmploymentInfoForUpdateEmploymentRequest(ctx, req, beReq)
	if err != nil {
		logger.Error(ctx, "failed to update be employment request with employment info options", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	beReq.UpdateSource = provenanceToUpdateSourceMap[req.GetProvenance()]
	beRes, err := s.EmploymentClient.UpdateNewEmploymentData(ctx, beReq)
	if err = epifigrpc.RPCError(beRes, err); err != nil {
		if beRes.GetStatus().IsInvalidArgument() && strings.EqualFold(beRes.GetStatus().GetDebugMessage(), "occupation not mentioned") {
			res.RespHeader.Status = rpc.StatusInvalidArgument()
			res.RespHeader.ErrorView = OccupationNotMentionedErrorView
			return res, nil
		}
		logger.Error(ctx, "failed to update be employment information in be", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	return res, nil
}

func newEmploymentVerificationStatusResponse(nextPoll int32, action *deeplink.Deeplink,
	transitionTitleText, transitionSubtitleText string) (*pb.CheckEmploymentVerificationStatusResponse, error) {
	return &pb.CheckEmploymentVerificationStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction:             action,
		NextPollAfter:          nextPoll,
		TransitionTitleText:    transitionTitleText,
		TransitionSubtitleText: transitionSubtitleText,
	}, nil
}

func populateEmploymentInfo(ctx context.Context, req *pb.ProcessEmploymentDataRequest, beReq *employmentPb.ProcessEmploymentDataRequest) (*employmentPb.ProcessEmploymentDataRequest, error) {
	beReq.UpdateSource = employmentPb.UpdateSource(employmentPb.UpdateSource_value[req.GetUpdateSource()])
	switch req.GetEmploymentType() {
	case uiStatePb.EmploymentType_HOMEMAKER:
	case uiStatePb.EmploymentType_WORKING_PROFESSIONAL:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_EnrollmentNumberOption{
			EnrollmentNumberOption: &employmentPb.EnrollmentNumberOption{
				EnrollmentNo: req.GetEnrollmentNumber().GetEnrollmentNo(),
			},
		}
	case uiStatePb.EmploymentType_BUSINESS_OWNER:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_BusinessOwnerOption{
			BusinessOwnerOption: &employmentPb.BusinessOwnerInfoOption{
				GstinNo: req.GetBusinessOwnerInfo().GetGstinNo(),
			},
		}
	case uiStatePb.EmploymentType_STUDENT:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_StudentDetailsOption{
			StudentDetailsOption: &employmentPb.StudentDetailsOption{
				Year:   req.GetStudentDetails().GetYear(),
				MailId: req.GetStudentDetails().GetMailId(),
			},
		}
	case uiStatePb.EmploymentType_FREELANCER, uiStatePb.EmploymentType_SELF_EMPLOYED, uiStatePb.EmploymentType_RETIRED, uiStatePb.EmploymentType_OTHERS:
		beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_PersonalProfileInfo{
			PersonalProfileInfo: &employmentPb.PersonalProfileOption{
				PersonalProfileInfo: req.GetPersonalInfo().GetUrl(),
			},
		}
	case uiStatePb.EmploymentType_SALARIED:
		switch req.GetInput().(type) {
		case *pb.ProcessEmploymentDataRequest_EmployerInfo:
			if req.GetEmployerInfo().GetId() == "" {
				logger.Info(ctx, "employment info does contain required fields")
				return beReq, nil
			}
			beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_EmployerInfo{
				EmployerInfo: &employmentPb.EmployerInfoOption{
					EmployerId:  req.GetEmployerInfo().GetId(),
					EnteredText: req.GetEmployerInfo().GetEnteredText(),
				}}
			return beReq, nil
		case *pb.ProcessEmploymentDataRequest_CompanyInfo:
			if req.GetCompanyInfo().GetName() == "" {
				logger.Info(ctx, "Company info doesn't contain required fields")
				return beReq, nil
			}
			companyInfo := req.GetCompanyInfo()
			isManuallyAdded := companyInfo.GetVendorId() == ""
			beReq.EmploymentInfoOptions = &employmentPb.ProcessEmploymentDataRequest_CompanyInfo{
				CompanyInfo: &employmentPb.CompanyInfoOption{
					CompanyName:     companyInfo.GetName(),
					VendorId:        companyInfo.GetVendorId(),
					IsEpfRegistered: companyInfo.GetIsEpfRegistered(),
					IsManualInput:   isManuallyAdded,
					EnteredText:     companyInfo.GetEnteredText(),
				}}
			return beReq, nil
		default:
			logger.Info(ctx, fmt.Sprintf("Input type not implementated for salaried employment type, '%v'", req.GetInput()))
		}
	default:
		logger.Info(ctx, fmt.Sprintf("invalid employment type: %v", req.GetEmploymentProofType()))
		return nil, InvalidProofForNonSalariedUsersErr
	}
	return beReq, nil
}

func populateEmploymentInfoForUpdateEmploymentRequest(ctx context.Context,
	req *pb.UpdateEmploymentDataRequest, beReq *employmentPb.UpdateNewEmploymentDataRequest) (*employmentPb.UpdateNewEmploymentDataRequest, error) {
	logger.Debug(ctx, "creating BE request for non-salaried users")

	switch req.GetEmploymentType() {
	case uiStatePb.EmploymentType_HOMEMAKER:
	case uiStatePb.EmploymentType_WORKING_PROFESSIONAL:
		beReq.EmploymentInfoOptions = &employmentPb.UpdateNewEmploymentDataRequest_EnrollmentNumberOption{
			EnrollmentNumberOption: &employmentPb.EnrollmentNumberOption{
				EnrollmentNo: req.GetEnrollmentNumber().GetEnrollmentNo(),
			},
		}
	case uiStatePb.EmploymentType_BUSINESS_OWNER:
		beReq.EmploymentInfoOptions = &employmentPb.UpdateNewEmploymentDataRequest_BusinessOwnerOption{
			BusinessOwnerOption: &employmentPb.BusinessOwnerInfoOption{
				GstinNo: req.GetBusinessOwnerInfo().GetGstinNo(),
			},
		}
	case uiStatePb.EmploymentType_STUDENT:
		beReq.EmploymentInfoOptions = &employmentPb.UpdateNewEmploymentDataRequest_StudentDetailsOption{
			StudentDetailsOption: &employmentPb.StudentDetailsOption{
				Year:   req.GetStudentDetails().GetYear(),
				MailId: req.GetStudentDetails().GetMailId(),
			},
		}
	case uiStatePb.EmploymentType_FREELANCER, uiStatePb.EmploymentType_SELF_EMPLOYED, uiStatePb.EmploymentType_RETIRED, uiStatePb.EmploymentType_OTHERS:
		beReq.EmploymentInfoOptions = &employmentPb.UpdateNewEmploymentDataRequest_PersonalProfileInfo{
			PersonalProfileInfo: &employmentPb.PersonalProfileOption{
				PersonalProfileInfo: req.GetPersonalInfo().GetUrl(),
			},
		}
	case uiStatePb.EmploymentType_SALARIED:
		switch req.GetInput().(type) {
		case *pb.UpdateEmploymentDataRequest_EmployerInfo:
			beReq.EmploymentInfoOptions = &employmentPb.UpdateNewEmploymentDataRequest_EmployerInfo{
				EmployerInfo: &employmentPb.EmployerInfoOption{
					EmployerId:  req.GetEmployerInfo().GetId(),
					EnteredText: req.GetEmployerInfo().GetEnteredText(),
				}}
			return beReq, nil
		case *pb.UpdateEmploymentDataRequest_CompanyInfo:
			companyInfo := req.GetCompanyInfo()
			isManuallyAdded := companyInfo.GetVendorId() == ""
			beReq.EmploymentInfoOptions = &employmentPb.UpdateNewEmploymentDataRequest_CompanyInfo{
				CompanyInfo: &employmentPb.CompanyInfoOption{
					CompanyName:     companyInfo.GetName(),
					VendorId:        companyInfo.GetVendorId(),
					IsEpfRegistered: companyInfo.GetIsEpfRegistered(),
					IsManualInput:   isManuallyAdded,
					EnteredText:     companyInfo.GetEnteredText(),
				}}
			return beReq, nil
		default:
			logger.Error(ctx, fmt.Sprintf("invalid input for SALARIED employment type, '%v'", req.GetInput()))
		}
	default:
		logger.Error(ctx, "invalid employment proof type for non-salaried users",
			zap.String("proofType", req.GetEmploymentProofType().String()))
		return nil, InvalidProofForNonSalariedUsersErr
	}
	return beReq, nil
}

func getSalaryRange(salary float32) *user.SalaryRange {
	var maxSalary int32
	var minSalary int32
	salaryRanges := [][]int32{
		{0, 100000},
		{100000, 500000},
		{500000, 1000000},
		{1000000, 2500000},
		{2500000, 10000000},
		{10000000, 25000000},
		{25000000, 1000000000},
	}
	for _, salaryRange := range salaryRanges {
		if int32(salary) >= salaryRange[0] && int32(salary) < salaryRange[1] {
			minSalary = salaryRange[0]
			maxSalary = salaryRange[1]
			break
		}
	}
	return &user.SalaryRange{
		MinValue: minSalary,
		MaxValue: maxSalary,
	}
}

func (s *Service) GetCompanyNamesV2(ctx context.Context, req *pb.GetCompanyNamesRequestV2) (*pb.GetCompanyNamesResponseV2, error) {
	res := &pb.GetCompanyNamesResponseV2{RespHeader: &header.ResponseHeader{}}
	beRes, err := s.EmploymentClient.SearchCompanyV2(ctx, &employmentPb.SearchCompanyRequestV2{
		SearchString: req.GetNamePrefix(),
		Provenance:   employmentPb.SearchCompanyRequestV2_PROFILE,
	})
	if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
		if rpc.StatusFromError(rpcErr).IsInvalidArgument() {
			logger.Error(ctx, fmt.Sprintf("Invalid search string, string: %v", req.GetNamePrefix()), zap.Error(rpcErr))
			res.RespHeader.Status = rpc.StatusInvalidArgumentWithDebugMsg(beRes.GetStatus().GetDebugMessage())
			res.RespHeader.ErrorView = InvalidSearchStringArgumentErrorView
			return res, nil
		}
		logger.Error(ctx, "error in calling BE search company method", zap.Error(rpcErr))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(rpcErr.Error())
		res.RespHeader.ErrorView = DefaultErrorView
		return res, nil
	}
	companies := make([]*pb.CompanyInfoV2, 0)
	for _, company := range beRes.Companies {
		companies = append(companies, &pb.CompanyInfoV2{
			Id:                 company.GetEmployerId(),
			LegalName:          company.GetNameBySource(),
			TradeName:          company.GetTradeName(),
			IsVerifiedEmployer: company.GetIsVerified(),
		})
	}
	logger.Info(ctx, "successfully returning company suggestions V2", zap.Int("suggestionCount", len(companies)))
	res.Companies = companies
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) processEmploymentData(ctx context.Context, req *pb.ProcessEmploymentDataRequest) (*pb.ProcessEmploymentDataResponse, error) {
	var (
		employmentType = req.GetEmploymentType()
		proofType      = req.GetEmploymentProofType()
		err            error
	)
	logger.Info(ctx, "started ProcessEmploymentData for v2", zap.String("empType", employmentType.String()),
		zap.String("proofType", proofType.String()))

	// This flow is a part on screener 2, where the user just selects the employer,
	// and we force perform EPFO check
	updateSource := employmentPb.UpdateSource(employmentPb.UpdateSource_value[req.GetUpdateSource()])
	if updateSource == employmentPb.UpdateSource_UPDATE_SOURCE_EPFO_SCREEN {
		return s.performEPFOCheck(ctx, req)
	}

	if employmentType == uiStatePb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		logger.Info(ctx, "employment type unspecified")
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("employment type unspecified"),
			},
		}, nil
	}

	// From older device we only get salary range, updating salary for those cases
	annualSalary := req.GetAnnualSalary()
	if annualSalary == nil {
		annualSalary = &pb.AnnualSalary{
			Range: req.GetAnnualSalaryRange(),
		}
	}

	beReq := &employmentPb.ProcessEmploymentDataRequest{
		ActorId:               req.GetReq().GetAuth().GetActorId(),
		EmploymentType:        getBeEmploymentType(req.EmploymentType),
		EmploymentInfoOptions: nil,
		AnnualSalary:          annualSalary,
		OccupationType:        getOccupationFromString(ctx, req.GetOccupationType()),
	}

	beReq, err = populateEmploymentInfo(ctx, req, beReq)
	if err != nil {
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg(err.Error()),
			},
		}, nil
	}

	beRes, err := s.EmploymentClient.ProcessEmploymentData(ctx, beReq)
	if empErr := epifigrpc.RPCError(beRes, err); empErr != nil && !beRes.GetStatus().IsAlreadyExists() {
		if beRes.GetStatus().IsInvalidArgument() {
			logger.Info(ctx, "invalid argument in ProcessEmploymentData", zap.String("error message", beRes.GetStatus().GetDebugMessage()))
			return getProcessEmploymentDataResponseFromInvalidArgumentMessage(beRes.GetStatus())
		}
		logger.Info(ctx, "error calling ProcessEmploymentData", zap.Error(err))
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error calling BE ProcessEmploymentData"),
				ErrorView: DefaultErrorView,
			},
		}, nil
	}

	_ = s.updateUserDetails(ctx, req)

	return &pb.ProcessEmploymentDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: processEmploymentUpdateSrcToDeeplinkMap[employmentPb.UpdateSource(employmentPb.UpdateSource_value[req.GetUpdateSource()])],
	}, nil
}

func getProcessEmploymentDataResponseFromInvalidArgumentMessage(status *rpc.Status) (*pb.ProcessEmploymentDataResponse, error) {
	switch {
	case strings.EqualFold(status.GetDebugMessage(), "employment type unspecified"):
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: EmploymentTypeNotMentionedErrorView,
			},
		}, nil
	case strings.EqualFold(status.GetDebugMessage(), "occupation type not mentioned"):
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: OccupationNotMentionedErrorView,
			},
		}, nil
	default:
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: DefaultErrorView,
			},
		}, nil
	}
}

func (s *Service) updateUserDetails(ctx context.Context, req *pb.ProcessEmploymentDataRequest) error {
	var userFieldMask []user.UserFieldMask
	// get user id from actor id
	actorRes, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: req.GetReq().GetAuth().GetActorId()})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		logger.Error(ctx, "failed to get actor from id", zap.Error(te))
		return te
	}

	resp, errResp := s.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: actorRes.GetActor().GetEntityId(),
		},
	})
	if err = epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching user from actor id", zap.Error(err))
		return err
	}
	userDetails := resp.GetUser()
	userProfileDetails := userDetails.GetProfile()
	if userProfileDetails == nil {
		userProfileDetails = &user.Profile{}
	}

	// for older device we will receive annual salary range
	// for upgraded one we will receive absolute value or range in annual salary
	userSalaryRange := &user.SalaryRange{
		MinValue: req.GetAnnualSalaryRange().GetMinValue(),
		MaxValue: req.GetAnnualSalaryRange().GetMaxValue(),
	}

	// preference is given to annual salary
	if req.AnnualSalary != nil {
		userSalaryRange = &user.SalaryRange{
			MinValue: req.GetAnnualSalary().GetRange().GetMinValue(),
			MaxValue: req.GetAnnualSalary().GetRange().GetMaxValue(),
		}
		// if we receive only absolute value from client
		if req.AnnualSalary.Range == nil {
			userSalaryRange = getSalaryRange(req.GetAnnualSalary().GetAbsolute())
		}
	}
	userProfileDetails.SalaryRange = userSalaryRange
	userFieldMask = append(userFieldMask, user.UserFieldMask_SALARY_RANGE)

	switch {
	case len(req.GetQualification()) > 0:
		// Remove line breaks before converting to enum format
		cleanedQualification := strings.ReplaceAll(req.GetQualification(), "\n", " ")
		cleanedQualification = strings.ReplaceAll(cleanedQualification, " ", "_")
		userProfileDetails.Qualification = types.Qualification(types.Qualification_value[cleanedQualification])
		userFieldMask = append(userFieldMask, user.UserFieldMask_QUALIFICATION)
	case len(req.GetSourceOfFunds()) > 0:
		cleanedSourceOfFunds := strings.ReplaceAll(req.GetSourceOfFunds(), "\n", " ")
		cleanedSourceOfFunds = strings.ReplaceAll(cleanedSourceOfFunds, " ", "_")
		userProfileDetails.SourceOfFunds = types.SourceOfFunds(types.SourceOfFunds_value[cleanedSourceOfFunds])
		userFieldMask = append(userFieldMask, user.UserFieldMask_SOURCE_OF_FUNDS)
	case req.GetAnnualTransactionVolume() != nil:
		annualTransactionVolumeRange := &user.SalaryRange{
			MinValue: req.GetAnnualTransactionVolume().GetMinValue(),
			MaxValue: req.GetAnnualTransactionVolume().GetMaxValue(),
		}
		userProfileDetails.AnnualTransactionVolume = annualTransactionVolumeRange
		userFieldMask = append(userFieldMask, user.UserFieldMask_ANNUAL_TRANSACTION_VOLUME)
	}

	updateUserRes, err := s.usersClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User: &user.User{
			Id:      actorRes.GetActor().GetEntityId(),
			Profile: userProfileDetails,
		},
		UpdateMask: userFieldMask,
	})

	if te := epifigrpc.RPCError(updateUserRes, err); te != nil {
		logger.Error(ctx, "failed to update annual income of user", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) handleIncomeOccupationDiscrepancy(ctx context.Context, req *pb.ProcessEmploymentDataRequest) (*pb.ProcessEmploymentDataResponse, error) {
	// keeping it to large value to avoid cases in case employment type not found
	var maxNormalIncome int64 = 10000000000
	for _, employmentInfo := range s.frontendConfig.UserProfileView.ProfileEmploymentUpdateView.EmploymentTypeUiView.EmploymentTypes {
		if uiStatePb.EmploymentType(uiStatePb.EmploymentType_value[employmentInfo.Type]) == req.GetEmploymentType() {
			maxNormalIncome = employmentInfo.MaxNormalIncome
			logger.Info(ctx, fmt.Sprintf("Max normal income for employment type %v, %v", req.GetEmploymentType(), maxNormalIncome))
			break
		}
	}
	isIncOccDisparityExist := employment.CheckIfDiscrepancyExist(maxNormalIncome, int64(req.GetAnnualSalary().GetAbsolute()))
	if isIncOccDisparityExist && !req.GetIncomeDiscrepancyConsentGiven() {
		logger.Info(ctx, "User has not given consent for income and occupation discrepancy")
		return &pb.ProcessEmploymentDataResponse{
			RespHeader: header2.InlineErrResp(ErrViewStatus, fmt.Sprint(rpc.StatusInvalidArgument()), IncOccDiscrepancyConsentNotExist),
		}, fmt.Errorf("no consent given but discrepency exist")
	}
	if req.GetIncomeDiscrepancyConsentGiven() {
		logger.Info(ctx, "User has given consent for income and occupation discrepancy")
		recordConsentResp, recordConsentErr := s.consentClient.RecordConsent(ctx, &consentPb.RecordConsentRequest{
			ConsentType: consentPb.ConsentType_INCOME_OCCUPATION_DISCREPANCY,
			ActorId:     req.GetReq().GetAuth().GetActorId(),
			Device:      req.GetReq().GetAuth().GetDevice(),
			ExpiresAt:   timestampPb.New(time.Now().AddDate(0, 0, maxDaysForIncOccDiscrepancy)),
			Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
		})
		if te := epifigrpc.RPCError(recordConsentResp, recordConsentErr); te != nil {
			logger.Error(ctx, "Error in record consent for income occupation discrepancy", zap.Error(te))
			return &pb.ProcessEmploymentDataResponse{
				RespHeader: header2.InlineErrResp(ErrViewStatus, fmt.Sprint(rpc.StatusInvalidArgument()), IncOccDiscrepancyConsentBEError),
			}, fmt.Errorf("error in record consent")
		}
	}
	return &pb.ProcessEmploymentDataResponse{}, nil
}

func getOccupationFromString(ctx context.Context, occ string) employmentPb.OccupationType {
	occType := employmentPb.OccupationType(employmentPb.OccupationType_value[occ])
	if occType == employmentPb.OccupationType_OCCUPATION_TYPE_UNSPECIFIED {
		logger.Error(ctx, fmt.Sprintf("invalid occupation type is mentioned in request: %v", occ))
	}
	return occType
}
