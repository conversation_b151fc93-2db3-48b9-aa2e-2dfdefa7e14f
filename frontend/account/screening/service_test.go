package screening

import (
	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/actor"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/employment"
	mockEmp "github.com/epifi/gamma/api/employment/mocks"
	pb "github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/frontend/account/screening/uistate"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	screenerPb "github.com/epifi/gamma/api/screener"
	scrnrMock "github.com/epifi/gamma/api/screener/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	userMock "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onbMock "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/frontend/config"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
)

func Test_getSalaryRange(t *testing.T) {
	type args struct {
		salary float32
	}
	tests := []struct {
		name string
		args args
		want *user.SalaryRange
	}{
		{
			name: "value in range",
			args: args{
				salary: 1023,
			},
			want: &user.SalaryRange{
				MinValue: 0,
				MaxValue: 100000,
			},
		},
		{
			name: "value in negative",
			args: args{
				salary: -1023,
			},
			want: &user.SalaryRange{
				MinValue: 0,
				MaxValue: 0,
			},
		},
		{
			name: "value out of max salary range",
			args: args{
				salary: 2500000432,
			},
			want: &user.SalaryRange{
				MinValue: 0,
				MaxValue: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSalaryRange(tt.args.salary); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSalaryRange() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetCompanyNamesV2(t *testing.T) {
	type test struct {
		name  string
		req   *pb.GetCompanyNamesRequestV2
		res   *pb.GetCompanyNamesResponseV2
		mocks func(client *mockEmp.MockEmploymentClient)
	}

	var (
		companiesFe = []*pb.CompanyInfoV2{
			{
				Id:                 "id1",
				LegalName:          "legalName1",
				TradeName:          "tradeName1",
				IsVerifiedEmployer: true,
			},
		}
		companiesBe = []*employment.EmployerInfo{
			{
				NameBySource:    "legalName1",
				TradeName:       "tradeName1",
				IsVerified:      true,
				IsEpfRegistered: true,
				EmployerId:      "id1",
			},
		}
	)

	tests := []test{
		{
			name: "success get companies by search",
			req: &pb.GetCompanyNamesRequestV2{
				Req:        &header.RequestHeader{},
				NamePrefix: "companyName1",
			},
			res: &pb.GetCompanyNamesResponseV2{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				Companies:  companiesFe,
			},
			mocks: func(client *mockEmp.MockEmploymentClient) {
				client.EXPECT().SearchCompanyV2(gomock.Any(), &employment.SearchCompanyRequestV2{SearchString: "companyName1", Provenance: employment.SearchCompanyRequestV2_PROFILE}).
					Return(&employment.SearchCompanyResponseV2{
						Status:    rpc.StatusOk(),
						Companies: companiesBe,
					}, nil)
			},
		},
		{
			name: "Invalid argument for search string",
			req: &pb.GetCompanyNamesRequestV2{
				Req:        &header.RequestHeader{},
				NamePrefix: "***",
			},
			res: &pb.GetCompanyNamesResponseV2{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInvalidArgumentWithDebugMsg("debug message"),
					ErrorView: errors2.NewBottomSheetErrorView("SCRNING0013", "Request Failed", "",
						"Looks like company name search has invalid characters. Can you try with valid company name")},
			},
			mocks: func(client *mockEmp.MockEmploymentClient) {
				client.EXPECT().SearchCompanyV2(gomock.Any(), &employment.SearchCompanyRequestV2{SearchString: "***", Provenance: employment.SearchCompanyRequestV2_PROFILE}).
					Return(&employment.SearchCompanyResponseV2{
						Status: rpc.StatusInvalidArgumentWithDebugMsg("debug message"),
					}, nil)
			},
		},
	}

	ctx := context.Background()
	for _, tt := range tests {

		ctrl := gomock.NewController(t)
		empMockClient := mockEmp.NewMockEmploymentClient(ctrl)
		s := &Service{EmploymentClient: empMockClient}

		t.Run(tt.name, func(t *testing.T) {
			if tt.mocks != nil {
				tt.mocks(empMockClient)
			}
			res, err := s.GetCompanyNamesV2(ctx, tt.req)
			if err != nil {
				t.Error("failed to get company names", zap.Error(err))
			}
			if !proto.Equal(res, tt.res) {
				t.Errorf("got: %v, want: %v", res, tt.res)
			}
		})
	}
}

func TestService_processEmploymentData(t *testing.T) {

	retiredUserFix1 := &employment.ProcessEmploymentDataRequest{
		EmploymentType: employment.EmploymentType_RETIRED,
		EmploymentInfoOptions: &employment.ProcessEmploymentDataRequest_PersonalProfileInfo{
			PersonalProfileInfo: &employment.PersonalProfileOption{
				PersonalProfileInfo: "<EMAIL>",
			},
		},
		AnnualSalary: &pb.AnnualSalary{
			Absolute: 1234,
		},
		UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_ONBOARDING,
		OccupationType: employment.OccupationType_OCCUPATION_TYPE_RETIRED,
	}

	actorRes := &actor.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor: &typesPb.Actor{
			EntityId: "232-321-32-13-2131",
		},
	}

	userSalaryFix1 := &user.UpdateUserRequest{
		User: &user.User{
			Id: actorRes.GetActor().GetEntityId(),
			Profile: &user.Profile{
				SalaryRange: &user.SalaryRange{
					MaxValue: 100000,
				},
			},
		},
		UpdateMask: []user.UserFieldMask{user.UserFieldMask_SALARY_RANGE},
	}

	getUserReq := &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: actorRes.GetActor().GetEntityId(),
		},
	}

	userDetails := &user.User{
		Profile: &user.Profile{
			SalaryRange: &user.SalaryRange{
				MaxValue: 100000,
			},
		},
	}

	salaryUserFix2 := &employment.ProcessEmploymentDataRequest{
		EmploymentType: employment.EmploymentType_SALARIED,
		AnnualSalary: &pb.AnnualSalary{
			Absolute: 1234,
		},
		UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_USER_PROFILE,
		OccupationType: employment.OccupationType_OCCUPATION_TYPE_ENGINEERING,
	}

	ctrl := gomock.NewController(t)
	empMockClient := mockEmp.NewMockEmploymentClient(ctrl)
	mockActorClient := actorMock.NewMockActorClient(ctrl)
	mockUserClient := userMock.NewMockUsersClient(ctrl)

	type args struct {
		ctx context.Context
		req *pb.ProcessEmploymentDataRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *pb.ProcessEmploymentDataResponse
		wantErr   bool
		setupMock func()
	}{
		{
			name: "Employment type is unspecified",
			args: args{
				ctx: context.Background(),
				req: &pb.ProcessEmploymentDataRequest{
					EmploymentType: uistate.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED,
				},
			},
			wantErr: false,
			want: &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("employment type unspecified"),
				},
			},
		},
		{
			name: "Employment type is retired",
			args: args{
				ctx: context.Background(),
				req: &pb.ProcessEmploymentDataRequest{
					EmploymentType: uistate.EmploymentType_RETIRED,
					Input: &pb.ProcessEmploymentDataRequest_PersonalInfo{
						PersonalInfo: &pb.PersonalProfileInfo{
							Url: "<EMAIL>",
						},
					},
					UpdateSource: "UPDATE_SOURCE_ONBOARDING",
					AnnualSalary: &pb.AnnualSalary{
						Absolute: 1234,
					},
					OccupationType: employment.OccupationType_OCCUPATION_TYPE_RETIRED.String(),
				},
			},
			wantErr: false,
			want: &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_EMPLOYMENT_VERIFICATION_STATUS,
				},
			},
			setupMock: func() {
				empMockClient.EXPECT().ProcessEmploymentData(context.Background(), retiredUserFix1).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActorClient.EXPECT().GetActorById(context.Background(), &actor.GetActorByIdRequest{}).Return(actorRes, nil)
				mockUserClient.EXPECT().GetUser(context.Background(), getUserReq).Return(&user.GetUserResponse{Status: rpc.StatusOk(), User: userDetails}, nil)
				mockUserClient.EXPECT().UpdateUser(context.Background(), userSalaryFix1).Return(&user.UpdateUserResponse{Status: rpc.StatusOk()}, nil)
			},
		},
		{
			name: "Employment type salaried with empty company object",
			args: args{
				ctx: context.Background(),
				req: &pb.ProcessEmploymentDataRequest{
					EmploymentType: uistate.EmploymentType_SALARIED,
					Input: &pb.ProcessEmploymentDataRequest_CompanyInfo{
						CompanyInfo: &pb.CompanyInfo{},
					},
					UpdateSource: "UPDATE_SOURCE_USER_PROFILE",
					AnnualSalary: &pb.AnnualSalary{
						Absolute: 1234,
					},
					OccupationType: employment.OccupationType_OCCUPATION_TYPE_ENGINEERING.String(),
				},
			},
			wantErr: false,
			want: &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
			},
			setupMock: func() {
				empMockClient.EXPECT().ProcessEmploymentData(context.Background(), salaryUserFix2).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActorClient.EXPECT().GetActorById(context.Background(), &actor.GetActorByIdRequest{}).Return(actorRes, nil)
				mockUserClient.EXPECT().GetUser(context.Background(), getUserReq).Return(&user.GetUserResponse{Status: rpc.StatusOk(), User: userDetails}, nil)
				mockUserClient.EXPECT().UpdateUser(context.Background(), userSalaryFix1).Return(&user.UpdateUserResponse{Status: rpc.StatusOk()}, nil)
			},
		},
		{
			name: "Employment type salaried with empty employer object",
			args: args{
				ctx: context.Background(),
				req: &pb.ProcessEmploymentDataRequest{
					EmploymentType: uistate.EmploymentType_SALARIED,
					Input: &pb.ProcessEmploymentDataRequest_EmployerInfo{
						EmployerInfo: &pb.UserSelectedEmployerInfo{},
					},
					UpdateSource: "UPDATE_SOURCE_USER_PROFILE",
					AnnualSalary: &pb.AnnualSalary{
						Absolute: 1234,
					},
					OccupationType: employment.OccupationType_OCCUPATION_TYPE_ENGINEERING.String(),
				},
			},
			wantErr: false,
			want: &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
			},
			setupMock: func() {
				empMockClient.EXPECT().ProcessEmploymentData(context.Background(), salaryUserFix2).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockActorClient.EXPECT().GetActorById(context.Background(), &actor.GetActorByIdRequest{}).Return(actorRes, nil)
				mockUserClient.EXPECT().GetUser(context.Background(), getUserReq).Return(&user.GetUserResponse{Status: rpc.StatusOk(), User: userDetails}, nil)
				mockUserClient.EXPECT().UpdateUser(context.Background(), userSalaryFix1).Return(&user.UpdateUserResponse{Status: rpc.StatusOk()}, nil)
			},
		},
		{
			name: "BE return invalid argument",
			args: args{
				ctx: context.Background(),
				req: &pb.ProcessEmploymentDataRequest{
					EmploymentType: uistate.EmploymentType_RETIRED,
					Input: &pb.ProcessEmploymentDataRequest_PersonalInfo{
						PersonalInfo: &pb.PersonalProfileInfo{
							Url: "<EMAIL>",
						},
					},
					UpdateSource: "UPDATE_SOURCE_ONBOARDING",
					AnnualSalary: &pb.AnnualSalary{
						Absolute: 1234,
					},
				},
			},
			wantErr: false,
			want: &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInvalidArgumentWithDebugMsg("occupation type not mentioned"),
					ErrorView: OccupationNotMentionedErrorView,
				},
			},
			setupMock: func() {
				empMockClient.EXPECT().ProcessEmploymentData(context.Background(), &employment.ProcessEmploymentDataRequest{
					EmploymentType: employment.EmploymentType_RETIRED,
					EmploymentInfoOptions: &employment.ProcessEmploymentDataRequest_PersonalProfileInfo{
						PersonalProfileInfo: &employment.PersonalProfileOption{
							PersonalProfileInfo: "<EMAIL>",
						},
					},
					AnnualSalary: &pb.AnnualSalary{
						Absolute: 1234,
					},
					UpdateSource: employment.UpdateSource_UPDATE_SOURCE_ONBOARDING,
				}).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("occupation type not mentioned"),
				}, nil)
			},
		},
		{
			name: "Be return already exist",
			args: args{
				ctx: context.Background(),
				req: &pb.ProcessEmploymentDataRequest{
					EmploymentType: uistate.EmploymentType_RETIRED,
					Input: &pb.ProcessEmploymentDataRequest_PersonalInfo{
						PersonalInfo: &pb.PersonalProfileInfo{
							Url: "<EMAIL>",
						},
					},
					UpdateSource: "UPDATE_SOURCE_ONBOARDING",
					AnnualSalary: &pb.AnnualSalary{
						Absolute: 1234,
					},
					OccupationType: employment.OccupationType_OCCUPATION_TYPE_RETIRED.String(),
				},
			},
			wantErr: false,
			want: &pb.ProcessEmploymentDataResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_EMPLOYMENT_VERIFICATION_STATUS,
				},
			},
			setupMock: func() {
				empMockClient.EXPECT().ProcessEmploymentData(context.Background(), retiredUserFix1).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil)
				mockActorClient.EXPECT().GetActorById(context.Background(), &actor.GetActorByIdRequest{}).Return(actorRes, nil)
				mockUserClient.EXPECT().GetUser(context.Background(), getUserReq).Return(&user.GetUserResponse{Status: rpc.StatusOk(), User: userDetails}, nil)
				mockUserClient.EXPECT().UpdateUser(context.Background(), userSalaryFix1).Return(&user.UpdateUserResponse{Status: rpc.StatusOk()}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setupMock != nil {
				tt.setupMock()
			}
			s := &Service{
				EmploymentClient: empMockClient,
				actorClient:      mockActorClient,
				usersClient:      mockUserClient,
				genConf:          genConf,
				frontendConfig:   conf,
			}
			got, err := s.ProcessEmploymentData(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("processEmploymentDataV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("processEmploymentDataV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_CheckCreditReportVerificationStatus(t *testing.T) {
	actorId := "actor-id"
	type mockStruct struct {
		Conf        *config.Screening
		OnbClient   *onbMock.MockOnboardingClient
		scrnrClient *scrnrMock.MockScreenerClient
	}
	type args struct {
		ctx     context.Context
		request *pb.CheckCreditReportVerificationStatusRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(*mockStruct)
		want       *pb.CheckCreditReportVerificationStatusResponse
		wantErr    error
	}{
		{
			name: "verification failed",
			args: args{
				ctx: context.Background(),
				request: &pb.CheckCreditReportVerificationStatusRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			setupMocks: func(m *mockStruct) {
				m.OnbClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{
					ActorId: actorId,
				}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_SEND_WORK_EMAIL_OTP,
					},
				}, nil)
				m.scrnrClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId:    actorId,
					CachedData: true,
				}).Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusOk(),
					ChecksMap: []*screenerPb.CheckDetails{
						{
							CheckType:   screenerPb.CheckType_CHECK_TYPE_CREDIT_REPORT,
							CheckResult: screenerPb.CheckResult_CHECK_RESULT_FAILED,
							RetriesLeft: 0,
						},
					},
				}, nil)
			},
			want: &pb.CheckCreditReportVerificationStatusResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_SEND_WORK_EMAIL_OTP,
				},
				NextPollAfter:                  5,
				CreditReportVerificationStatus: pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_FAILED,
				TransitionTitleText:            workStatusVerificationFailure,
				TransitionSubtitleText:         workStatusVerificationFailureSubtitle,
			},
			wantErr: nil,
		},
		{
			name: "verification in-progress",
			args: args{
				ctx: context.Background(),
				request: &pb.CheckCreditReportVerificationStatusRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			setupMocks: func(m *mockStruct) {
				m.OnbClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{
					ActorId: actorId,
				}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_CREDIT_REPORT_VERIFICATION_STATUS,
					},
				}, nil)
				m.scrnrClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId:    actorId,
					CachedData: true,
				}).Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusOk(),
					ChecksMap: []*screenerPb.CheckDetails{
						{
							CheckType:   screenerPb.CheckType_CHECK_TYPE_CREDIT_REPORT,
							CheckResult: screenerPb.CheckResult_CHECK_RESULT_FAILED,
							RetriesLeft: 0,
						},
					},
				}, nil)
			},
			want: &pb.CheckCreditReportVerificationStatusResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_REPORT_VERIFICATION_STATUS,
				},
				NextPollAfter:                  5,
				CreditReportVerificationStatus: pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_IN_PROGRESS,
				TransitionTitleText:            creditReportVerificationInProgress,
				TransitionSubtitleText:         "",
			},
			wantErr: nil,
		},
		{
			name: "verification passed using credit report check",
			args: args{
				ctx: context.Background(),
				request: &pb.CheckCreditReportVerificationStatusRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			setupMocks: func(m *mockStruct) {
				m.OnbClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{
					ActorId: actorId,
				}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_PARENTS_NAME_GETTER,
					},
				}, nil)
				m.scrnrClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId:    actorId,
					CachedData: true,
				}).Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusOk(),
					ChecksMap: []*screenerPb.CheckDetails{
						{
							CheckType:   screenerPb.CheckType_CHECK_TYPE_CREDIT_REPORT,
							CheckResult: screenerPb.CheckResult_CHECK_RESULT_PASSED,
							RetriesLeft: 0,
						},
					},
				}, nil)
			},
			want: &pb.CheckCreditReportVerificationStatusResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_PARENTS_NAME_GETTER,
				},
				NextPollAfter:                  5,
				CreditReportVerificationStatus: pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL,
				TransitionTitleText:            cbSuccessEmploymentDeclaration,
				TransitionSubtitleText:         "",
			},
			wantErr: nil,
		},
		{
			name: "verification passed using income estimate check",
			args: args{
				ctx: context.Background(),
				request: &pb.CheckCreditReportVerificationStatusRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			setupMocks: func(m *mockStruct) {
				m.OnbClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{
					ActorId: actorId,
				}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_PARENTS_NAME_GETTER,
					},
				}, nil)
				m.scrnrClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId:    actorId,
					CachedData: true,
				}).Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusOk(),
					ChecksMap: []*screenerPb.CheckDetails{
						{
							CheckType:   screenerPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE,
							CheckResult: screenerPb.CheckResult_CHECK_RESULT_PASSED,
							RetriesLeft: 0,
						},
					},
				}, nil)
			},
			want: &pb.CheckCreditReportVerificationStatusResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_PARENTS_NAME_GETTER,
				},
				NextPollAfter:                  5,
				CreditReportVerificationStatus: pb.CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL,
				TransitionTitleText:            cbSuccessEmploymentDeclaration,
				TransitionSubtitleText:         "",
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			onbClient := onbMock.NewMockOnboardingClient(ctr)
			scrnrClient := scrnrMock.NewMockScreenerClient(ctr)

			if tt.setupMocks != nil {
				tt.setupMocks(&mockStruct{
					Conf:        conf.Screening,
					OnbClient:   onbClient,
					scrnrClient: scrnrClient,
				})
			}

			s := NewService(nil, conf.Screening, onbClient, nil, nil, nil, scrnrClient, nil, nil, nil, nil)
			got, err := s.CheckCreditReportVerificationStatus(tt.args.ctx, tt.args.request)
			if err != tt.wantErr {
				t.Errorf("CheckCreditReportVerificationStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// ignore polling response info in tests
			got.PollingResponseInfo = tt.want.GetPollingResponseInfo()
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckCreditReportVerificationStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
