package display_names

import (
	"fmt"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
)

var (
	regularDisplayString     = "REGULAR"
	basicDisplayString       = "STANDARD"
	plusDisplayString        = "PLUS"
	infiniteDisplayString    = "INFINITE"
	salaryDisplayString      = "SALARY"
	salaryBasicDisplayString = "SALARY BASIC"
	salaryLiteDisplayString  = "SALARY LITE"
	aaSalaryDisplayString    = "PRIME"
)

func GetCapitalCaseDisplayString(tier beTieringExtPb.Tier) (string, error) {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return basicDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return plusDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return infiniteDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return salaryDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return salaryDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return salaryLiteDisplayString, nil
	default:
		if tier.IsAaSalaryTier() {
			return aaSalaryDisplayString, nil
		}
		return "", fmt.Errorf("%s tier is not handled for display strings", tier.String())
	}
}

func GetTitleCaseDisplayString(tier beTieringExtPb.Tier) (string, error) {
	caser := cases.Title(language.English)
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return caser.String(regularDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return caser.String(basicDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return caser.String(plusDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return caser.String(infiniteDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return caser.String(salaryDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return caser.String(salaryLiteDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return caser.String(salaryBasicDisplayString), nil
	default:
		if tier.IsAaSalaryTier() {
			return caser.String(aaSalaryDisplayString), nil
		}
		return "", fmt.Errorf("%s tier is not handled for display strings", tier.String())
	}
}
