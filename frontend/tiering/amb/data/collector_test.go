package data

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feDePb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	feDeMocksPb "github.com/epifi/gamma/api/frontend/dynamic_elements/mocks"
	"github.com/epifi/gamma/api/frontend/header"
)

func TestAMBDataCollectorImpl_fetchDynamicBanner(t *testing.T) {
	ctx := context.Background()
	actorID := "test-actor-id"
	req := &header.RequestHeader{
		Auth: &header.AuthHeader{
			ActorId: actorID,
		},
	}

	tests := []struct {
		name           string
		setupMock      func(client *feDeMocksPb.MockDynamicElementsClient)
		expectedBanner bool
		expectedError  bool
	}{
		{
			name: "Success - Banner found",
			setupMock: func(mockClient *feDeMocksPb.MockDynamicElementsClient) {

				elementsList := []*feDePb.DynamicElement{
					{
						StructureType: feDePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
						Content: &feDePb.ElementContent{
							Content: &feDePb.ElementContent_BannerV2{
								BannerV2: &feDePb.BannerElementContentV2{
									Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PAY_QR_SCREEN},
									Image: &commontypes.Image{
										ImageType: commontypes.ImageType_PNG,
										ImageUrl:  "https://epifi-icons.pointz.in/tiering/tier_all_plans_v2_entry_banner_image.png",
									},
								},
							},
						},
					},
				}
				response := &feDePb.FetchDynamicElementsResponse{
					ElementsList: elementsList,
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
				}

				mockClient.EXPECT().
					FetchDynamicElements(gomock.Any(), gomock.Any()).
					Return(response, nil)
			},
			expectedBanner: true,
			expectedError:  false,
		},
		{
			name: "Success - No banner elements found",
			setupMock: func(mockClient *feDeMocksPb.MockDynamicElementsClient) {
				response := &feDePb.FetchDynamicElementsResponse{
					ElementsList: []*feDePb.DynamicElement{},
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
				}

				mockClient.EXPECT().
					FetchDynamicElements(gomock.Any(), gomock.Any()).
					Return(response, nil)
			},
			expectedBanner: false,
			expectedError:  false,
		},
		{
			name: "Success - Element with wrong structure type",
			setupMock: func(mockClient *feDeMocksPb.MockDynamicElementsClient) {
				element := &feDePb.DynamicElement{
					StructureType: feDePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
				}

				response := &feDePb.FetchDynamicElementsResponse{
					ElementsList: []*feDePb.DynamicElement{element},
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
				}

				mockClient.EXPECT().
					FetchDynamicElements(gomock.Any(), gomock.Any()).
					Return(response, nil)
			},
			expectedBanner: false,
			expectedError:  false,
		},
		{
			name: "Error - Failed to fetch dynamic elements",
			setupMock: func(mockClient *feDeMocksPb.MockDynamicElementsClient) {
				response := &feDePb.FetchDynamicElementsResponse{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
				}

				mockClient.EXPECT().
					FetchDynamicElements(gomock.Any(), gomock.Any()).
					Return(response, nil)
			},
			expectedBanner: false,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup controller and mock
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			dynamicElementsClientMock := feDeMocksPb.NewMockDynamicElementsClient(ctrl)
			tt.setupMock(dynamicElementsClientMock)

			// Create the data collector with mock
			collector := &AMBDataCollectorImpl{
				dynamicElementsClient: dynamicElementsClientMock,
			}

			// Call the function being tested
			banner, err := collector.fetchDynamicBanner(ctx, req)

			// Assert error
			if tt.expectedError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			// Assert banner
			if tt.expectedBanner {
				assert.NotNil(t, banner)
				assert.NotNil(t, banner.Image)
				assert.NotNil(t, banner.Deeplink)
			} else {
				assert.Nil(t, banner)
			}
		})
	}
}
