package lenden

import (
	"net/http"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/user"
	lendenVgClient "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/lenden"
	"github.com/epifi/gamma/preapprovedloan/helper"
	lendenPkg "github.com/epifi/gamma/preapprovedloan/pkg/lenden"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

type Processor struct {
	loanRequestDao                 dao.LoanRequestsDao
	loanStepExecutionDao           dao.LoanStepExecutionsDao
	loanOfferDao                   dao.LoanOffersDao
	loecDao                        dao.LoanOfferEligibilityCriteriaDao
	rpcHelper                      *helper.RpcHelper
	loanAccountDao                 dao.LoanAccountsDao
	loanApplicantDao               dao.LoanApplicantDao
	lendenVgClient                 lendenVgClient.LendenClient
	loanActivityDao                dao.LoanActivityDao
	loanInstallmentInfoDao         dao.LoanInstallmentInfoDao
	deeplinkFactory                *deeplink.ProviderFactory
	lendenDlProvider               *lenden.Provider // TODO(mohitswain): remove this in future PR
	txnExecutorProvider            *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	httpClient                     *http.Client
	s3Client                       s3.S3Client
	connectedSalaryAccountProvider *helper.ConnectedSalaryAccountProvider
	consentClient                  consentPb.ConsentClient
	loanPaymentRequestDao          dao.LoanPaymentRequestsDao
	userClient                     user.UsersClient
	ldcMandateSetupProcessor       *lendenPkg.MandateSetupProcessor
}

func NewProcessor(
	loanRequestDao dao.LoanRequestsDao,
	loanOfferDao dao.LoanOffersDao,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	rpcHelper *helper.RpcHelper,
	loanAccountDao dao.LoanAccountsDao,
	loanApplicantDao dao.LoanApplicantDao,
	lendenVgClient lendenVgClient.LendenClient,
	loanActivityDao dao.LoanActivityDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	deeplinkFactory *deeplink.ProviderFactory,
	lendenDlProvider *lenden.Provider,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	loecDao dao.LoanOfferEligibilityCriteriaDao,
	httpClient *http.Client,
	s3Client types2.PreApprovedLoanS3Client,
	connectedSalaryAccountProvider *helper.ConnectedSalaryAccountProvider,
	consentClient consentPb.ConsentClient,
	loanPaymentRequestDao dao.LoanPaymentRequestsDao,
	userClient user.UsersClient,
	ldcMandateSetupProcessor *lendenPkg.MandateSetupProcessor,
) *Processor {
	return &Processor{
		loanRequestDao:                 loanRequestDao,
		loanOfferDao:                   loanOfferDao,
		loanStepExecutionDao:           loanStepExecutionDao,
		rpcHelper:                      rpcHelper,
		loanAccountDao:                 loanAccountDao,
		loanApplicantDao:               loanApplicantDao,
		lendenVgClient:                 lendenVgClient,
		loanActivityDao:                loanActivityDao,
		loanInstallmentInfoDao:         loanInstallmentInfoDao,
		deeplinkFactory:                deeplinkFactory,
		lendenDlProvider:               lendenDlProvider,
		txnExecutorProvider:            txnExecutorProvider,
		loecDao:                        loecDao,
		httpClient:                     httpClient,
		s3Client:                       s3Client,
		connectedSalaryAccountProvider: connectedSalaryAccountProvider,
		consentClient:                  consentClient,
		loanPaymentRequestDao:          loanPaymentRequestDao,
		userClient:                     userClient,
		ldcMandateSetupProcessor:       ldcMandateSetupProcessor,
	}
}
