package lenden

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2/account"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	mandateHelper "github.com/epifi/gamma/preapprovedloan/mandate_manager/helper"
	"github.com/epifi/gamma/preapprovedloan/pkg/lenden"
	"github.com/epifi/gamma/preapprovedloan/utils"
)

type getMandateInitScreenRequest struct {
	vendor                 palPb.Vendor
	loanProgram            palPb.LoanProgram
	lse                    *palPb.LoanStepExecution
	offerConstraintAccount *palPb.AaAnalysisBankDetails
	savingsAccount         *palPb.MandateData_BankingDetails_AccountDetails
}

// Helper function to get the initiate mandate screen v2 deeplink
func (p *Processor) getMandateInitScreen(ctx context.Context, req *getMandateInitScreenRequest) (*deeplinkPb.Deeplink, error) {
	lg := activity.GetLogger(ctx)
	if req.offerConstraintAccount != nil {
		dl, err := p.getMandateInitScreenForOfferConstraintAccount(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "error getting mandate init screen for offer constraint account")
		}
		return dl, nil
	}
	var savingsBankAccountDetails *palPb.MandateData_BankingDetails_AccountDetails
	for _, accDetails := range req.lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails() {
		// TODO(Brijesh): Add logic to show a preferable bank account when we have multiple accounts
		savingsBankAccountDetails = accDetails
	}
	dlProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.vendor, LoanProgram: req.loanProgram})
	var addBankDl *deeplinkPb.Deeplink
	if req.savingsAccount == nil {
		var err error
		lg.Info("no savings account found, allowing user to add bank account")
		addBankDl, err = dlProvider.GetBankingDetailsScreenDeeplink(dlProvider.GetLoanHeader(), req.lse.GetRefId(), req.lse.GetId(), 0, 0, "")
		if err != nil {
			return nil, errors.Wrap(err, "error getting screen for adding savings bank account details")
		}
	}
	dl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, dlProvider, req.lse, savingsBankAccountDetails, true, addBankDl)
	if err != nil {
		return nil, errors.Wrap(err, "error creating mandate init screen")
	}
	return dl, nil
}

func (p *Processor) getMandateInitScreenForOfferConstraintAccount(ctx context.Context, req *getMandateInitScreenRequest) (*deeplinkPb.Deeplink, error) {
	lg := activity.GetLogger(ctx)
	var offerAndSavingsAccountMatched bool
	if len(req.offerConstraintAccount.GetAccountNumber()) < 4 {
		return nil, errors.Errorf("offer bank account number is less than 4 digits: %s", req.offerConstraintAccount.GetAccountNumber())
	}
	offerConstraintAccountSuffix := req.offerConstraintAccount.GetAccountNumber()[len(req.offerConstraintAccount.GetAccountNumber())-4:]
	if req.savingsAccount != nil {
		lg.Info("comparing offer constraint account with savings account")
		ifscMatch := req.offerConstraintAccount.GetIfsc() == req.savingsAccount.GetIfscCode()
		if len(req.savingsAccount.GetAccountNumber()) < 4 {
			return nil, errors.Errorf("savings account number is less than 4 digits: %s", req.savingsAccount.GetAccountNumber())
		}
		savingsAccountNumberSuffix := req.savingsAccount.GetAccountNumber()[len(req.savingsAccount.GetAccountNumber())-4:]
		accountSuffixMatch := offerConstraintAccountSuffix == savingsAccountNumberSuffix
		if ifscMatch && accountSuffixMatch {
			offerAndSavingsAccountMatched = true
		}
	}
	dlProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.vendor, LoanProgram: req.loanProgram})
	if offerAndSavingsAccountMatched {
		lg.Info("using matching savings account as user's bank account for mandate")
		dl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, dlProvider, req.lse, req.savingsAccount, false, nil)
		if err != nil {
			return nil, errors.Wrap(err, "error creating mandate init screen with matching savings account details")
		}
		return dl, nil
	}
	addBankDl, err := dlProvider.GetBankingDetailsScreenDeeplink(dlProvider.GetLoanHeader(), req.lse.GetRefId(), req.lse.GetId(), 0, 0, offerConstraintAccountSuffix)
	if err != nil {
		return nil, errors.Wrap(err, "error getting screen for adding offer constraint bank account details")
	}
	userBankDetails := &palPb.MandateData_BankingDetails_AccountDetails{
		AccountNumber:     req.offerConstraintAccount.GetAccountNumber(),
		AccountHolderName: req.offerConstraintAccount.GetAccountHolderName(),
		IfscCode:          req.offerConstraintAccount.GetIfsc(),
		BankName:          req.offerConstraintAccount.GetBankName(),
	}
	dl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, dlProvider, req.lse, userBankDetails, false, addBankDl)
	if err != nil {
		return nil, errors.Wrap(err, "error creating mandate init screen for offer constraint account details")
	}
	return dl, nil
}

func (p *Processor) LdcCheckMandateStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	execWorkRes, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		if lse.GetDetails().GetMandateData().GetMerchantTxnId() == "" {
			lg.Info("no tracking id found in mandate data")
			mandateInitScreenDl, err := p.checkMandateStatusWhenNoTrackingIdPresent(ctx, lse, req)
			if err != nil {
				if errors.Is(err, epifierrors.ErrAlreadyExists) {
					lg.Info("mandate setup already completed")
					return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
				}
				return nil, errors.Wrap(err, "error checking mandate status when no tracking id present")
			}
			return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: mandateInitScreenDl},
				epifitemporal.NewTransientError(errors.Errorf("mandate not initiated yet, keep sending mandate initiation screen"))
		}
		mandateInfo, err := p.ldcMandateSetupProcessor.GetMandateInfo(ctx, lse)
		if err != nil {
			return nil, errors.Wrap(err, "error getting latest mandate setup info")
		}
		if mandateInfo.IsCompleted {
			lg.Info("mandate setup completed")
			return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
		}
		lse = mandateInfo.Lse
		if lse.GetDetails().GetMandateData().GetUrl() == "" {
			lg.Error("no mandate setup URL found in mandate data")
			// TODO(Brijesh): Check and mark permanent error with special sub statuses if needed for better debugging
			return nil, epifitemporal.NewTransientError(errors.New("no mandate setup URL found in mandate data"))
		}
		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
		mandateWebViewScreenDl, err := deeplinkProvider.GetLoansMandateSetupScreen(ctx, deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), lse.GetId(), &provider.LoansMandateSetupScreenParams{
			WebViewParams: &provider.WebViewParams{EntryUrl: lse.GetDetails().GetMandateData().GetUrl()},
		})
		if err != nil {
			lg.Error("error generating mandate webview screen deeplink", zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error generating mandate webview screen deeplink"))
		}
		return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: mandateWebViewScreenDl},
			epifitemporal.NewTransientError(errors.Errorf("mandate setup not completed yet, keep asking for mandate status"))
	})
	return execWorkRes, err
}

func (p *Processor) checkMandateStatusWhenNoTrackingIdPresent(ctx context.Context, lse *palPb.LoanStepExecution, req *palActivityPb.PalActivityRequest) (*deeplinkPb.Deeplink, error) {
	lg := activity.GetLogger(ctx)
	alreadyCompleted, lse, err := p.checkAndUpdateLseIfMandateAlreadySetup(ctx, lse)
	if err != nil {
		lg.Error("error checking and updating lse if mandate already setup", zap.Error(err))
		return nil, errors.Wrap(err, "error checking and updating lse if mandate already setup")
	}
	if alreadyCompleted {
		return nil, errors.Wrap(epifierrors.ErrAlreadyExists, "mandate setup already completed")
	}
	offerConstraintAccount, err := p.getOfferConstraintAccount(ctx, lse.GetRefId())
	if err != nil {
		lg.Error("error getting offer bank account details", zap.Error(err))
		return nil, errors.Wrap(err, "error getting estimated salary bank account details")
	}
	lse, savingsAccount, err := p.storeSavingsAccountInLSE(ctx, lse)
	if err != nil {
		lg.Error("error storing savings account in LSE", zap.Error(err))
		return nil, errors.Wrap(err, "error storing savings bank account details")
	}
	mandateInitScreenDl, err := p.getMandateInitScreen(ctx, &getMandateInitScreenRequest{
		vendor:                 req.GetVendor(),
		loanProgram:            req.GetLoanProgram(),
		lse:                    lse,
		offerConstraintAccount: offerConstraintAccount,
		savingsAccount:         savingsAccount,
	})
	if err != nil {
		lg.Error("error getting initiate mandate screen", zap.Error(err))
		return nil, errors.Wrap(err, "error getting initiate mandate screen")
	}
	return mandateInitScreenDl, nil
}

func (p *Processor) checkAndUpdateLseIfMandateAlreadySetup(ctx context.Context, lse *palPb.LoanStepExecution) (bool, *palPb.LoanStepExecution, error) {
	initMandateRes, err := p.ldcMandateSetupProcessor.InitiateMandate(ctx, lse)
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting initiation info")
	}
	switch initMandateRes.Status {
	case lenden.MandateStatusAlreadyCompleted:
		if initMandateRes.Data.TrackingId == "" {
			return false, nil, errors.New("expected non-empty tracking id when e-NACH already completed")
		}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initMandateRes.Data)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating mandate")
		}
		return true, lse, nil
	case lenden.MandateStatusPreRequisitesPending, lenden.MandateStatusInProgress:
		return false, lse, nil
	default:
		return false, nil, errors.Errorf("unhandled mandate status: %s", initMandateRes.Status)
	}
}

func (p *Processor) updateLseWithMandateCompletionInfo(ctx context.Context, lse *palPb.LoanStepExecution, mandateData *lenden.MandateData) (*palPb.LoanStepExecution, error) {
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	if mandateData.TrackingId != "" {
		lse.GetDetails().GetMandateData().MerchantTxnId = mandateData.TrackingId
	}
	if mandateData.URL != "" {
		lse.GetDetails().GetMandateData().Url = mandateData.URL
	}
	if mandateData.URLValidity.AsTime() != time.Unix(0, 0) {
		lse.GetDetails().GetMandateData().MandateLinkExpiry = mandateData.URLValidity
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL
	err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating mandate status")
	}
	return lse, nil
}

func (p *Processor) getOfferConstraintAccount(ctx context.Context, loanReqId string) (*palPb.AaAnalysisBankDetails, error) {
	loanRequest, err := p.loanRequestDao.GetById(ctx, loanReqId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	loanOffer, err := p.loanOfferDao.GetById(ctx, loanRequest.GetOfferId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan offer")
	}
	return loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails(), nil
}

// LDC may or may not constrain a user to use a specific bank account
// based on the evaluation done by their underwriting policy.
// If the user is not constrained, they can use any bank account to set up EMI mandates.
// Such a user with an existing Fi-Federal savings account may use this account to set up EMI mandates
func (p *Processor) storeSavingsAccountInLSE(ctx context.Context, lse *palPb.LoanStepExecution) (*palPb.LoanStepExecution, *palPb.MandateData_BankingDetails_AccountDetails, error) {
	lg := activity.GetLogger(ctx)
	acc, err := p.rpcHelper.GetSavingsAccountDetails(ctx, lse.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Info("savings account not found")
			return lse, nil, nil
		}
		return nil, nil, errors.Wrap(err, "error getting savings account details")
	}
	user, err := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, nil, errors.Wrap(err, "error getting user")
	}
	savingsAccount := &palPb.MandateData_BankingDetails_AccountDetails{
		AccountNumber:     acc.GetAccountNo(),
		AccountHolderName: user.GetProfile().GetKycName().ToString(),
		IfscCode:          acc.GetIfscCode(),
		BankName:          utils.FiFederalBank,
	}
	accountsMap := lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails()
	savingsAccountKeyForAccountsMap := mandateHelper.GetAltAccMapKey(savingsAccount)
	_, savingsAccountPresentInAccountsMap := accountsMap[savingsAccountKeyForAccountsMap]
	if savingsAccountPresentInAccountsMap {
		return lse, savingsAccount, nil
	}
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	if lse.GetDetails().GetMandateData().GetBankingDetails() == nil {
		lse.GetDetails().GetMandateData().BankingDetails = &palPb.MandateData_BankingDetails{}
	}
	if lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails() == nil {
		lse.GetDetails().GetMandateData().GetBankingDetails().AlternateAccDetails = make(map[string]*palPb.MandateData_BankingDetails_AccountDetails)
	}
	lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails()[savingsAccountKeyForAccountsMap] = savingsAccount
	err = p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		return nil, nil, errors.Wrap(err, "error storing savings bank account details")
	}
	return lse, savingsAccount, nil
}
