package lenden

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	lendenVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// MandateExpiryDuration is the duration for which the mandate link is valid once generated
const MandateExpiryDuration = 15 * time.Minute

type MandateSetupProcessor struct {
	loanApplicantDao     dao.LoanApplicantDao
	loanStepExecutionDao dao.LoanStepExecutionsDao
	loanRequestDao       dao.LoanRequestsDao
	rpcHelper            *helper.RpcHelper
	userClient           userPb.UsersClient
	lendenVgClient       lendenVgPb.LendenClient
}

type MandateInfo struct {
	IsCompleted bool
	Lse         *palPb.LoanStepExecution
}

func NewMandateSetupProcessor(
	loanApplicantDao dao.LoanApplicantDao,
	lendenVgClient lendenVgPb.LendenClient,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanRequestDao dao.LoanRequestsDao,
	rpcHelper *helper.RpcHelper,
	userClient userPb.UsersClient,
) *MandateSetupProcessor {
	return &MandateSetupProcessor{
		loanApplicantDao:     loanApplicantDao,
		lendenVgClient:       lendenVgClient,
		loanStepExecutionDao: loanStepExecutionDao,
		loanRequestDao:       loanRequestDao,
		rpcHelper:            rpcHelper,
		userClient:           userClient,
	}
}

func (p *MandateSetupProcessor) GetMandateInfo(ctx context.Context, lse *palPb.LoanStepExecution) (*MandateInfo, error) {
	if lse.GetDetails().GetMandateData().GetMerchantTxnId() == "" {
		initMandateRes, err := p.InitiateMandate(ctx, lse)
		if err != nil {
			return nil, errors.Wrap(err, "error getting mandate initiation info")
		}
		switch initMandateRes.Status {
		case MandateStatusAlreadyCompleted:
			if initMandateRes.Data.TrackingId == "" {
				return nil, errors.New("expected non-empty tracking id when e-NACH already completed")
			}
			lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initMandateRes.Data)
			if err != nil {
				return nil, errors.Wrap(err, "error updating mandate")
			}
			return &MandateInfo{IsCompleted: true, Lse: lse}, nil
		case MandateStatusPreRequisitesPending:
			return &MandateInfo{IsCompleted: false, Lse: lse}, nil
		case MandateStatusInProgress:
			lse, err = p.updateLseWithMandateInProgressInfo(ctx, lse, initMandateRes)
			if err != nil {
				return nil, errors.Wrap(err, "error updating LSE with mandate in progress info")
			}
			return &MandateInfo{IsCompleted: false, Lse: lse}, nil
		default:
			return nil, errors.Errorf("unhandled mandate status: %s", initMandateRes.Status)
		}
	}
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	mandateStatusRes, err := p.lendenVgClient.CheckMandateStatus(ctx, &lendenVgPb.CheckMandateStatusRequest{
		UserId:      applicant.GetVendorApplicantId(),
		TrackingId:  lse.GetDetails().GetMandateData().GetMerchantTxnId(),
		MandateType: lendenVgPb.MandateType_MANDATE_TYPE_NACH_MANDATE,
	})
	if err = epifigrpc.RPCError(mandateStatusRes, err); err != nil {
		return nil, errors.Wrap(err, "error checking mandate status")
	}
	switch mandateStatusRes.GetMandateStatus() {
	case lendenVgPb.MandateStatus_MANDATE_STATUS_COMPLETED:
		data := &MandateData{TrackingId: mandateStatusRes.TrackingId}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, data)
		if err != nil {
			return nil, errors.Wrap(err, "error updating LSE with mandate completion info")
		}
		return &MandateInfo{IsCompleted: true, Lse: lse}, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_FAILED,
		lendenVgPb.MandateStatus_MANDATE_STATUS_EXPIRED:
		var reInitiateRes *reInitiateMandateResponse
		reInitiateRes, err = p.reInitiateMandateSetup(ctx, lse, applicant)
		if err != nil {
			return nil, errors.Wrap(err, "error re-initiating mandate setup after status failure")
		}
		if reInitiateRes.isMandateCompleted {
			return &MandateInfo{IsCompleted: true, Lse: lse}, nil
		}
		if lse.GetDetails().GetMandateData().GetUrl() == "" {
			return nil, errors.New("expected non-empty mandate URL after re-initiating mandate setup")
		}
		return &MandateInfo{IsCompleted: false, Lse: lse}, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS:
		mandateLinkExpired := time.Now().After(lse.GetDetails().GetMandateData().GetUrlGeneratedAt().AsTime().Add(MandateExpiryDuration))
		if mandateLinkExpired {
			var reInitiateRes *reInitiateMandateResponse
			reInitiateRes, err = p.reInitiateMandateSetup(ctx, lse, applicant)
			if err != nil {
				return nil, errors.Wrap(err, "error re-initiating mandate setup after link expiry or URL age")
			}
			if reInitiateRes.isMandateCompleted {
				return &MandateInfo{IsCompleted: true, Lse: lse}, nil
			}
			if lse.GetDetails().GetMandateData().GetUrl() == "" {
				return nil, errors.New("expected non-empty mandate URL after re-initiating mandate setup")
			}
			return &MandateInfo{IsCompleted: false, Lse: lse}, nil
		}
		return &MandateInfo{IsCompleted: false, Lse: lse}, nil
	default:
		return nil, errors.Errorf("unexpected mandate status: %s", mandateStatusRes.GetMandateStatus())
	}
}

func (p *MandateSetupProcessor) updateLseWithMandateCompletionInfo(ctx context.Context, lse *palPb.LoanStepExecution, mandateData *MandateData) (*palPb.LoanStepExecution, error) {
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	if mandateData.TrackingId != "" {
		lse.GetDetails().GetMandateData().MerchantTxnId = mandateData.TrackingId
	}
	if mandateData.URL != "" {
		lse.GetDetails().GetMandateData().Url = mandateData.URL
	}
	if mandateData.URLValidity.AsTime() != time.Unix(0, 0) {
		lse.GetDetails().GetMandateData().MandateLinkExpiry = mandateData.URLValidity
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL
	err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating LSE")
	}
	return lse, nil
}

type reInitiateMandateResponse struct {
	isMandateCompleted bool
	lse                *palPb.LoanStepExecution
}

func (p *MandateSetupProcessor) reInitiateMandateSetup(ctx context.Context, lse *palPb.LoanStepExecution, applicant *palPb.LoanApplicant) (*reInitiateMandateResponse, error) {
	initiateMandateRes, err := p.InitiateMandate(ctx, lse)
	if err != nil {
		return nil, errors.Wrap(err, "error getting initiation info")
	}
	switch initiateMandateRes.Status {
	case MandateStatusInProgress:
		lse, err = p.updateLseWithMandateInProgressInfo(ctx, lse, initiateMandateRes)
		if err != nil {
			return nil, errors.Wrap(err, "error updating LSE with mandate in progress info")
		}
		return &reInitiateMandateResponse{isMandateCompleted: false, lse: lse}, nil
	case MandateStatusAlreadyCompleted:
		if initiateMandateRes.Data.TrackingId == "" {
			return nil, errors.New("expected non-empty tracking id when e-NACH already completed")
		}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initiateMandateRes.Data)
		if err != nil {
			return nil, errors.Wrap(err, "error updating LSE with mandate completion info")
		}
		return &reInitiateMandateResponse{isMandateCompleted: true, lse: lse}, nil
	default:
		return nil, errors.Errorf("unexpected mandate status: %s", initiateMandateRes.Status)
	}
}

func (p *MandateSetupProcessor) updateLseWithMandateInProgressInfo(
	ctx context.Context,
	lse *palPb.LoanStepExecution,
	initiateMandateRes *InitiateMandateResponse,
) (*palPb.LoanStepExecution, error) {
	if initiateMandateRes.Data.TrackingId == "" {
		return nil, errors.Errorf("expected non-empty mandate setup tracking id with in progress status")
	}
	if initiateMandateRes.Data.URL == "" {
		return nil, errors.Errorf("expected non-empty mandate setup url with in progress status")
	}
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	lse.GetDetails().GetMandateData().MerchantTxnId = initiateMandateRes.Data.TrackingId
	lse.GetDetails().GetMandateData().Url = initiateMandateRes.Data.URL
	lse.GetDetails().GetMandateData().UrlGeneratedAt = initiateMandateRes.Data.URLGeneratedAt
	if initiateMandateRes.Data.URLValidity.AsTime() != time.Unix(0, 0) {
		lse.GetDetails().GetMandateData().MandateLinkExpiry = initiateMandateRes.Data.URLValidity
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED
	err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating LSE")
	}
	return lse, nil
}

type MandateStatus int

const (
	MandateStatusUnknown MandateStatus = iota

	// MandateStatusPreRequisitesPending When an action needs to be done before setting up a payment mandate
	// like validating bank account details with lender
	MandateStatusPreRequisitesPending

	// MandateStatusInProgress When pre-requisites for starting mandate setup are complete and the setup has been started
	MandateStatusInProgress

	// MandateStatusAlreadyCompleted When the mandate has already been set up (most probably during a previous loan account opening orchestration)
	MandateStatusAlreadyCompleted
)

func (s MandateStatus) String() string {
	return []string{"Unknown", "PreRequisitesPending", "InProgress", "AlreadyCompleted"}[s]
}

type InitiateMandateResponse struct {
	Status MandateStatus
	Data   *MandateData
}

type MandateData struct {
	TrackingId     string
	URL            string
	URLValidity    *timestamppb.Timestamp
	URLGeneratedAt *timestamppb.Timestamp
}

func (p *MandateSetupProcessor) InitiateMandate(ctx context.Context, lse *palPb.LoanStepExecution) (*InitiateMandateResponse, error) {
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	userIpAddress, err := p.rpcHelper.FetchIpAddress(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting user ip address")
	}
	userDP, udpErr := p.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: lse.GetActorId(),
		PropertyTypes: []typesv2.DeviceProperty{
			typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
		return nil, errors.Wrapf(te, "error getting device id")
	}
	deviceId := userDP.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()
	res, err := p.lendenVgClient.InitMandate(ctx, &lendenVgPb.InitMandateRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId: lr.GetVendorRequestId(),
		UserId: applicant.GetVendorApplicantId(),
		ConsentCodeList: []lendenVgPb.ConsentType{
			lendenVgPb.ConsentType_CONSENT_TYPE_MANDATE,
		},
		MandateType: lendenVgPb.MandateType_MANDATE_TYPE_NACH_MANDATE,
		UserIp:      userIpAddress,
		DeviceId:    deviceId,
		ConsentTime: timestamppb.Now(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		switch lendenVgPb.InitMandateResponse_Status(res.GetStatus().GetCode()) {
		case lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED:
			if res.GetTrackingId() == "" {
				return nil, errors.Errorf("expected non-empty tracking id when e-NACH setup is already completed")
			}
			return &InitiateMandateResponse{
				Status: MandateStatusAlreadyCompleted,
				Data:   &MandateData{TrackingId: res.GetTrackingId()},
			}, nil
		case lendenVgPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND,
			lendenVgPb.InitMandateResponse_BANK_ACCOUNT_NOT_VERIFIED:
			return &InitiateMandateResponse{
				Status: MandateStatusPreRequisitesPending,
			}, nil
		default:
			return nil, errors.Wrapf(err, "error initiating mandate with lender")
		}
	}
	return &InitiateMandateResponse{
		Status: MandateStatusInProgress,
		Data: &MandateData{
			TrackingId:     res.GetTrackingId(),
			URL:            res.GetRedirectionUrl(),
			URLValidity:    res.GetMandateUrlValidity(),
			URLGeneratedAt: timestamppb.Now(),
		},
	}, nil
}
