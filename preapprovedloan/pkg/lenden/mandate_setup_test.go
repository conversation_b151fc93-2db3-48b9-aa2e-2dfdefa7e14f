package lenden

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	lendenVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	lendenMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden/mocks"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

type mockFields struct {
	loanApplicantDao     *mocks.MockLoanApplicantDao
	loanStepExecutionDao *mocks.MockLoanStepExecutionsDao
	loanRequestDao       *mocks.MockLoanRequestsDao
	rpcHelper            *helper.RpcHelper // can be nil if not used directly
	userClient           *userMocks.MockUsersClient
	lendenVgClient       *lendenMocks.MockLendenClient
}

func initMocks(ctrl *gomock.Controller) *mockFields {
	return &mockFields{
		loanApplicantDao:     mocks.NewMockLoanApplicantDao(ctrl),
		loanStepExecutionDao: mocks.NewMockLoanStepExecutionsDao(ctrl),
		loanRequestDao:       mocks.NewMockLoanRequestsDao(ctrl),
		userClient:           userMocks.NewMockUsersClient(ctrl),
		lendenVgClient:       lendenMocks.NewMockLendenClient(ctrl),
	}
}

func newProcessorWithMocks(f *mockFields) *MandateSetupProcessor {
	return &MandateSetupProcessor{
		loanApplicantDao:     f.loanApplicantDao,
		loanStepExecutionDao: f.loanStepExecutionDao,
		loanRequestDao:       f.loanRequestDao,
		rpcHelper:            nil, // not used directly in GetMandateInfo
		userClient:           f.userClient,
		lendenVgClient:       f.lendenVgClient,
	}
}

func TestMandateSetupProcessor_GetMandateInfo(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	type testCase struct {
		name     string
		setup    func(f *mockFields)
		lse      *palPb.LoanStepExecution
		want     *MandateInfo
		wantErr  bool
		assertFn func(t *testing.T, got *MandateInfo, err error)
	}

	trackingId := "track-123"
	mandateUrl := "https://mandate.url"
	urlGenAt := time.Now().Add(-10 * time.Minute)
	urlGenAtProto := timestamppb.New(urlGenAt)
	urlValidity := timestamppb.New(urlGenAt.Add(15 * time.Minute))

	applicant := &palPb.LoanApplicant{Id: "app-1", VendorApplicantId: "vend-app-1"}

	lseBase := &palPb.LoanStepExecution{
		Id:      "lse-1",
		ActorId: "actor-1",
		RefId:   "ref-1",
		Details: &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_MandateData{
				MandateData: &palPb.MandateData{},
			},
		},
	}

	tests := []testCase{
		{
			name: "Mandate not initiated, InitiateMandate returns AlreadyCompleted",
			lse:  lseBase.CloneVT(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:     &lendenVgPb.InitMandateResponse_Status{Code: int32(lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED)},
					TrackingId: trackingId,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{}, nil).AnyTimes()
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &MandateInfo{IsCompleted: true},
			wantErr: false,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.NoError(t, err)
				assert.True(t, got.IsCompleted)
				assert.NotNil(t, got.Lse)
				assert.Equal(t, trackingId, got.Lse.GetDetails().GetMandateData().GetMerchantTxnId())
			},
		},
		{
			name: "Mandate not initiated, InitiateMandate returns PreRequisitesPending",
			lse:  lseBase.CloneVT(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status: &lendenVgPb.InitMandateResponse_Status{Code: int32(lendenVgPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND)},
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: false},
			wantErr: false,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.NoError(t, err)
				assert.False(t, got.IsCompleted)
			},
		},
		{
			name: "Mandate not initiated, InitiateMandate returns InProgress",
			lse:  lseBase.CloneVT(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:             &lendenVgPb.InitMandateResponse_Status{Code: int32(lendenVgPb.InitMandateResponse_SUCCESS)},
					TrackingId:         trackingId,
					RedirectionUrl:     mandateUrl,
					MandateUrlValidity: urlValidity,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{}, nil).AnyTimes()
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &MandateInfo{IsCompleted: false},
			wantErr: false,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.NoError(t, err)
				assert.False(t, got.IsCompleted)
				assert.NotNil(t, got.Lse)
				assert.Equal(t, trackingId, got.Lse.GetDetails().GetMandateData().GetMerchantTxnId())
				assert.Equal(t, mandateUrl, got.Lse.GetDetails().GetMandateData().GetUrl())
			},
		},
		{
			name: "Mandate already present, status completed",
			lse: func() *palPb.LoanStepExecution {
				lse := lseBase.CloneVT()
				lse.GetDetails().GetMandateData().MerchantTxnId = trackingId
				return lse
			}(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil)
				f.lendenVgClient.EXPECT().CheckMandateStatus(ctx, gomock.Any()).Return(&lendenVgPb.CheckMandateStatusResponse{
					MandateStatus: lendenVgPb.MandateStatus_MANDATE_STATUS_COMPLETED,
					TrackingId:    trackingId,
				}, nil)
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &MandateInfo{IsCompleted: true},
			wantErr: false,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.NoError(t, err)
				assert.True(t, got.IsCompleted)
			},
		},
		{
			name: "Mandate already present, status failed, re-initiate returns completed",
			lse: func() *palPb.LoanStepExecution {
				lse := lseBase.CloneVT()
				lse.GetDetails().GetMandateData().MerchantTxnId = trackingId
				return lse
			}(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil)
				f.lendenVgClient.EXPECT().CheckMandateStatus(ctx, gomock.Any()).Return(&lendenVgPb.CheckMandateStatusResponse{
					MandateStatus: lendenVgPb.MandateStatus_MANDATE_STATUS_FAILED,
				}, nil)
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:     &lendenVgPb.InitMandateResponse_Status{Code: int32(lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED)},
					TrackingId: trackingId,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{}, nil).AnyTimes()
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &MandateInfo{IsCompleted: true},
			wantErr: false,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.NoError(t, err)
				assert.True(t, got.IsCompleted)
			},
		},
		{
			name: "Mandate already present, status in progress, link expired, re-initiate returns in progress",
			lse: func() *palPb.LoanStepExecution {
				lse := lseBase.CloneVT()
				lse.GetDetails().GetMandateData().MerchantTxnId = trackingId
				lse.GetDetails().GetMandateData().UrlGeneratedAt = timestamppb.New(time.Now().Add(-20 * time.Minute))
				return lse
			}(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil)
				f.lendenVgClient.EXPECT().CheckMandateStatus(ctx, gomock.Any()).Return(&lendenVgPb.CheckMandateStatusResponse{
					MandateStatus: lendenVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS,
				}, nil)
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:             &lendenVgPb.InitMandateResponse_Status{Code: int32(lendenVgPb.InitMandateResponse_SUCCESS)},
					TrackingId:         trackingId,
					RedirectionUrl:     mandateUrl,
					MandateUrlValidity: urlValidity,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{}, nil).AnyTimes()
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
			},
			want:    &MandateInfo{IsCompleted: false},
			wantErr: false,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.NoError(t, err)
				assert.False(t, got.IsCompleted)
			},
		},
		{
			name: "Error from GetByActorId",
			lse:  lseBase.CloneVT(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
			assertFn: func(t *testing.T, got *MandateInfo, err error) {
				assert.Error(t, err)
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			if tc.setup != nil {
				tc.setup(f)
			}
			p := newProcessorWithMocks(f)
			got, err := p.GetMandateInfo(ctx, tc.lse)
			if tc.assertFn != nil {
				tc.assertFn(t, got, err)
			} else {
				if tc.wantErr {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
					assert.Equal(t, tc.want.IsCompleted, got.IsCompleted)
				}
			}
		})
	}
}
