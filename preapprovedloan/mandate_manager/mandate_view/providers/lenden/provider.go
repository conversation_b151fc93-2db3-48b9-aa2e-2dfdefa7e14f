package lenden

import (
	"context"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	typesPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/account"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	rpEnachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	userPb "github.com/epifi/gamma/api/user"
	lendenVgClient "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/helper"
	lendenPkg "github.com/epifi/gamma/preapprovedloan/pkg/lenden"
)

type LendenMandateViewProvider struct {
	rpcHelper             *helper.RpcHelper
	deeplinkFactory       deeplink.IDeeplinkProviderFactory
	loanStepExecutionDao  dao.LoanStepExecutionsDao
	loanApplicantDao      dao.LoanApplicantDao
	loanRequestsDao       dao.LoanRequestsDao
	lendenClient          lendenVgClient.LendenClient
	userClient            userPb.UsersClient
	loanOfferDao          dao.LoanOffersDao
	mandateSetupProcessor *lendenPkg.MandateSetupProcessor
}

func NewLendenMandateViewProvider(
	rpcHelper *helper.RpcHelper,
	deeplinkFactory deeplink.IDeeplinkProviderFactory,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanApplicantDao dao.LoanApplicantDao,
	loanRequestsDao dao.LoanRequestsDao,
	lendenClient lendenVgClient.LendenClient,
	userClient userPb.UsersClient,
	loanOfferDao dao.LoanOffersDao,
	mandateSetupProcessor *lendenPkg.MandateSetupProcessor,
) *LendenMandateViewProvider {
	return &LendenMandateViewProvider{
		rpcHelper:             rpcHelper,
		deeplinkFactory:       deeplinkFactory,
		loanStepExecutionDao:  loanStepExecutionDao,
		loanApplicantDao:      loanApplicantDao,
		loanRequestsDao:       loanRequestsDao,
		lendenClient:          lendenClient,
		userClient:            userClient,
		loanOfferDao:          loanOfferDao,
		mandateSetupProcessor: mandateSetupProcessor,
	}
}

func (l *LendenMandateViewProvider) ValidateLoanStep(ctx context.Context, _ *palPb.LoanHeader, lse *palPb.LoanStepExecution, accDetails *palPb.MandateData_BankingDetails_AccountDetails) (error, string) {
	if lse.GetDetails().GetMandateData().GetMandateLinkExpiry() == nil ||
		lse.GetDetails().GetMandateData().GetMandateLinkExpiry().AsTime() != time.Unix(0, 0) {
		logger.Info(ctx, "no mandate link expiry present, skipping validation to initiate mandate")
		return nil, ""
	}
	if time.Since(lse.GetDetails().GetMandateData().GetMandateLinkExpiry().AsTime()) < lendenPkg.MandateExpiryDuration {
		roundedUpWaitTimeInMinutes := int(time.Since(lse.GetDetails().GetMandateData().GetMandateLinkExpiry().AsTime().Add(time.Minute)).Round(time.Minute).Minutes())
		message := "Cannot process another mandate until the current one expires. Please wait for " + strconv.Itoa(roundedUpWaitTimeInMinutes) + " minutes."
		return errors.Wrap(epifierrors.ErrAlreadyExists, "mandate link has not expired yet"), message
	}
	logger.Info(ctx, "mandate link has expired, skipping validation to re-initiate mandate")
	return nil, ""
}

func (l *LendenMandateViewProvider) GetLoansMandateSetupScreen(ctx context.Context, loanHeader *palPb.LoanHeader, lse *palPb.LoanStepExecution,
	accDetails *palPb.MandateData_BankingDetails_AccountDetails, _ rpEnachEnumsPb.EnachRegistrationAuthMode) (*deeplinkPb.Deeplink, error) {
	if lse.GetDetails().GetMandateData().GetMerchantTxnId() != "" {
		mandateSetupInfo, err := l.mandateSetupProcessor.GetMandateInfo(ctx, lse)
		if err != nil {
			return nil, errors.Wrap(err, "error getting latest mandate setup info")
		}
		if mandateSetupInfo.IsCompleted {
			deeplinkProvider := l.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: loanHeader.GetVendor(), LoanProgram: loanHeader.GetLoanProgram()})
			return deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lse.GetRefId()), nil
		}
		dl, err := l.getMandateSetupScreen(ctx, loanHeader, mandateSetupInfo.Lse)
		if err != nil {
			return nil, errors.Wrap(err, "error getting mandate setup screen")
		}
		return dl, nil
	}
	applicant, err := l.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting actor id")
	}
	lr, err := l.loanRequestsDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan_request")
	}
	loanOffer, err := l.loanOfferDao.GetById(ctx, lr.GetOfferId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan offer")
	}
	offerBankDetails := loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails()
	accountNumber := accDetails.GetAccountNumber()
	// TODO(Brijesh): Find out how to get account type and where to get it from
	accountType := account.AccountType_SAVINGS
	bankAccountDetailsForLDC := &typesPb.BankAccountDetails{
		AccountName:   accDetails.GetAccountHolderName(),
		AccountNumber: accountNumber,
		BankName:      accDetails.GetBankName(),
		Ifsc:          accDetails.GetIfscCode(),
		AccountType:   accountType,
	}
	if offerBankDetails != nil {
		logger.Info(ctx, "using details from Lenden offer constraints for sending bank details to LDC", zap.String(logger.OFFER_ID, loanOffer.GetId()))
		bankAccountDetailsForLDC = &typesPb.BankAccountDetails{
			AccountName:   offerBankDetails.GetAccountHolderName(),
			AccountNumber: accountNumber,
			BankName:      offerBankDetails.GetBankName(),
			Ifsc:          offerBankDetails.GetIfsc(),
			AccountType:   accountType,
		}
	}
	addBankDetailsRequest := lendenVgClient.AddBankDetailsRequest{
		Header:             &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId:             lr.GetVendorRequestId(),
		UserId:             applicant.GetVendorApplicantId(),
		BankAccountDetails: bankAccountDetailsForLDC,
	}
	addBankDetailsResponse, err := l.lendenClient.AddBankDetails(ctx, &addBankDetailsRequest)
	if err = epifigrpc.RPCError(addBankDetailsResponse, err); err != nil {
		logger.Error(ctx, "error adding bank details", zap.Error(err))
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_BANK_ACCOUNT_NOT_ACTIVE) {
			return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "bank account not active")
		}
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_NAME_MISMATCH) {
			return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "name mismatch when adding bank details")
		}
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_BANK_CONNECTION_ERROR) {
			return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "error connecting to bank")
		}
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_INVALID_BANK_DETAILS) {
			return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "invalid bank details")
		}
		return nil, errors.Wrap(err, "error in lenden add bank details api")
	}
	mandateInfo, err := l.mandateSetupProcessor.GetMandateInfo(ctx, lse)
	if err != nil {
		return nil, errors.Wrap(err, "error initiating mandate")
	}
	if mandateInfo.IsCompleted {
		deeplinkProvider := l.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: loanHeader.GetVendor(), LoanProgram: loanHeader.GetLoanProgram()})
		return deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lse.GetRefId()), nil
	}
	lse = mandateInfo.Lse
	dl, err := l.getMandateSetupScreen(ctx, loanHeader, lse)
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate setup screen deeplink")
	}
	return dl, nil
}

func (l *LendenMandateViewProvider) getMandateSetupScreen(ctx context.Context, loanHeader *palPb.LoanHeader, lse *palPb.LoanStepExecution) (*deeplinkPb.Deeplink, error) {
	mandateUrl := lse.GetDetails().GetMandateData().GetUrl()
	if mandateUrl == "" {
		return nil, errors.New("mandate URL is empty")
	}
	deeplinkProvider := l.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: loanHeader.GetVendor(), LoanProgram: loanHeader.GetLoanProgram()})
	dl, err := deeplinkProvider.GetLoansMandateSetupScreen(ctx,
		deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), lse.GetId(),
		&provider.LoansMandateSetupScreenParams{WebViewParams: &provider.WebViewParams{EntryUrl: mandateUrl}},
	)
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate setup screen deeplink")
	}
	return dl, nil
}

func (l *LendenMandateViewProvider) PostDlGenerationProcessing(ctx context.Context, _ *palPb.LoanHeader, lseId string, accDetails *palPb.MandateData_BankingDetails_AccountDetails) error {
	// fetch lse object from db to update final acc used
	// fetching this record from db to avoid any dirty updates
	lse, err := l.loanStepExecutionDao.GetById(ctx, lseId)
	if err != nil {
		return errors.Wrap(err, "error while fetching lse object by id from db")
	}
	lse.GetDetails().GetMandateData().GetBankingDetails().FinalAccDetailsUsed = accDetails
	updateErr := l.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if updateErr != nil {
		return errors.Wrap(updateErr, "error while updating final acc details used field in mandate banking details")
	}
	return nil
}
