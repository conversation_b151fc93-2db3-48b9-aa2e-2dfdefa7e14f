// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.KYCLevel(0)
)

// Validate checks the field values on ConfirmCardMailingAddressOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConfirmCardMailingAddressOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmCardMailingAddressOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConfirmCardMailingAddressOptionsMultiError, or nil if none found.
func (m *ConfirmCardMailingAddressOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardMailingAddressOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KycLevel

	// no validation rules for Title

	// no validation rules for Subtitle

	// no validation rules for PlaceHolderForName

	// no validation rules for PlaceHolderForAddress

	for idx, item := range m.GetCheckBoxTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
						field:  fmt.Sprintf("CheckBoxTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
						field:  fmt.Sprintf("CheckBoxTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConfirmCardMailingAddressOptionsValidationError{
					field:  fmt.Sprintf("CheckBoxTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Flow

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CheckboxTextColor

	// no validation rules for PlaceHolderForAmount

	// no validation rules for HintTextAmount

	// no validation rules for PlaceHolderColor

	// no validation rules for ContentColor

	// no validation rules for HintColor

	// no validation rules for DividerColor

	// no validation rules for EditIconColor

	// no validation rules for CardColor

	// no validation rules for BackgroundColor

	if all {
		switch v := interface{}(m.GetScreenTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "ScreenTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "ScreenTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "ScreenTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "ScreenSubtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "ScreenSubtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "ScreenSubtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayAmount

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for AddressConfirmationMessage

	// no validation rules for HideAddressField

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardNameDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "DebitCardNameDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "DebitCardNameDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardNameDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "DebitCardNameDescription",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGender()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGender()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "Gender",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPurposeOfSavingsAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "PurposeOfSavingsAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "PurposeOfSavingsAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurposeOfSavingsAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "PurposeOfSavingsAccount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConfirmBottomSheetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "ConfirmBottomSheetHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptionsValidationError{
					field:  "ConfirmBottomSheetHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmBottomSheetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptionsValidationError{
				field:  "ConfirmBottomSheetHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmCardMailingAddressOptionsMultiError(errors)
	}

	return nil
}

// ConfirmCardMailingAddressOptionsMultiError is an error wrapping multiple
// validation errors returned by
// ConfirmCardMailingAddressOptions.ValidateAll() if the designated
// constraints aren't met.
type ConfirmCardMailingAddressOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardMailingAddressOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardMailingAddressOptionsMultiError) AllErrors() []error { return m }

// ConfirmCardMailingAddressOptionsValidationError is the validation error
// returned by ConfirmCardMailingAddressOptions.Validate if the designated
// constraints aren't met.
type ConfirmCardMailingAddressOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardMailingAddressOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardMailingAddressOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardMailingAddressOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardMailingAddressOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardMailingAddressOptionsValidationError) ErrorName() string {
	return "ConfirmCardMailingAddressOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardMailingAddressOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardMailingAddressOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardMailingAddressOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardMailingAddressOptionsValidationError{}

// Validate checks the field values on
// ConfirmCardMailingAddressOptions_CheckBoxText with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConfirmCardMailingAddressOptions_CheckBoxText) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConfirmCardMailingAddressOptions_CheckBoxText with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConfirmCardMailingAddressOptions_CheckBoxTextMultiError, or nil if none found.
func (m *ConfirmCardMailingAddressOptions_CheckBoxText) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardMailingAddressOptions_CheckBoxText) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Text

	if len(errors) > 0 {
		return ConfirmCardMailingAddressOptions_CheckBoxTextMultiError(errors)
	}

	return nil
}

// ConfirmCardMailingAddressOptions_CheckBoxTextMultiError is an error wrapping
// multiple validation errors returned by
// ConfirmCardMailingAddressOptions_CheckBoxText.ValidateAll() if the
// designated constraints aren't met.
type ConfirmCardMailingAddressOptions_CheckBoxTextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardMailingAddressOptions_CheckBoxTextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardMailingAddressOptions_CheckBoxTextMultiError) AllErrors() []error { return m }

// ConfirmCardMailingAddressOptions_CheckBoxTextValidationError is the
// validation error returned by
// ConfirmCardMailingAddressOptions_CheckBoxText.Validate if the designated
// constraints aren't met.
type ConfirmCardMailingAddressOptions_CheckBoxTextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardMailingAddressOptions_CheckBoxTextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardMailingAddressOptions_CheckBoxTextValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConfirmCardMailingAddressOptions_CheckBoxTextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardMailingAddressOptions_CheckBoxTextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardMailingAddressOptions_CheckBoxTextValidationError) ErrorName() string {
	return "ConfirmCardMailingAddressOptions_CheckBoxTextValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardMailingAddressOptions_CheckBoxTextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardMailingAddressOptions_CheckBoxText.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardMailingAddressOptions_CheckBoxTextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardMailingAddressOptions_CheckBoxTextValidationError{}

// Validate checks the field values on
// ConfirmCardMailingAddressOptions_PlaceHolder with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConfirmCardMailingAddressOptions_PlaceHolder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConfirmCardMailingAddressOptions_PlaceHolder with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConfirmCardMailingAddressOptions_PlaceHolderMultiError, or nil if none found.
func (m *ConfirmCardMailingAddressOptions_PlaceHolder) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardMailingAddressOptions_PlaceHolder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
				field:  "BorderColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "Label",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "Label",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
				field:  "Label",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExplanatoryText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "ExplanatoryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
					field:  "ExplanatoryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExplanatoryText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_PlaceHolderValidationError{
				field:  "ExplanatoryText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmCardMailingAddressOptions_PlaceHolderMultiError(errors)
	}

	return nil
}

// ConfirmCardMailingAddressOptions_PlaceHolderMultiError is an error wrapping
// multiple validation errors returned by
// ConfirmCardMailingAddressOptions_PlaceHolder.ValidateAll() if the
// designated constraints aren't met.
type ConfirmCardMailingAddressOptions_PlaceHolderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardMailingAddressOptions_PlaceHolderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardMailingAddressOptions_PlaceHolderMultiError) AllErrors() []error { return m }

// ConfirmCardMailingAddressOptions_PlaceHolderValidationError is the
// validation error returned by
// ConfirmCardMailingAddressOptions_PlaceHolder.Validate if the designated
// constraints aren't met.
type ConfirmCardMailingAddressOptions_PlaceHolderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardMailingAddressOptions_PlaceHolderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardMailingAddressOptions_PlaceHolderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardMailingAddressOptions_PlaceHolderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardMailingAddressOptions_PlaceHolderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardMailingAddressOptions_PlaceHolderValidationError) ErrorName() string {
	return "ConfirmCardMailingAddressOptions_PlaceHolderValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardMailingAddressOptions_PlaceHolderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardMailingAddressOptions_PlaceHolder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardMailingAddressOptions_PlaceHolderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardMailingAddressOptions_PlaceHolderValidationError{}

// Validate checks the field values on
// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountMultiError, or nil
// if none found.
func (m *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRadioOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{
						field:  fmt.Sprintf("RadioOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{
						field:  fmt.Sprintf("RadioOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{
					field:  fmt.Sprintf("RadioOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountMultiError(errors)
	}

	return nil
}

// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountMultiError is an
// error wrapping multiple validation errors returned by
// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount.ValidateAll() if
// the designated constraints aren't met.
type ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountMultiError) AllErrors() []error {
	return m
}

// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError is
// the validation error returned by
// ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount.Validate if the
// designated constraints aren't met.
type ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError) ErrorName() string {
	return "ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardMailingAddressOptions_PurposeOfSavingsAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardMailingAddressOptions_PurposeOfSavingsAccountValidationError{}

// Validate checks the field values on
// ConfirmCardMailingAddressOptions_RadioOption with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConfirmCardMailingAddressOptions_RadioOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConfirmCardMailingAddressOptions_RadioOption with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConfirmCardMailingAddressOptions_RadioOptionMultiError, or nil if none found.
func (m *ConfirmCardMailingAddressOptions_RadioOption) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardMailingAddressOptions_RadioOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_RadioOptionValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardMailingAddressOptions_RadioOptionValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardMailingAddressOptions_RadioOptionValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PurposeOfSavingsAccountValue

	if len(errors) > 0 {
		return ConfirmCardMailingAddressOptions_RadioOptionMultiError(errors)
	}

	return nil
}

// ConfirmCardMailingAddressOptions_RadioOptionMultiError is an error wrapping
// multiple validation errors returned by
// ConfirmCardMailingAddressOptions_RadioOption.ValidateAll() if the
// designated constraints aren't met.
type ConfirmCardMailingAddressOptions_RadioOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardMailingAddressOptions_RadioOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardMailingAddressOptions_RadioOptionMultiError) AllErrors() []error { return m }

// ConfirmCardMailingAddressOptions_RadioOptionValidationError is the
// validation error returned by
// ConfirmCardMailingAddressOptions_RadioOption.Validate if the designated
// constraints aren't met.
type ConfirmCardMailingAddressOptions_RadioOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardMailingAddressOptions_RadioOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardMailingAddressOptions_RadioOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardMailingAddressOptions_RadioOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardMailingAddressOptions_RadioOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardMailingAddressOptions_RadioOptionValidationError) ErrorName() string {
	return "ConfirmCardMailingAddressOptions_RadioOptionValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardMailingAddressOptions_RadioOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardMailingAddressOptions_RadioOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardMailingAddressOptions_RadioOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardMailingAddressOptions_RadioOptionValidationError{}
