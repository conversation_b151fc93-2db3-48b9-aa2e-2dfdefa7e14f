// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/onboarding/fi_lite_screen_options.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on FeatureBenefitsScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeatureBenefitsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureBenefitsScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeatureBenefitsScreenOptionsMultiError, or nil if none found.
func (m *FeatureBenefitsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureBenefitsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureBenefitsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FeatureBenefitsScreenOptionsValidationError{
					field:  fmt.Sprintf("Sections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FeatureBenefitsScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureBenefitsScreenOptionsValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDividerColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "DividerColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "DividerColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDividerColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureBenefitsScreenOptionsValidationError{
				field:  "DividerColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureBenefitsScreenOptionsValidationError{
				field:  "BgImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeatureOnboardingEntryPoint

	// no validation rules for ToFetchScreenOptionsData

	if all {
		switch v := interface{}(m.GetTncText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "TncText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "TncText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTncText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureBenefitsScreenOptionsValidationError{
				field:  "TncText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomSectionBg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "BottomSectionBg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureBenefitsScreenOptionsValidationError{
					field:  "BottomSectionBg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSectionBg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureBenefitsScreenOptionsValidationError{
				field:  "BottomSectionBg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FeatureBenefitsScreenOptionsMultiError(errors)
	}

	return nil
}

// FeatureBenefitsScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by FeatureBenefitsScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type FeatureBenefitsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureBenefitsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureBenefitsScreenOptionsMultiError) AllErrors() []error { return m }

// FeatureBenefitsScreenOptionsValidationError is the validation error returned
// by FeatureBenefitsScreenOptions.Validate if the designated constraints
// aren't met.
type FeatureBenefitsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureBenefitsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureBenefitsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureBenefitsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureBenefitsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureBenefitsScreenOptionsValidationError) ErrorName() string {
	return "FeatureBenefitsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e FeatureBenefitsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureBenefitsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureBenefitsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureBenefitsScreenOptionsValidationError{}

// Validate checks the field values on Section with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Section) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Section with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SectionMultiError, or nil if none found.
func (m *Section) ValidateAll() error {
	return m.validate(true)
}

func (m *Section) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Type.(type) {
	case *Section_SectionTypeList:
		if v == nil {
			err := SectionValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionTypeList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionTypeList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  "SectionTypeList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Section_SectionTypeIcons:
		if v == nil {
			err := SectionValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionTypeIcons()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeIcons",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeIcons",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionTypeIcons()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  "SectionTypeIcons",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Section_SectionTypeText:
		if v == nil {
			err := SectionValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionTypeText()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeText",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeText",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionTypeText()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  "SectionTypeText",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Section_SectionTypeHeader:
		if v == nil {
			err := SectionValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionTypeHeader()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeHeader",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeHeader",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionTypeHeader()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  "SectionTypeHeader",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Section_SectionTypeCarousel:
		if v == nil {
			err := SectionValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionTypeCarousel()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeCarousel",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeCarousel",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionTypeCarousel()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  "SectionTypeCarousel",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Section_SectionTypeItc:
		if v == nil {
			err := SectionValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSectionTypeItc()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeItc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  "SectionTypeItc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSectionTypeItc()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  "SectionTypeItc",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SectionMultiError(errors)
	}

	return nil
}

// SectionMultiError is an error wrapping multiple validation errors returned
// by Section.ValidateAll() if the designated constraints aren't met.
type SectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionMultiError) AllErrors() []error { return m }

// SectionValidationError is the validation error returned by Section.Validate
// if the designated constraints aren't met.
type SectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionValidationError) ErrorName() string { return "SectionValidationError" }

// Error satisfies the builtin error interface
func (e SectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionValidationError{}

// Validate checks the field values on SectionTypeIconTextComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SectionTypeIconTextComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionTypeIconTextComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionTypeIconTextComponentMultiError, or nil if none found.
func (m *SectionTypeIconTextComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionTypeIconTextComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSectionItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeIconTextComponentValidationError{
					field:  "SectionItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeIconTextComponentValidationError{
					field:  "SectionItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeIconTextComponentValidationError{
				field:  "SectionItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionTypeIconTextComponentMultiError(errors)
	}

	return nil
}

// SectionTypeIconTextComponentMultiError is an error wrapping multiple
// validation errors returned by SectionTypeIconTextComponent.ValidateAll() if
// the designated constraints aren't met.
type SectionTypeIconTextComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionTypeIconTextComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionTypeIconTextComponentMultiError) AllErrors() []error { return m }

// SectionTypeIconTextComponentValidationError is the validation error returned
// by SectionTypeIconTextComponent.Validate if the designated constraints
// aren't met.
type SectionTypeIconTextComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionTypeIconTextComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionTypeIconTextComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionTypeIconTextComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionTypeIconTextComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionTypeIconTextComponentValidationError) ErrorName() string {
	return "SectionTypeIconTextComponentValidationError"
}

// Error satisfies the builtin error interface
func (e SectionTypeIconTextComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionTypeIconTextComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionTypeIconTextComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionTypeIconTextComponentValidationError{}

// Validate checks the field values on SectionTypeList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SectionTypeList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionTypeList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionTypeListMultiError, or nil if none found.
func (m *SectionTypeList) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionTypeList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSectionHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeListValidationError{
					field:  "SectionHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeListValidationError{
					field:  "SectionHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeListValidationError{
				field:  "SectionHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionTypeListValidationError{
						field:  fmt.Sprintf("BenefitListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionTypeListValidationError{
						field:  fmt.Sprintf("BenefitListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionTypeListValidationError{
					field:  fmt.Sprintf("BenefitListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeListValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeListValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeListValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionTypeListMultiError(errors)
	}

	return nil
}

// SectionTypeListMultiError is an error wrapping multiple validation errors
// returned by SectionTypeList.ValidateAll() if the designated constraints
// aren't met.
type SectionTypeListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionTypeListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionTypeListMultiError) AllErrors() []error { return m }

// SectionTypeListValidationError is the validation error returned by
// SectionTypeList.Validate if the designated constraints aren't met.
type SectionTypeListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionTypeListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionTypeListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionTypeListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionTypeListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionTypeListValidationError) ErrorName() string { return "SectionTypeListValidationError" }

// Error satisfies the builtin error interface
func (e SectionTypeListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionTypeList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionTypeListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionTypeListValidationError{}

// Validate checks the field values on SectionTypeIcons with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SectionTypeIcons) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionTypeIcons with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionTypeIconsMultiError, or nil if none found.
func (m *SectionTypeIcons) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionTypeIcons) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSectionTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeIconsValidationError{
					field:  "SectionTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeIconsValidationError{
					field:  "SectionTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeIconsValidationError{
				field:  "SectionTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSectionInnerTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeIconsValidationError{
					field:  "SectionInnerTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeIconsValidationError{
					field:  "SectionInnerTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionInnerTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeIconsValidationError{
				field:  "SectionInnerTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionTypeIconsValidationError{
						field:  fmt.Sprintf("BenefitListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionTypeIconsValidationError{
						field:  fmt.Sprintf("BenefitListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionTypeIconsValidationError{
					field:  fmt.Sprintf("BenefitListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MaxElementsPerRow

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeIconsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeIconsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeIconsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionTypeIconsMultiError(errors)
	}

	return nil
}

// SectionTypeIconsMultiError is an error wrapping multiple validation errors
// returned by SectionTypeIcons.ValidateAll() if the designated constraints
// aren't met.
type SectionTypeIconsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionTypeIconsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionTypeIconsMultiError) AllErrors() []error { return m }

// SectionTypeIconsValidationError is the validation error returned by
// SectionTypeIcons.Validate if the designated constraints aren't met.
type SectionTypeIconsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionTypeIconsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionTypeIconsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionTypeIconsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionTypeIconsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionTypeIconsValidationError) ErrorName() string { return "SectionTypeIconsValidationError" }

// Error satisfies the builtin error interface
func (e SectionTypeIconsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionTypeIcons.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionTypeIconsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionTypeIconsValidationError{}

// Validate checks the field values on SectionTypeText with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SectionTypeText) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionTypeText with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionTypeTextMultiError, or nil if none found.
func (m *SectionTypeText) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionTypeText) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeTextValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeTextValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeTextValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeTextValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeTextValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeTextValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionTypeTextMultiError(errors)
	}

	return nil
}

// SectionTypeTextMultiError is an error wrapping multiple validation errors
// returned by SectionTypeText.ValidateAll() if the designated constraints
// aren't met.
type SectionTypeTextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionTypeTextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionTypeTextMultiError) AllErrors() []error { return m }

// SectionTypeTextValidationError is the validation error returned by
// SectionTypeText.Validate if the designated constraints aren't met.
type SectionTypeTextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionTypeTextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionTypeTextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionTypeTextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionTypeTextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionTypeTextValidationError) ErrorName() string { return "SectionTypeTextValidationError" }

// Error satisfies the builtin error interface
func (e SectionTypeTextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionTypeText.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionTypeTextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionTypeTextValidationError{}

// Validate checks the field values on SectionTypeHeader with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SectionTypeHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionTypeHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionTypeHeaderMultiError, or nil if none found.
func (m *SectionTypeHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionTypeHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "TitleImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "TitleImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeHeaderValidationError{
				field:  "TitleImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTextOverTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "TextOverTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "TextOverTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTextOverTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeHeaderValidationError{
				field:  "TextOverTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeHeaderValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeHeaderValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitleImageShadow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "TitleImageShadow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "TitleImageShadow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleImageShadow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeHeaderValidationError{
				field:  "TitleImageShadow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPoweredBy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "PoweredBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeHeaderValidationError{
					field:  "PoweredBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPoweredBy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeHeaderValidationError{
				field:  "PoweredBy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionTypeHeaderMultiError(errors)
	}

	return nil
}

// SectionTypeHeaderMultiError is an error wrapping multiple validation errors
// returned by SectionTypeHeader.ValidateAll() if the designated constraints
// aren't met.
type SectionTypeHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionTypeHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionTypeHeaderMultiError) AllErrors() []error { return m }

// SectionTypeHeaderValidationError is the validation error returned by
// SectionTypeHeader.Validate if the designated constraints aren't met.
type SectionTypeHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionTypeHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionTypeHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionTypeHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionTypeHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionTypeHeaderValidationError) ErrorName() string {
	return "SectionTypeHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e SectionTypeHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionTypeHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionTypeHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionTypeHeaderValidationError{}

// Validate checks the field values on SectionTypeCarousel with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SectionTypeCarousel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionTypeCarousel with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SectionTypeCarouselMultiError, or nil if none found.
func (m *SectionTypeCarousel) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionTypeCarousel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSectionTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeCarouselValidationError{
					field:  "SectionTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeCarouselValidationError{
					field:  "SectionTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeCarouselValidationError{
				field:  "SectionTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitListItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionTypeCarouselValidationError{
						field:  fmt.Sprintf("BenefitListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionTypeCarouselValidationError{
						field:  fmt.Sprintf("BenefitListItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionTypeCarouselValidationError{
					field:  fmt.Sprintf("BenefitListItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionTypeCarouselValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionTypeCarouselValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionTypeCarouselValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionTypeCarouselMultiError(errors)
	}

	return nil
}

// SectionTypeCarouselMultiError is an error wrapping multiple validation
// errors returned by SectionTypeCarousel.ValidateAll() if the designated
// constraints aren't met.
type SectionTypeCarouselMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionTypeCarouselMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionTypeCarouselMultiError) AllErrors() []error { return m }

// SectionTypeCarouselValidationError is the validation error returned by
// SectionTypeCarousel.Validate if the designated constraints aren't met.
type SectionTypeCarouselValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionTypeCarouselValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionTypeCarouselValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionTypeCarouselValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionTypeCarouselValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionTypeCarouselValidationError) ErrorName() string {
	return "SectionTypeCarouselValidationError"
}

// Error satisfies the builtin error interface
func (e SectionTypeCarouselValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionTypeCarousel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionTypeCarouselValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionTypeCarouselValidationError{}

// Validate checks the field values on OnboardingIntentSelectionScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *OnboardingIntentSelectionScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OnboardingIntentSelectionScreenOptions with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// OnboardingIntentSelectionScreenOptionsMultiError, or nil if none found.
func (m *OnboardingIntentSelectionScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingIntentSelectionScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIntents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
						field:  fmt.Sprintf("Intents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
						field:  fmt.Sprintf("Intents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OnboardingIntentSelectionScreenOptionsValidationError{
					field:  fmt.Sprintf("Intents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAdditionalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptionsValidationError{
				field:  "AdditionalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OnboardingIntentSelectionScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAdditionalIntentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "AdditionalIntentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptionsValidationError{
					field:  "AdditionalIntentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalIntentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptionsValidationError{
				field:  "AdditionalIntentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	// no validation rules for DefaultIntentIdentifier

	if len(errors) > 0 {
		return OnboardingIntentSelectionScreenOptionsMultiError(errors)
	}

	return nil
}

// OnboardingIntentSelectionScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// OnboardingIntentSelectionScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type OnboardingIntentSelectionScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingIntentSelectionScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingIntentSelectionScreenOptionsMultiError) AllErrors() []error { return m }

// OnboardingIntentSelectionScreenOptionsValidationError is the validation
// error returned by OnboardingIntentSelectionScreenOptions.Validate if the
// designated constraints aren't met.
type OnboardingIntentSelectionScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingIntentSelectionScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingIntentSelectionScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingIntentSelectionScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingIntentSelectionScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingIntentSelectionScreenOptionsValidationError) ErrorName() string {
	return "OnboardingIntentSelectionScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingIntentSelectionScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingIntentSelectionScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingIntentSelectionScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingIntentSelectionScreenOptionsValidationError{}

// Validate checks the field values on AdditionalIntentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdditionalIntentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdditionalIntentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdditionalIntentDetailsMultiError, or nil if none found.
func (m *AdditionalIntentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AdditionalIntentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IntentIdentifier

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AdditionalIntentDetailsValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AdditionalIntentDetailsValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AdditionalIntentDetailsValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AdditionalIntentDetailsMultiError(errors)
	}

	return nil
}

// AdditionalIntentDetailsMultiError is an error wrapping multiple validation
// errors returned by AdditionalIntentDetails.ValidateAll() if the designated
// constraints aren't met.
type AdditionalIntentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdditionalIntentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdditionalIntentDetailsMultiError) AllErrors() []error { return m }

// AdditionalIntentDetailsValidationError is the validation error returned by
// AdditionalIntentDetails.Validate if the designated constraints aren't met.
type AdditionalIntentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdditionalIntentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdditionalIntentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdditionalIntentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdditionalIntentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdditionalIntentDetailsValidationError) ErrorName() string {
	return "AdditionalIntentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AdditionalIntentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdditionalIntentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdditionalIntentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdditionalIntentDetailsValidationError{}

// Validate checks the field values on SendSmsDataScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendSmsDataScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendSmsDataScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendSmsDataScreenOptionsMultiError, or nil if none found.
func (m *SendSmsDataScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SendSmsDataScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoadingText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "LoadingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "LoadingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoadingText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptionsValidationError{
				field:  "LoadingText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptionsValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptionsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptionsValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendSmsDataScreenOptionsMultiError(errors)
	}

	return nil
}

// SendSmsDataScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by SendSmsDataScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type SendSmsDataScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendSmsDataScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendSmsDataScreenOptionsMultiError) AllErrors() []error { return m }

// SendSmsDataScreenOptionsValidationError is the validation error returned by
// SendSmsDataScreenOptions.Validate if the designated constraints aren't met.
type SendSmsDataScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendSmsDataScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendSmsDataScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendSmsDataScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendSmsDataScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendSmsDataScreenOptionsValidationError) ErrorName() string {
	return "SendSmsDataScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SendSmsDataScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendSmsDataScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendSmsDataScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendSmsDataScreenOptionsValidationError{}

// Validate checks the field values on
// OnboardingIntentSelectionScreenOptions_IntentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnboardingIntentSelectionScreenOptions_IntentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OnboardingIntentSelectionScreenOptions_IntentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// OnboardingIntentSelectionScreenOptions_IntentDetailsMultiError, or nil if
// none found.
func (m *OnboardingIntentSelectionScreenOptions_IntentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingIntentSelectionScreenOptions_IntentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IntentIdentifier

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsIntentDisabled

	if all {
		switch v := interface{}(m.GetFailureMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  "FailureMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
					field:  "FailureMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFailureMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{
				field:  "FailureMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OnboardingIntentSelectionScreenOptions_IntentDetailsMultiError(errors)
	}

	return nil
}

// OnboardingIntentSelectionScreenOptions_IntentDetailsMultiError is an error
// wrapping multiple validation errors returned by
// OnboardingIntentSelectionScreenOptions_IntentDetails.ValidateAll() if the
// designated constraints aren't met.
type OnboardingIntentSelectionScreenOptions_IntentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingIntentSelectionScreenOptions_IntentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingIntentSelectionScreenOptions_IntentDetailsMultiError) AllErrors() []error { return m }

// OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError is the
// validation error returned by
// OnboardingIntentSelectionScreenOptions_IntentDetails.Validate if the
// designated constraints aren't met.
type OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError) ErrorName() string {
	return "OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingIntentSelectionScreenOptions_IntentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingIntentSelectionScreenOptions_IntentDetailsValidationError{}

// Validate checks the field values on
// OnboardingIntentSelectionScreenOptions_IntentDetails_Tag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OnboardingIntentSelectionScreenOptions_IntentDetails_Tag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnboardingIntentSelectionScreenOptions_IntentDetails_TagMultiError, or nil
// if none found.
func (m *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OnboardingIntentSelectionScreenOptions_IntentDetails_TagMultiError(errors)
	}

	return nil
}

// OnboardingIntentSelectionScreenOptions_IntentDetails_TagMultiError is an
// error wrapping multiple validation errors returned by
// OnboardingIntentSelectionScreenOptions_IntentDetails_Tag.ValidateAll() if
// the designated constraints aren't met.
type OnboardingIntentSelectionScreenOptions_IntentDetails_TagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingIntentSelectionScreenOptions_IntentDetails_TagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingIntentSelectionScreenOptions_IntentDetails_TagMultiError) AllErrors() []error {
	return m
}

// OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError is
// the validation error returned by
// OnboardingIntentSelectionScreenOptions_IntentDetails_Tag.Validate if the
// designated constraints aren't met.
type OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError) ErrorName() string {
	return "OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingIntentSelectionScreenOptions_IntentDetails_Tag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingIntentSelectionScreenOptions_IntentDetails_TagValidationError{}

// Validate checks the field values on SendSmsDataScreenOptions_Footer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendSmsDataScreenOptions_Footer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendSmsDataScreenOptions_Footer with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendSmsDataScreenOptions_FooterMultiError, or nil if none found.
func (m *SendSmsDataScreenOptions_Footer) ValidateAll() error {
	return m.validate(true)
}

func (m *SendSmsDataScreenOptions_Footer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptions_FooterValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptions_FooterValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptions_FooterValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptions_FooterValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptions_FooterValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptions_FooterValidationError{
				field:  "VisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataScreenOptions_FooterValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataScreenOptions_FooterValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataScreenOptions_FooterValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendSmsDataScreenOptions_FooterMultiError(errors)
	}

	return nil
}

// SendSmsDataScreenOptions_FooterMultiError is an error wrapping multiple
// validation errors returned by SendSmsDataScreenOptions_Footer.ValidateAll()
// if the designated constraints aren't met.
type SendSmsDataScreenOptions_FooterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendSmsDataScreenOptions_FooterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendSmsDataScreenOptions_FooterMultiError) AllErrors() []error { return m }

// SendSmsDataScreenOptions_FooterValidationError is the validation error
// returned by SendSmsDataScreenOptions_Footer.Validate if the designated
// constraints aren't met.
type SendSmsDataScreenOptions_FooterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendSmsDataScreenOptions_FooterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendSmsDataScreenOptions_FooterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendSmsDataScreenOptions_FooterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendSmsDataScreenOptions_FooterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendSmsDataScreenOptions_FooterValidationError) ErrorName() string {
	return "SendSmsDataScreenOptions_FooterValidationError"
}

// Error satisfies the builtin error interface
func (e SendSmsDataScreenOptions_FooterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendSmsDataScreenOptions_Footer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendSmsDataScreenOptions_FooterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendSmsDataScreenOptions_FooterValidationError{}
