// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/onboarding/fi_lite_screen_options.proto

package onboarding

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Entry point for the benefit screen, will be used to control redirection after onboarding is complete
type FeatureOnboardingEntryPoint int32

const (
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED    FeatureOnboardingEntryPoint = 0
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_JUMP           FeatureOnboardingEntryPoint = 1
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_MF             FeatureOnboardingEntryPoint = 2
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_SD             FeatureOnboardingEntryPoint = 3
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_FD             FeatureOnboardingEntryPoint = 4
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_USS            FeatureOnboardingEntryPoint = 5
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_CC             FeatureOnboardingEntryPoint = 6
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_DC             FeatureOnboardingEntryPoint = 7
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_AUTO_PAY       FeatureOnboardingEntryPoint = 8
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_PL             FeatureOnboardingEntryPoint = 9
	FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_BOTTOM_NAV_BAR FeatureOnboardingEntryPoint = 10
)

// Enum value maps for FeatureOnboardingEntryPoint.
var (
	FeatureOnboardingEntryPoint_name = map[int32]string{
		0:  "FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED",
		1:  "FEATURE_ONBOARDING_ENTRY_POINT_JUMP",
		2:  "FEATURE_ONBOARDING_ENTRY_POINT_MF",
		3:  "FEATURE_ONBOARDING_ENTRY_POINT_SD",
		4:  "FEATURE_ONBOARDING_ENTRY_POINT_FD",
		5:  "FEATURE_ONBOARDING_ENTRY_POINT_USS",
		6:  "FEATURE_ONBOARDING_ENTRY_POINT_CC",
		7:  "FEATURE_ONBOARDING_ENTRY_POINT_DC",
		8:  "FEATURE_ONBOARDING_ENTRY_POINT_AUTO_PAY",
		9:  "FEATURE_ONBOARDING_ENTRY_POINT_PL",
		10: "FEATURE_ONBOARDING_ENTRY_POINT_BOTTOM_NAV_BAR",
	}
	FeatureOnboardingEntryPoint_value = map[string]int32{
		"FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED":    0,
		"FEATURE_ONBOARDING_ENTRY_POINT_JUMP":           1,
		"FEATURE_ONBOARDING_ENTRY_POINT_MF":             2,
		"FEATURE_ONBOARDING_ENTRY_POINT_SD":             3,
		"FEATURE_ONBOARDING_ENTRY_POINT_FD":             4,
		"FEATURE_ONBOARDING_ENTRY_POINT_USS":            5,
		"FEATURE_ONBOARDING_ENTRY_POINT_CC":             6,
		"FEATURE_ONBOARDING_ENTRY_POINT_DC":             7,
		"FEATURE_ONBOARDING_ENTRY_POINT_AUTO_PAY":       8,
		"FEATURE_ONBOARDING_ENTRY_POINT_PL":             9,
		"FEATURE_ONBOARDING_ENTRY_POINT_BOTTOM_NAV_BAR": 10,
	}
)

func (x FeatureOnboardingEntryPoint) Enum() *FeatureOnboardingEntryPoint {
	p := new(FeatureOnboardingEntryPoint)
	*p = x
	return p
}

func (x FeatureOnboardingEntryPoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeatureOnboardingEntryPoint) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_enumTypes[0].Descriptor()
}

func (FeatureOnboardingEntryPoint) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_enumTypes[0]
}

func (x FeatureOnboardingEntryPoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeatureOnboardingEntryPoint.Descriptor instead.
func (FeatureOnboardingEntryPoint) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{0}
}

type SmsDataType int32

const (
	SmsDataType_SMS_DATA_TYPE_UNSPECIFIED SmsDataType = 0
	SmsDataType_SMS_DATA_TYPE_PAN         SmsDataType = 1
)

// Enum value maps for SmsDataType.
var (
	SmsDataType_name = map[int32]string{
		0: "SMS_DATA_TYPE_UNSPECIFIED",
		1: "SMS_DATA_TYPE_PAN",
	}
	SmsDataType_value = map[string]int32{
		"SMS_DATA_TYPE_UNSPECIFIED": 0,
		"SMS_DATA_TYPE_PAN":         1,
	}
)

func (x SmsDataType) Enum() *SmsDataType {
	p := new(SmsDataType)
	*p = x
	return p
}

func (x SmsDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SmsDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_enumTypes[1].Descriptor()
}

func (SmsDataType) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_enumTypes[1]
}

func (x SmsDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SmsDataType.Descriptor instead.
func (SmsDataType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{1}
}

type FeatureBenefitsScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header                      *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Sections                    []*Section                                 `protobuf:"bytes,2,rep,name=sections,proto3" json:"sections,omitempty"`
	Ctas                        []*deeplink.Cta                            `protobuf:"bytes,3,rep,name=ctas,proto3" json:"ctas,omitempty"`
	BackgroundColour            *widget.BackgroundColour                   `protobuf:"bytes,4,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	DividerColour               *widget.BackgroundColour                   `protobuf:"bytes,5,opt,name=divider_colour,json=dividerColour,proto3" json:"divider_colour,omitempty"`
	BgImage                     *common.VisualElement                      `protobuf:"bytes,6,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	FeatureOnboardingEntryPoint string                                     `protobuf:"bytes,7,opt,name=feature_onboarding_entry_point,json=featureOnboardingEntryPoint,proto3" json:"feature_onboarding_entry_point,omitempty"`
	// flag to decide whether to fetch data from RPC or use screen options instead
	ToFetchScreenOptionsData common.BooleanEnum `protobuf:"varint,8,opt,name=to_fetch_screen_options_data,json=toFetchScreenOptionsData,proto3,enum=api.typesv2.common.BooleanEnum" json:"to_fetch_screen_options_data,omitempty"`
	// TNC text which is getting displayed above the CTA
	TncText *common.Text `protobuf:"bytes,9,opt,name=tnc_text,json=tncText,proto3" json:"tnc_text,omitempty"`
	// Background color for the bottom section which have CTA and tnc text
	BottomSectionBg *widget.BackgroundColour `protobuf:"bytes,10,opt,name=bottom_section_bg,json=bottomSectionBg,proto3" json:"bottom_section_bg,omitempty"`
}

func (x *FeatureBenefitsScreenOptions) Reset() {
	*x = FeatureBenefitsScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureBenefitsScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureBenefitsScreenOptions) ProtoMessage() {}

func (x *FeatureBenefitsScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureBenefitsScreenOptions.ProtoReflect.Descriptor instead.
func (*FeatureBenefitsScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *FeatureBenefitsScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetSections() []*Section {
	if x != nil {
		return x.Sections
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetCtas() []*deeplink.Cta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetDividerColour() *widget.BackgroundColour {
	if x != nil {
		return x.DividerColour
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetBgImage() *common.VisualElement {
	if x != nil {
		return x.BgImage
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetFeatureOnboardingEntryPoint() string {
	if x != nil {
		return x.FeatureOnboardingEntryPoint
	}
	return ""
}

func (x *FeatureBenefitsScreenOptions) GetToFetchScreenOptionsData() common.BooleanEnum {
	if x != nil {
		return x.ToFetchScreenOptionsData
	}
	return common.BooleanEnum(0)
}

func (x *FeatureBenefitsScreenOptions) GetTncText() *common.Text {
	if x != nil {
		return x.TncText
	}
	return nil
}

func (x *FeatureBenefitsScreenOptions) GetBottomSectionBg() *widget.BackgroundColour {
	if x != nil {
		return x.BottomSectionBg
	}
	return nil
}

type Section struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Type:
	//
	//	*Section_SectionTypeList
	//	*Section_SectionTypeIcons
	//	*Section_SectionTypeText
	//	*Section_SectionTypeHeader
	//	*Section_SectionTypeCarousel
	//	*Section_SectionTypeItc
	Type isSection_Type `protobuf_oneof:"type"`
}

func (x *Section) Reset() {
	*x = Section{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Section) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Section) ProtoMessage() {}

func (x *Section) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Section.ProtoReflect.Descriptor instead.
func (*Section) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{1}
}

func (m *Section) GetType() isSection_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (x *Section) GetSectionTypeList() *SectionTypeList {
	if x, ok := x.GetType().(*Section_SectionTypeList); ok {
		return x.SectionTypeList
	}
	return nil
}

func (x *Section) GetSectionTypeIcons() *SectionTypeIcons {
	if x, ok := x.GetType().(*Section_SectionTypeIcons); ok {
		return x.SectionTypeIcons
	}
	return nil
}

func (x *Section) GetSectionTypeText() *SectionTypeText {
	if x, ok := x.GetType().(*Section_SectionTypeText); ok {
		return x.SectionTypeText
	}
	return nil
}

func (x *Section) GetSectionTypeHeader() *SectionTypeHeader {
	if x, ok := x.GetType().(*Section_SectionTypeHeader); ok {
		return x.SectionTypeHeader
	}
	return nil
}

func (x *Section) GetSectionTypeCarousel() *SectionTypeCarousel {
	if x, ok := x.GetType().(*Section_SectionTypeCarousel); ok {
		return x.SectionTypeCarousel
	}
	return nil
}

func (x *Section) GetSectionTypeItc() *SectionTypeIconTextComponent {
	if x, ok := x.GetType().(*Section_SectionTypeItc); ok {
		return x.SectionTypeItc
	}
	return nil
}

type isSection_Type interface {
	isSection_Type()
}

type Section_SectionTypeList struct {
	SectionTypeList *SectionTypeList `protobuf:"bytes,1,opt,name=section_type_list,json=sectionTypeList,proto3,oneof"`
}

type Section_SectionTypeIcons struct {
	SectionTypeIcons *SectionTypeIcons `protobuf:"bytes,2,opt,name=section_type_icons,json=sectionTypeIcons,proto3,oneof"`
}

type Section_SectionTypeText struct {
	SectionTypeText *SectionTypeText `protobuf:"bytes,3,opt,name=section_type_text,json=sectionTypeText,proto3,oneof"`
}

type Section_SectionTypeHeader struct {
	SectionTypeHeader *SectionTypeHeader `protobuf:"bytes,4,opt,name=section_type_header,json=sectionTypeHeader,proto3,oneof"`
}

type Section_SectionTypeCarousel struct {
	SectionTypeCarousel *SectionTypeCarousel `protobuf:"bytes,5,opt,name=section_type_carousel,json=sectionTypeCarousel,proto3,oneof"`
}

type Section_SectionTypeItc struct {
	SectionTypeItc *SectionTypeIconTextComponent `protobuf:"bytes,6,opt,name=section_type_itc,json=sectionTypeItc,proto3,oneof"`
}

func (*Section_SectionTypeList) isSection_Type() {}

func (*Section_SectionTypeIcons) isSection_Type() {}

func (*Section_SectionTypeText) isSection_Type() {}

func (*Section_SectionTypeHeader) isSection_Type() {}

func (*Section_SectionTypeCarousel) isSection_Type() {}

func (*Section_SectionTypeItc) isSection_Type() {}

type SectionTypeIconTextComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectionItc *ui.IconTextComponent `protobuf:"bytes,1,opt,name=section_itc,json=sectionItc,proto3" json:"section_itc,omitempty"`
}

func (x *SectionTypeIconTextComponent) Reset() {
	*x = SectionTypeIconTextComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionTypeIconTextComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionTypeIconTextComponent) ProtoMessage() {}

func (x *SectionTypeIconTextComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionTypeIconTextComponent.ProtoReflect.Descriptor instead.
func (*SectionTypeIconTextComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *SectionTypeIconTextComponent) GetSectionItc() *ui.IconTextComponent {
	if x != nil {
		return x.SectionItc
	}
	return nil
}

// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1696-94670&t=bBH0T1l6cGtjcfIN-4
type SectionTypeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectionHeader    *common.Text             `protobuf:"bytes,1,opt,name=section_header,json=sectionHeader,proto3" json:"section_header,omitempty"`
	BenefitListItems []*ui.IconTextComponent  `protobuf:"bytes,2,rep,name=benefit_list_items,json=benefitListItems,proto3" json:"benefit_list_items,omitempty"`
	BgColor          *widget.BackgroundColour `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"` // This controls the bg colour of the entire screen
}

func (x *SectionTypeList) Reset() {
	*x = SectionTypeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionTypeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionTypeList) ProtoMessage() {}

func (x *SectionTypeList) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionTypeList.ProtoReflect.Descriptor instead.
func (*SectionTypeList) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *SectionTypeList) GetSectionHeader() *common.Text {
	if x != nil {
		return x.SectionHeader
	}
	return nil
}

func (x *SectionTypeList) GetBenefitListItems() []*ui.IconTextComponent {
	if x != nil {
		return x.BenefitListItems
	}
	return nil
}

func (x *SectionTypeList) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1696-95144&t=bBH0T1l6cGtjcfIN-4
type SectionTypeIcons struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectionTitle      *common.Text                    `protobuf:"bytes,1,opt,name=section_title,json=sectionTitle,proto3" json:"section_title,omitempty"`
	SectionInnerTitle *common.Text                    `protobuf:"bytes,2,opt,name=section_inner_title,json=sectionInnerTitle,proto3" json:"section_inner_title,omitempty"`
	BenefitListItems  []*ui.VerticalIconTextComponent `protobuf:"bytes,3,rep,name=benefit_list_items,json=benefitListItems,proto3" json:"benefit_list_items,omitempty"`
	MaxElementsPerRow int32                           `protobuf:"varint,4,opt,name=max_elements_per_row,json=maxElementsPerRow,proto3" json:"max_elements_per_row,omitempty"`
	BgColor           *widget.BackgroundColour        `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *SectionTypeIcons) Reset() {
	*x = SectionTypeIcons{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionTypeIcons) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionTypeIcons) ProtoMessage() {}

func (x *SectionTypeIcons) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionTypeIcons.ProtoReflect.Descriptor instead.
func (*SectionTypeIcons) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *SectionTypeIcons) GetSectionTitle() *common.Text {
	if x != nil {
		return x.SectionTitle
	}
	return nil
}

func (x *SectionTypeIcons) GetSectionInnerTitle() *common.Text {
	if x != nil {
		return x.SectionInnerTitle
	}
	return nil
}

func (x *SectionTypeIcons) GetBenefitListItems() []*ui.VerticalIconTextComponent {
	if x != nil {
		return x.BenefitListItems
	}
	return nil
}

func (x *SectionTypeIcons) GetMaxElementsPerRow() int32 {
	if x != nil {
		return x.MaxElementsPerRow
	}
	return 0
}

func (x *SectionTypeIcons) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

type SectionTypeText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text    *ui.IconTextComponent    `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	BgColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *SectionTypeText) Reset() {
	*x = SectionTypeText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionTypeText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionTypeText) ProtoMessage() {}

func (x *SectionTypeText) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionTypeText.ProtoReflect.Descriptor instead.
func (*SectionTypeText) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *SectionTypeText) GetText() *ui.IconTextComponent {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *SectionTypeText) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

type SectionTypeHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleImage       *common.VisualElement `protobuf:"bytes,1,opt,name=title_image,json=titleImage,proto3" json:"title_image,omitempty"`
	TextOverTitle    *common.Text          `protobuf:"bytes,2,opt,name=text_over_title,json=textOverTitle,proto3" json:"text_over_title,omitempty"`
	Title            *common.Text          `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle         *common.Text          `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	TitleImageShadow *widget.Shadow        `protobuf:"bytes,5,opt,name=title_image_shadow,json=titleImageShadow,proto3" json:"title_image_shadow,omitempty"`
	// Powered by Federal or Alpaca
	PoweredBy *ui.IconTextComponent `protobuf:"bytes,6,opt,name=powered_by,json=poweredBy,proto3" json:"powered_by,omitempty"`
}

func (x *SectionTypeHeader) Reset() {
	*x = SectionTypeHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionTypeHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionTypeHeader) ProtoMessage() {}

func (x *SectionTypeHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionTypeHeader.ProtoReflect.Descriptor instead.
func (*SectionTypeHeader) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *SectionTypeHeader) GetTitleImage() *common.VisualElement {
	if x != nil {
		return x.TitleImage
	}
	return nil
}

func (x *SectionTypeHeader) GetTextOverTitle() *common.Text {
	if x != nil {
		return x.TextOverTitle
	}
	return nil
}

func (x *SectionTypeHeader) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SectionTypeHeader) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *SectionTypeHeader) GetTitleImageShadow() *widget.Shadow {
	if x != nil {
		return x.TitleImageShadow
	}
	return nil
}

func (x *SectionTypeHeader) GetPoweredBy() *ui.IconTextComponent {
	if x != nil {
		return x.PoweredBy
	}
	return nil
}

type SectionTypeCarousel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectionTitle     *common.Text                    `protobuf:"bytes,1,opt,name=section_title,json=sectionTitle,proto3" json:"section_title,omitempty"`
	BenefitListItems []*ui.VerticalIconTextComponent `protobuf:"bytes,2,rep,name=benefit_list_items,json=benefitListItems,proto3" json:"benefit_list_items,omitempty"`
	BgColor          *widget.BackgroundColour        `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *SectionTypeCarousel) Reset() {
	*x = SectionTypeCarousel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionTypeCarousel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionTypeCarousel) ProtoMessage() {}

func (x *SectionTypeCarousel) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionTypeCarousel.ProtoReflect.Descriptor instead.
func (*SectionTypeCarousel) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *SectionTypeCarousel) GetSectionTitle() *common.Text {
	if x != nil {
		return x.SectionTitle
	}
	return nil
}

func (x *SectionTypeCarousel) GetBenefitListItems() []*ui.VerticalIconTextComponent {
	if x != nil {
		return x.BenefitListItems
	}
	return nil
}

func (x *SectionTypeCarousel) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

// screen options for ONBOARDING_INTENT_SELECTION
// https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=20001%3A66397&mode=dev
type OnboardingIntentSelectionScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header   *deeplink_screen_option.ScreenOptionHeader              `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Title    *common.Text                                            `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle *common.Text                                            `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	BgColor  *widget.BackgroundColour                                `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	Intents  []*OnboardingIntentSelectionScreenOptions_IntentDetails `protobuf:"bytes,5,rep,name=intents,proto3" json:"intents,omitempty"`
	// additional_info is the text displayed right above the "Next" CTA
	AdditionalInfo *common.Text    `protobuf:"bytes,7,opt,name=additional_info,json=additionalInfo,proto3" json:"additional_info,omitempty"`
	Ctas           []*deeplink.Cta `protobuf:"bytes,6,rep,name=ctas,proto3" json:"ctas,omitempty"`
	// Any additional intent not already present in "intents" field, which can be selected by the user
	// Ex - Fi Lite intent details
	AdditionalIntentDetails *AdditionalIntentDetails `protobuf:"bytes,8,opt,name=additional_intent_details,json=additionalIntentDetails,proto3" json:"additional_intent_details,omitempty"`
	// Entry point from where user entered the ONBOARDING_INTENT_SELECTION screen
	// The entry point from intent screen deeplink should be passed via client to the SetOnboardingIntent call
	// string version of user.onboarding.IntentSelectionEntryPoint
	EntryPoint string `protobuf:"bytes,9,opt,name=entry_point,json=entryPoint,proto3" json:"entry_point,omitempty"`
	// If default intent identifier is matching with any of the intent identifiers, it will be selected by default
	DefaultIntentIdentifier string `protobuf:"bytes,10,opt,name=default_intent_identifier,json=defaultIntentIdentifier,proto3" json:"default_intent_identifier,omitempty"`
}

func (x *OnboardingIntentSelectionScreenOptions) Reset() {
	*x = OnboardingIntentSelectionScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingIntentSelectionScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingIntentSelectionScreenOptions) ProtoMessage() {}

func (x *OnboardingIntentSelectionScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingIntentSelectionScreenOptions.ProtoReflect.Descriptor instead.
func (*OnboardingIntentSelectionScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{8}
}

func (x *OnboardingIntentSelectionScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetIntents() []*OnboardingIntentSelectionScreenOptions_IntentDetails {
	if x != nil {
		return x.Intents
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetAdditionalInfo() *common.Text {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetCtas() []*deeplink.Cta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetAdditionalIntentDetails() *AdditionalIntentDetails {
	if x != nil {
		return x.AdditionalIntentDetails
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions) GetEntryPoint() string {
	if x != nil {
		return x.EntryPoint
	}
	return ""
}

func (x *OnboardingIntentSelectionScreenOptions) GetDefaultIntentIdentifier() string {
	if x != nil {
		return x.DefaultIntentIdentifier
	}
	return ""
}

type AdditionalIntentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Intent identifier has to be sent to the API that will fetch
	// The next screen to display for this particular selection
	IntentIdentifier string `protobuf:"bytes,1,opt,name=intent_identifier,json=intentIdentifier,proto3" json:"intent_identifier,omitempty"`
	// Details to be shown
	// Example - "I’m only looking to make UPI payments" text
	// NOTE - If there is a deeplink inside details call that on click of this component
	// Otherwise call SetOnboardingIntent API to set intent
	Details *ui.IconTextComponent `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *AdditionalIntentDetails) Reset() {
	*x = AdditionalIntentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalIntentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalIntentDetails) ProtoMessage() {}

func (x *AdditionalIntentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalIntentDetails.ProtoReflect.Descriptor instead.
func (*AdditionalIntentDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{9}
}

func (x *AdditionalIntentDetails) GetIntentIdentifier() string {
	if x != nil {
		return x.IntentIdentifier
	}
	return ""
}

func (x *AdditionalIntentDetails) GetDetails() *ui.IconTextComponent {
	if x != nil {
		return x.Details
	}
	return nil
}

// screen options for SEND_SMS_DATA screen
type SendSmsDataScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// type of sms data to be sent
	SmsDataTypes []SmsDataType                    `protobuf:"varint,2,rep,packed,name=sms_data_types,json=smsDataTypes,proto3,enum=api.typesv2.deeplink_screen_option.onboarding.SmsDataType" json:"sms_data_types,omitempty"`
	LoadingText  *common.Text                     `protobuf:"bytes,3,opt,name=loading_text,json=loadingText,proto3" json:"loading_text,omitempty"`
	Footer       *SendSmsDataScreenOptions_Footer `protobuf:"bytes,4,opt,name=footer,proto3" json:"footer,omitempty"`
	Deeplink     *deeplink.Deeplink               `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *SendSmsDataScreenOptions) Reset() {
	*x = SendSmsDataScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsDataScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsDataScreenOptions) ProtoMessage() {}

func (x *SendSmsDataScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsDataScreenOptions.ProtoReflect.Descriptor instead.
func (*SendSmsDataScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{10}
}

func (x *SendSmsDataScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SendSmsDataScreenOptions) GetSmsDataTypes() []SmsDataType {
	if x != nil {
		return x.SmsDataTypes
	}
	return nil
}

func (x *SendSmsDataScreenOptions) GetLoadingText() *common.Text {
	if x != nil {
		return x.LoadingText
	}
	return nil
}

func (x *SendSmsDataScreenOptions) GetFooter() *SendSmsDataScreenOptions_Footer {
	if x != nil {
		return x.Footer
	}
	return nil
}

func (x *SendSmsDataScreenOptions) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type OnboardingIntentSelectionScreenOptions_IntentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// intent identifier has to be sent to the API that will fetch
	// the next screen to display for this particular selection
	IntentIdentifier string                                                      `protobuf:"bytes,1,opt,name=intent_identifier,json=intentIdentifier,proto3" json:"intent_identifier,omitempty"`
	Title            *common.Text                                                `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon             *common.VisualElement                                       `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Tags             []*OnboardingIntentSelectionScreenOptions_IntentDetails_Tag `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	// If a particular intent is not applicable for the user then this flag will disable the selection for them
	IsIntentDisabled bool `protobuf:"varint,7,opt,name=is_intent_disabled,json=isIntentDisabled,proto3" json:"is_intent_disabled,omitempty"`
	// If a particular intent is disabled then the following message will be shown to the user
	FailureMessage *ui.IconTextComponent `protobuf:"bytes,6,opt,name=failure_message,json=failureMessage,proto3" json:"failure_message,omitempty"`
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) Reset() {
	*x = OnboardingIntentSelectionScreenOptions_IntentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingIntentSelectionScreenOptions_IntentDetails) ProtoMessage() {}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingIntentSelectionScreenOptions_IntentDetails.ProtoReflect.Descriptor instead.
func (*OnboardingIntentSelectionScreenOptions_IntentDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{8, 0}
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) GetIntentIdentifier() string {
	if x != nil {
		return x.IntentIdentifier
	}
	return ""
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) GetTags() []*OnboardingIntentSelectionScreenOptions_IntentDetails_Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) GetIsIntentDisabled() bool {
	if x != nil {
		return x.IsIntentDisabled
	}
	return false
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails) GetFailureMessage() *ui.IconTextComponent {
	if x != nil {
		return x.FailureMessage
	}
	return nil
}

type OnboardingIntentSelectionScreenOptions_IntentDetails_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text    *common.Text             `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	BgColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) Reset() {
	*x = OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) ProtoMessage() {}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingIntentSelectionScreenOptions_IntentDetails_Tag.ProtoReflect.Descriptor instead.
func (*OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{8, 0, 0}
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *OnboardingIntentSelectionScreenOptions_IntentDetails_Tag) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

type SendSmsDataScreenOptions_Footer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text          *common.Text             `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	VisualElement *common.VisualElement    `protobuf:"bytes,2,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	BgColor       *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *SendSmsDataScreenOptions_Footer) Reset() {
	*x = SendSmsDataScreenOptions_Footer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsDataScreenOptions_Footer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsDataScreenOptions_Footer) ProtoMessage() {}

func (x *SendSmsDataScreenOptions_Footer) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsDataScreenOptions_Footer.ProtoReflect.Descriptor instead.
func (*SendSmsDataScreenOptions_Footer) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP(), []int{10, 0}
}

func (x *SendSmsDataScreenOptions_Footer) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *SendSmsDataScreenOptions_Footer) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *SendSmsDataScreenOptions_Footer) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2d, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x97, 0x06, 0x0a, 0x1c, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x08, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2a, 0x0a,
	0x04, 0x63, 0x74, 0x61, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x43, 0x74, 0x61, 0x52, 0x04, 0x63, 0x74, 0x61, 0x73, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x55, 0x0a, 0x0e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0d,
	0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x3c, 0x0a,
	0x08, 0x62, 0x67, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x07, 0x62, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x1e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x1b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x5f, 0x0a, 0x1c, 0x74, 0x6f, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x18, 0x74, 0x6f, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x33, 0x0a, 0x08, 0x74, 0x6e, 0x63, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x74,
	0x6e, 0x63, 0x54, 0x65, 0x78, 0x74, 0x12, 0x5a, 0x0a, 0x11, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x0f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x67, 0x22, 0xc5, 0x05, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6c,
	0x0a, 0x11, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6f, 0x0a, 0x12,
	0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x10, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x12, 0x6c, 0x0a,
	0x11, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x72, 0x0a, 0x13, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x48, 0x00, 0x52, 0x11, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x78, 0x0a, 0x15, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73,
	0x65, 0x6c, 0x48, 0x00, 0x52, 0x13, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x12, 0x77, 0x0a, 0x10, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x48, 0x00, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x74, 0x63, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x62, 0x0a, 0x1c, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0b, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x63, 0x22, 0xee,
	0x01, 0x0a, 0x0f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x3f, 0x0a, 0x0e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x12, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22,
	0xf0, 0x02, 0x0a, 0x10, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x63, 0x6f, 0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x48, 0x0a, 0x13, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x11, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x57, 0x0a,
	0x12, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69,
	0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x22, 0x93, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x49, 0x0a,
	0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x95, 0x03, 0x0a, 0x11, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x42,
	0x0a, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x74, 0x65, 0x78, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x10, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x12, 0x40,
	0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x65, 0x64, 0x42, 0x79,
	0x22, 0xf8, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x10,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x9c, 0x0a, 0x0a, 0x26,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x49, 0x0a, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x7d, 0x0a, 0x07, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x04, 0x63, 0x74, 0x61,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52,
	0x04, 0x63, 0x74, 0x61, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x19, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x19, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a, 0xa0, 0x04, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x7b, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x67, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x2e, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x4a, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x7e, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x2c, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x22, 0x83, 0x01, 0x0a, 0x17, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0xf8, 0x04, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x60, 0x0a,
	0x0e, 0x73, 0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x73, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x3b, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x12, 0x66, 0x0a, 0x06,
	0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x6f,
	0x6f, 0x74, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x1a, 0xcb, 0x01,
	0x0a, 0x06, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x2a, 0xe8, 0x03, 0x0a, 0x1b,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x2a, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x4a, 0x55,
	0x4d, 0x50, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x4d, 0x46, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x53, 0x44,
	0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x46, 0x44, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x53, 0x10,
	0x05, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x5f, 0x43, 0x43, 0x10, 0x06, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45,
	0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x43, 0x10, 0x07, 0x12,
	0x2b, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e,
	0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x08, 0x12, 0x25, 0x0a, 0x21,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x50,
	0x4c, 0x10, 0x09, 0x12, 0x31, 0x0a, 0x2d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x4e, 0x41, 0x56,
	0x5f, 0x42, 0x41, 0x52, 0x10, 0x0a, 0x2a, 0x43, 0x0a, 0x0b, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x4d, 0x53, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x4d, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x4e, 0x10, 0x01, 0x42, 0x8e, 0x01, 0x0a, 0x44,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_goTypes = []interface{}{
	(FeatureOnboardingEntryPoint)(0),                                 // 0: api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint
	(SmsDataType)(0),                                                 // 1: api.typesv2.deeplink_screen_option.onboarding.SmsDataType
	(*FeatureBenefitsScreenOptions)(nil),                             // 2: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions
	(*Section)(nil),                                                  // 3: api.typesv2.deeplink_screen_option.onboarding.Section
	(*SectionTypeIconTextComponent)(nil),                             // 4: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIconTextComponent
	(*SectionTypeList)(nil),                                          // 5: api.typesv2.deeplink_screen_option.onboarding.SectionTypeList
	(*SectionTypeIcons)(nil),                                         // 6: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIcons
	(*SectionTypeText)(nil),                                          // 7: api.typesv2.deeplink_screen_option.onboarding.SectionTypeText
	(*SectionTypeHeader)(nil),                                        // 8: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader
	(*SectionTypeCarousel)(nil),                                      // 9: api.typesv2.deeplink_screen_option.onboarding.SectionTypeCarousel
	(*OnboardingIntentSelectionScreenOptions)(nil),                   // 10: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions
	(*AdditionalIntentDetails)(nil),                                  // 11: api.typesv2.deeplink_screen_option.onboarding.AdditionalIntentDetails
	(*SendSmsDataScreenOptions)(nil),                                 // 12: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions
	(*OnboardingIntentSelectionScreenOptions_IntentDetails)(nil),     // 13: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails
	(*OnboardingIntentSelectionScreenOptions_IntentDetails_Tag)(nil), // 14: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.Tag
	(*SendSmsDataScreenOptions_Footer)(nil),                          // 15: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.Footer
	(*deeplink_screen_option.ScreenOptionHeader)(nil),                // 16: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.Cta)(nil),                                             // 17: frontend.deeplink.Cta
	(*widget.BackgroundColour)(nil),                                  // 18: api.typesv2.common.ui.widget.BackgroundColour
	(*common.VisualElement)(nil),                                     // 19: api.typesv2.common.VisualElement
	(common.BooleanEnum)(0),                                          // 20: api.typesv2.common.BooleanEnum
	(*common.Text)(nil),                                              // 21: api.typesv2.common.Text
	(*ui.IconTextComponent)(nil),                                     // 22: api.typesv2.ui.IconTextComponent
	(*ui.VerticalIconTextComponent)(nil),                             // 23: api.typesv2.ui.VerticalIconTextComponent
	(*widget.Shadow)(nil),                                            // 24: api.typesv2.common.ui.widget.Shadow
	(*deeplink.Deeplink)(nil),                                        // 25: frontend.deeplink.Deeplink
}
var file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_depIdxs = []int32{
	16, // 0: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	3,  // 1: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.sections:type_name -> api.typesv2.deeplink_screen_option.onboarding.Section
	17, // 2: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.ctas:type_name -> frontend.deeplink.Cta
	18, // 3: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	18, // 4: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.divider_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	19, // 5: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.bg_image:type_name -> api.typesv2.common.VisualElement
	20, // 6: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.to_fetch_screen_options_data:type_name -> api.typesv2.common.BooleanEnum
	21, // 7: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.tnc_text:type_name -> api.typesv2.common.Text
	18, // 8: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions.bottom_section_bg:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	5,  // 9: api.typesv2.deeplink_screen_option.onboarding.Section.section_type_list:type_name -> api.typesv2.deeplink_screen_option.onboarding.SectionTypeList
	6,  // 10: api.typesv2.deeplink_screen_option.onboarding.Section.section_type_icons:type_name -> api.typesv2.deeplink_screen_option.onboarding.SectionTypeIcons
	7,  // 11: api.typesv2.deeplink_screen_option.onboarding.Section.section_type_text:type_name -> api.typesv2.deeplink_screen_option.onboarding.SectionTypeText
	8,  // 12: api.typesv2.deeplink_screen_option.onboarding.Section.section_type_header:type_name -> api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader
	9,  // 13: api.typesv2.deeplink_screen_option.onboarding.Section.section_type_carousel:type_name -> api.typesv2.deeplink_screen_option.onboarding.SectionTypeCarousel
	4,  // 14: api.typesv2.deeplink_screen_option.onboarding.Section.section_type_itc:type_name -> api.typesv2.deeplink_screen_option.onboarding.SectionTypeIconTextComponent
	22, // 15: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIconTextComponent.section_itc:type_name -> api.typesv2.ui.IconTextComponent
	21, // 16: api.typesv2.deeplink_screen_option.onboarding.SectionTypeList.section_header:type_name -> api.typesv2.common.Text
	22, // 17: api.typesv2.deeplink_screen_option.onboarding.SectionTypeList.benefit_list_items:type_name -> api.typesv2.ui.IconTextComponent
	18, // 18: api.typesv2.deeplink_screen_option.onboarding.SectionTypeList.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	21, // 19: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIcons.section_title:type_name -> api.typesv2.common.Text
	21, // 20: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIcons.section_inner_title:type_name -> api.typesv2.common.Text
	23, // 21: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIcons.benefit_list_items:type_name -> api.typesv2.ui.VerticalIconTextComponent
	18, // 22: api.typesv2.deeplink_screen_option.onboarding.SectionTypeIcons.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	22, // 23: api.typesv2.deeplink_screen_option.onboarding.SectionTypeText.text:type_name -> api.typesv2.ui.IconTextComponent
	18, // 24: api.typesv2.deeplink_screen_option.onboarding.SectionTypeText.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	19, // 25: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader.title_image:type_name -> api.typesv2.common.VisualElement
	21, // 26: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader.text_over_title:type_name -> api.typesv2.common.Text
	21, // 27: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader.title:type_name -> api.typesv2.common.Text
	21, // 28: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader.subtitle:type_name -> api.typesv2.common.Text
	24, // 29: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader.title_image_shadow:type_name -> api.typesv2.common.ui.widget.Shadow
	22, // 30: api.typesv2.deeplink_screen_option.onboarding.SectionTypeHeader.powered_by:type_name -> api.typesv2.ui.IconTextComponent
	21, // 31: api.typesv2.deeplink_screen_option.onboarding.SectionTypeCarousel.section_title:type_name -> api.typesv2.common.Text
	23, // 32: api.typesv2.deeplink_screen_option.onboarding.SectionTypeCarousel.benefit_list_items:type_name -> api.typesv2.ui.VerticalIconTextComponent
	18, // 33: api.typesv2.deeplink_screen_option.onboarding.SectionTypeCarousel.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	16, // 34: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	21, // 35: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.title:type_name -> api.typesv2.common.Text
	21, // 36: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.subtitle:type_name -> api.typesv2.common.Text
	18, // 37: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	13, // 38: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.intents:type_name -> api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails
	21, // 39: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.additional_info:type_name -> api.typesv2.common.Text
	17, // 40: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.ctas:type_name -> frontend.deeplink.Cta
	11, // 41: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.additional_intent_details:type_name -> api.typesv2.deeplink_screen_option.onboarding.AdditionalIntentDetails
	22, // 42: api.typesv2.deeplink_screen_option.onboarding.AdditionalIntentDetails.details:type_name -> api.typesv2.ui.IconTextComponent
	16, // 43: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	1,  // 44: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.sms_data_types:type_name -> api.typesv2.deeplink_screen_option.onboarding.SmsDataType
	21, // 45: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.loading_text:type_name -> api.typesv2.common.Text
	15, // 46: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.footer:type_name -> api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.Footer
	25, // 47: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.deeplink:type_name -> frontend.deeplink.Deeplink
	21, // 48: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.title:type_name -> api.typesv2.common.Text
	19, // 49: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.icon:type_name -> api.typesv2.common.VisualElement
	14, // 50: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.tags:type_name -> api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.Tag
	22, // 51: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.failure_message:type_name -> api.typesv2.ui.IconTextComponent
	21, // 52: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.Tag.text:type_name -> api.typesv2.common.Text
	18, // 53: api.typesv2.deeplink_screen_option.onboarding.OnboardingIntentSelectionScreenOptions.IntentDetails.Tag.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	21, // 54: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.Footer.text:type_name -> api.typesv2.common.Text
	19, // 55: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.Footer.visual_element:type_name -> api.typesv2.common.VisualElement
	18, // 56: api.typesv2.deeplink_screen_option.onboarding.SendSmsDataScreenOptions.Footer.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	57, // [57:57] is the sub-list for method output_type
	57, // [57:57] is the sub-list for method input_type
	57, // [57:57] is the sub-list for extension type_name
	57, // [57:57] is the sub-list for extension extendee
	0,  // [0:57] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureBenefitsScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Section); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionTypeIconTextComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionTypeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionTypeIcons); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionTypeText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionTypeHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionTypeCarousel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingIntentSelectionScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalIntentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsDataScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingIntentSelectionScreenOptions_IntentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingIntentSelectionScreenOptions_IntentDetails_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsDataScreenOptions_Footer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Section_SectionTypeList)(nil),
		(*Section_SectionTypeIcons)(nil),
		(*Section_SectionTypeText)(nil),
		(*Section_SectionTypeHeader)(nil),
		(*Section_SectionTypeCarousel)(nil),
		(*Section_SectionTypeItc)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_depIdxs = nil
}
