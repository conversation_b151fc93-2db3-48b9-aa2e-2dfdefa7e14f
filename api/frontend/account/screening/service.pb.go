// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/account/screening/service.proto

package screening

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/epifi/be-common/api/rpc"
	uistate "github.com/epifi/gamma/api/frontend/account/screening/uistate"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreditReportVerificationStatus denotes status credit report verification
type CreditReportVerificationStatus int32

const (
	CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_UNSPECIFIED CreditReportVerificationStatus = 0
	// non terminal state denoting verification is still in progress
	CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_IN_PROGRESS CreditReportVerificationStatus = 1
	// terminal state denoting verification was successful
	CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL CreditReportVerificationStatus = 2
	// terminal state denoting verification failed
	CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_FAILED CreditReportVerificationStatus = 3
)

// Enum value maps for CreditReportVerificationStatus.
var (
	CreditReportVerificationStatus_name = map[int32]string{
		0: "CREDIT_REPORT_VERIFICATION_STATUS_UNSPECIFIED",
		1: "CREDIT_REPORT_VERIFICATION_STATUS_IN_PROGRESS",
		2: "CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL",
		3: "CREDIT_REPORT_VERIFICATION_STATUS_FAILED",
	}
	CreditReportVerificationStatus_value = map[string]int32{
		"CREDIT_REPORT_VERIFICATION_STATUS_UNSPECIFIED": 0,
		"CREDIT_REPORT_VERIFICATION_STATUS_IN_PROGRESS": 1,
		"CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL":  2,
		"CREDIT_REPORT_VERIFICATION_STATUS_FAILED":      3,
	}
)

func (x CreditReportVerificationStatus) Enum() *CreditReportVerificationStatus {
	p := new(CreditReportVerificationStatus)
	*p = x
	return p
}

func (x CreditReportVerificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditReportVerificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[0].Descriptor()
}

func (CreditReportVerificationStatus) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[0]
}

func (x CreditReportVerificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditReportVerificationStatus.Descriptor instead.
func (CreditReportVerificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{0}
}

type GetCompanyNamesResponseV2_Status int32

const (
	GetCompanyNamesResponseV2_OK       GetCompanyNamesResponseV2_Status = 0
	GetCompanyNamesResponseV2_INTERNAL GetCompanyNamesResponseV2_Status = 13
)

// Enum value maps for GetCompanyNamesResponseV2_Status.
var (
	GetCompanyNamesResponseV2_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetCompanyNamesResponseV2_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetCompanyNamesResponseV2_Status) Enum() *GetCompanyNamesResponseV2_Status {
	p := new(GetCompanyNamesResponseV2_Status)
	*p = x
	return p
}

func (x GetCompanyNamesResponseV2_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetCompanyNamesResponseV2_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[1].Descriptor()
}

func (GetCompanyNamesResponseV2_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[1]
}

func (x GetCompanyNamesResponseV2_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetCompanyNamesResponseV2_Status.Descriptor instead.
func (GetCompanyNamesResponseV2_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{15, 0}
}

type GetCompanyNamesResponse_Status int32

const (
	GetCompanyNamesResponse_OK       GetCompanyNamesResponse_Status = 0
	GetCompanyNamesResponse_INTERNAL GetCompanyNamesResponse_Status = 13
)

// Enum value maps for GetCompanyNamesResponse_Status.
var (
	GetCompanyNamesResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetCompanyNamesResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetCompanyNamesResponse_Status) Enum() *GetCompanyNamesResponse_Status {
	p := new(GetCompanyNamesResponse_Status)
	*p = x
	return p
}

func (x GetCompanyNamesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetCompanyNamesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[2].Descriptor()
}

func (GetCompanyNamesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[2]
}

func (x GetCompanyNamesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetCompanyNamesResponse_Status.Descriptor instead.
func (GetCompanyNamesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{16, 0}
}

type ProcessEmploymentDataResponse_Status int32

const (
	ProcessEmploymentDataResponse_OK               ProcessEmploymentDataResponse_Status = 0
	ProcessEmploymentDataResponse_INVALID_ARGUMENT ProcessEmploymentDataResponse_Status = 3
	ProcessEmploymentDataResponse_INTERNAL         ProcessEmploymentDataResponse_Status = 13
)

// Enum value maps for ProcessEmploymentDataResponse_Status.
var (
	ProcessEmploymentDataResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	ProcessEmploymentDataResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x ProcessEmploymentDataResponse_Status) Enum() *ProcessEmploymentDataResponse_Status {
	p := new(ProcessEmploymentDataResponse_Status)
	*p = x
	return p
}

func (x ProcessEmploymentDataResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessEmploymentDataResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[3].Descriptor()
}

func (ProcessEmploymentDataResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[3]
}

func (x ProcessEmploymentDataResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessEmploymentDataResponse_Status.Descriptor instead.
func (ProcessEmploymentDataResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{18, 0}
}

type CheckEmploymentVerificationStatusResponse_Status int32

const (
	CheckEmploymentVerificationStatusResponse_OK        CheckEmploymentVerificationStatusResponse_Status = 0
	CheckEmploymentVerificationStatusResponse_NOT_FOUND CheckEmploymentVerificationStatusResponse_Status = 5
	CheckEmploymentVerificationStatusResponse_INTERNAL  CheckEmploymentVerificationStatusResponse_Status = 13
)

// Enum value maps for CheckEmploymentVerificationStatusResponse_Status.
var (
	CheckEmploymentVerificationStatusResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	CheckEmploymentVerificationStatusResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x CheckEmploymentVerificationStatusResponse_Status) Enum() *CheckEmploymentVerificationStatusResponse_Status {
	p := new(CheckEmploymentVerificationStatusResponse_Status)
	*p = x
	return p
}

func (x CheckEmploymentVerificationStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckEmploymentVerificationStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[4].Descriptor()
}

func (CheckEmploymentVerificationStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[4]
}

func (x CheckEmploymentVerificationStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckEmploymentVerificationStatusResponse_Status.Descriptor instead.
func (CheckEmploymentVerificationStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{20, 0}
}

type UpdateEmploymentDataRequest_Provenance int32

const (
	UpdateEmploymentDataRequest_PROVENANCE_UNSPECIFIED UpdateEmploymentDataRequest_Provenance = 0
	// When client makes a call from profile, this enum needs to be set
	UpdateEmploymentDataRequest_PROVENANCE_PROFILE UpdateEmploymentDataRequest_Provenance = 1
	// When client makes a call from vkyc, this enum needs to be set
	UpdateEmploymentDataRequest_PROVENANCE_VKYC UpdateEmploymentDataRequest_Provenance = 2
	// when client makes a call from income occupation popup, this enum needs to be set
	UpdateEmploymentDataRequest_PROVENANCE_INC_OCC_DISCREPANCY_POPUP UpdateEmploymentDataRequest_Provenance = 3
	// When client makes a call during periodic kyc flow, this enum needs to be set
	UpdateEmploymentDataRequest_PROVENANCE_PERIODIC_KYC UpdateEmploymentDataRequest_Provenance = 4
)

// Enum value maps for UpdateEmploymentDataRequest_Provenance.
var (
	UpdateEmploymentDataRequest_Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "PROVENANCE_PROFILE",
		2: "PROVENANCE_VKYC",
		3: "PROVENANCE_INC_OCC_DISCREPANCY_POPUP",
		4: "PROVENANCE_PERIODIC_KYC",
	}
	UpdateEmploymentDataRequest_Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED":               0,
		"PROVENANCE_PROFILE":                   1,
		"PROVENANCE_VKYC":                      2,
		"PROVENANCE_INC_OCC_DISCREPANCY_POPUP": 3,
		"PROVENANCE_PERIODIC_KYC":              4,
	}
)

func (x UpdateEmploymentDataRequest_Provenance) Enum() *UpdateEmploymentDataRequest_Provenance {
	p := new(UpdateEmploymentDataRequest_Provenance)
	*p = x
	return p
}

func (x UpdateEmploymentDataRequest_Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateEmploymentDataRequest_Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[5].Descriptor()
}

func (UpdateEmploymentDataRequest_Provenance) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[5]
}

func (x UpdateEmploymentDataRequest_Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateEmploymentDataRequest_Provenance.Descriptor instead.
func (UpdateEmploymentDataRequest_Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{28, 0}
}

type UpdateEmploymentDataResponse_Status int32

const (
	UpdateEmploymentDataResponse_OK               UpdateEmploymentDataResponse_Status = 0
	UpdateEmploymentDataResponse_INVALID_ARGUMENT UpdateEmploymentDataResponse_Status = 3
	UpdateEmploymentDataResponse_INTERNAL         UpdateEmploymentDataResponse_Status = 13
)

// Enum value maps for UpdateEmploymentDataResponse_Status.
var (
	UpdateEmploymentDataResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	UpdateEmploymentDataResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x UpdateEmploymentDataResponse_Status) Enum() *UpdateEmploymentDataResponse_Status {
	p := new(UpdateEmploymentDataResponse_Status)
	*p = x
	return p
}

func (x UpdateEmploymentDataResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateEmploymentDataResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_screening_service_proto_enumTypes[6].Descriptor()
}

func (UpdateEmploymentDataResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_screening_service_proto_enumTypes[6]
}

func (x UpdateEmploymentDataResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateEmploymentDataResponse_Status.Descriptor instead.
func (UpdateEmploymentDataResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{30, 0}
}

type SendWorkEmailOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// mandatory
	// email that is to be verified
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// Unique identifier of SendWorkEmailOTPRequest request
	// If token is not sent, a new OTP is generated and emailed to user
	// If token is sent, existing OTP is emailed to user again
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// client_req_id is the unique identifier of a work email verification process
	ClientReqId string `protobuf:"bytes,4,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// client is the string form of employment.VerificationProcessClient enum
	Client string `protobuf:"bytes,5,opt,name=client,proto3" json:"client,omitempty"`
}

func (x *SendWorkEmailOTPRequest) Reset() {
	*x = SendWorkEmailOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendWorkEmailOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendWorkEmailOTPRequest) ProtoMessage() {}

func (x *SendWorkEmailOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendWorkEmailOTPRequest.ProtoReflect.Descriptor instead.
func (*SendWorkEmailOTPRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{0}
}

func (x *SendWorkEmailOTPRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SendWorkEmailOTPRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SendWorkEmailOTPRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SendWorkEmailOTPRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *SendWorkEmailOTPRequest) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

type SendWorkEmailOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// Unique identifier of GenerateOtp request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// A timer(in seconds) after which client can send new request
	ResendAfter uint32 `protobuf:"varint,3,opt,name=resend_after,json=resendAfter,proto3" json:"resend_after,omitempty"`
	// otp_expiry(in seconds) denotes expiry time for the OTP
	// A timer for the client post which it should raise a NEW request for Otp with no token i.e. token = ""
	// Any RESEND attempt prior to the expiry should ideally be sent along with token received in first call
	OtpExpiry uint32 `protobuf:"varint,4,opt,name=otp_expiry,json=otpExpiry,proto3" json:"otp_expiry,omitempty"`
}

func (x *SendWorkEmailOTPResponse) Reset() {
	*x = SendWorkEmailOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendWorkEmailOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendWorkEmailOTPResponse) ProtoMessage() {}

func (x *SendWorkEmailOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendWorkEmailOTPResponse.ProtoReflect.Descriptor instead.
func (*SendWorkEmailOTPResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendWorkEmailOTPResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *SendWorkEmailOTPResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *SendWorkEmailOTPResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SendWorkEmailOTPResponse) GetResendAfter() uint32 {
	if x != nil {
		return x.ResendAfter
	}
	return 0
}

func (x *SendWorkEmailOTPResponse) GetOtpExpiry() uint32 {
	if x != nil {
		return x.OtpExpiry
	}
	return 0
}

type VerifyWorkEmailOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req   *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	Email string                `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// Unique identifier of OTP request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// 6-digit OTP that is sent to the email
	Otp string `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	// client_req_id is the unique identifier of a work email verification process
	ClientReqId string `protobuf:"bytes,5,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// client is the string form of employment.VerificationProcessClient enum
	Client string `protobuf:"bytes,6,opt,name=client,proto3" json:"client,omitempty"`
}

func (x *VerifyWorkEmailOTPRequest) Reset() {
	*x = VerifyWorkEmailOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyWorkEmailOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyWorkEmailOTPRequest) ProtoMessage() {}

func (x *VerifyWorkEmailOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyWorkEmailOTPRequest.ProtoReflect.Descriptor instead.
func (*VerifyWorkEmailOTPRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{2}
}

func (x *VerifyWorkEmailOTPRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *VerifyWorkEmailOTPRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *VerifyWorkEmailOTPRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *VerifyWorkEmailOTPRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyWorkEmailOTPRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *VerifyWorkEmailOTPRequest) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

type VerifyWorkEmailOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// text to be shown while transitioning
	TransitionTitleText string `protobuf:"bytes,2,opt,name=transition_title_text,json=transitionTitleText,proto3" json:"transition_title_text,omitempty"`
	// subtitle text to be shown while transitioning
	TransitionSubtitleText string `protobuf:"bytes,3,opt,name=transition_subtitle_text,json=transitionSubtitleText,proto3" json:"transition_subtitle_text,omitempty"`
	// url for display icon
	IconUrl string `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
}

func (x *VerifyWorkEmailOTPResponse) Reset() {
	*x = VerifyWorkEmailOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyWorkEmailOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyWorkEmailOTPResponse) ProtoMessage() {}

func (x *VerifyWorkEmailOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyWorkEmailOTPResponse.ProtoReflect.Descriptor instead.
func (*VerifyWorkEmailOTPResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{3}
}

func (x *VerifyWorkEmailOTPResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *VerifyWorkEmailOTPResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *VerifyWorkEmailOTPResponse) GetTransitionTitleText() string {
	if x != nil {
		return x.TransitionTitleText
	}
	return ""
}

func (x *VerifyWorkEmailOTPResponse) GetTransitionSubtitleText() string {
	if x != nil {
		return x.TransitionSubtitleText
	}
	return ""
}

func (x *VerifyWorkEmailOTPResponse) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

type CheckCreditReportAvailabilityStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *CheckCreditReportAvailabilityStatusRequest) Reset() {
	*x = CheckCreditReportAvailabilityStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreditReportAvailabilityStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreditReportAvailabilityStatusRequest) ProtoMessage() {}

func (x *CheckCreditReportAvailabilityStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreditReportAvailabilityStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckCreditReportAvailabilityStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckCreditReportAvailabilityStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type CheckCreditReportAvailabilityStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// amount of time(in seconds) the UI is expected to wait for, before polling the api again.
	NextPollAfter int32 `protobuf:"varint,2,opt,name=next_poll_after,json=nextPollAfter,proto3" json:"next_poll_after,omitempty"`
}

func (x *CheckCreditReportAvailabilityStatusResponse) Reset() {
	*x = CheckCreditReportAvailabilityStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreditReportAvailabilityStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreditReportAvailabilityStatusResponse) ProtoMessage() {}

func (x *CheckCreditReportAvailabilityStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreditReportAvailabilityStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckCreditReportAvailabilityStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{5}
}

func (x *CheckCreditReportAvailabilityStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CheckCreditReportAvailabilityStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *CheckCreditReportAvailabilityStatusResponse) GetNextPollAfter() int32 {
	if x != nil {
		return x.NextPollAfter
	}
	return 0
}

type GetConsentAndVerifyCreditReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req     *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	Consent bool                  `protobuf:"varint,1,opt,name=consent,proto3" json:"consent,omitempty"`
}

func (x *GetConsentAndVerifyCreditReportRequest) Reset() {
	*x = GetConsentAndVerifyCreditReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConsentAndVerifyCreditReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConsentAndVerifyCreditReportRequest) ProtoMessage() {}

func (x *GetConsentAndVerifyCreditReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConsentAndVerifyCreditReportRequest.ProtoReflect.Descriptor instead.
func (*GetConsentAndVerifyCreditReportRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetConsentAndVerifyCreditReportRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetConsentAndVerifyCreditReportRequest) GetConsent() bool {
	if x != nil {
		return x.Consent
	}
	return false
}

type GetConsentAndVerifyCreditReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetConsentAndVerifyCreditReportResponse) Reset() {
	*x = GetConsentAndVerifyCreditReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConsentAndVerifyCreditReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConsentAndVerifyCreditReportResponse) ProtoMessage() {}

func (x *GetConsentAndVerifyCreditReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConsentAndVerifyCreditReportResponse.ProtoReflect.Descriptor instead.
func (*GetConsentAndVerifyCreditReportResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetConsentAndVerifyCreditReportResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetConsentAndVerifyCreditReportResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type CheckCreditReportVerificationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req                *header.RequestHeader       `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	PollingRequestInfo *typesv2.PollingRequestInfo `protobuf:"bytes,1,opt,name=polling_request_info,json=pollingRequestInfo,proto3" json:"polling_request_info,omitempty"`
}

func (x *CheckCreditReportVerificationStatusRequest) Reset() {
	*x = CheckCreditReportVerificationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreditReportVerificationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreditReportVerificationStatusRequest) ProtoMessage() {}

func (x *CheckCreditReportVerificationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreditReportVerificationStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckCreditReportVerificationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{8}
}

func (x *CheckCreditReportVerificationStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *CheckCreditReportVerificationStatusRequest) GetPollingRequestInfo() *typesv2.PollingRequestInfo {
	if x != nil {
		return x.PollingRequestInfo
	}
	return nil
}

type CheckCreditReportVerificationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// amount of time(in seconds) the UI is expected to wait for, before polling the api again.
	// Deprecated: retry using retry timer from polling response info
	//
	// Deprecated: Marked as deprecated in api/frontend/account/screening/service.proto.
	NextPollAfter int32 `protobuf:"varint,2,opt,name=next_poll_after,json=nextPollAfter,proto3" json:"next_poll_after,omitempty"`
	// CreditReportVerificationStatus denotes status credit report verification
	CreditReportVerificationStatus CreditReportVerificationStatus `protobuf:"varint,3,opt,name=credit_report_verification_status,json=creditReportVerificationStatus,proto3,enum=frontend.account.screening.CreditReportVerificationStatus" json:"credit_report_verification_status,omitempty"`
	// text to be shown while in progress or transitioning
	TransitionTitleText string `protobuf:"bytes,4,opt,name=transition_title_text,json=transitionTitleText,proto3" json:"transition_title_text,omitempty"`
	// subtitle text to be shown while in progress or transitioning
	TransitionSubtitleText string `protobuf:"bytes,5,opt,name=transition_subtitle_text,json=transitionSubtitleText,proto3" json:"transition_subtitle_text,omitempty"`
	// url for display icon
	IconUrl             string                       `protobuf:"bytes,6,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	PollingResponseInfo *typesv2.PollingResponseInfo `protobuf:"bytes,7,opt,name=polling_response_info,json=pollingResponseInfo,proto3" json:"polling_response_info,omitempty"`
}

func (x *CheckCreditReportVerificationStatusResponse) Reset() {
	*x = CheckCreditReportVerificationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreditReportVerificationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreditReportVerificationStatusResponse) ProtoMessage() {}

func (x *CheckCreditReportVerificationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreditReportVerificationStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckCreditReportVerificationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{9}
}

func (x *CheckCreditReportVerificationStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CheckCreditReportVerificationStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/account/screening/service.proto.
func (x *CheckCreditReportVerificationStatusResponse) GetNextPollAfter() int32 {
	if x != nil {
		return x.NextPollAfter
	}
	return 0
}

func (x *CheckCreditReportVerificationStatusResponse) GetCreditReportVerificationStatus() CreditReportVerificationStatus {
	if x != nil {
		return x.CreditReportVerificationStatus
	}
	return CreditReportVerificationStatus_CREDIT_REPORT_VERIFICATION_STATUS_UNSPECIFIED
}

func (x *CheckCreditReportVerificationStatusResponse) GetTransitionTitleText() string {
	if x != nil {
		return x.TransitionTitleText
	}
	return ""
}

func (x *CheckCreditReportVerificationStatusResponse) GetTransitionSubtitleText() string {
	if x != nil {
		return x.TransitionSubtitleText
	}
	return ""
}

func (x *CheckCreditReportVerificationStatusResponse) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *CheckCreditReportVerificationStatusResponse) GetPollingResponseInfo() *typesv2.PollingResponseInfo {
	if x != nil {
		return x.PollingResponseInfo
	}
	return nil
}

type GetCompanyNamesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req        *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	NamePrefix string                `protobuf:"bytes,1,opt,name=name_prefix,json=namePrefix,proto3" json:"name_prefix,omitempty"`
}

func (x *GetCompanyNamesRequest) Reset() {
	*x = GetCompanyNamesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyNamesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyNamesRequest) ProtoMessage() {}

func (x *GetCompanyNamesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyNamesRequest.ProtoReflect.Descriptor instead.
func (*GetCompanyNamesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetCompanyNamesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetCompanyNamesRequest) GetNamePrefix() string {
	if x != nil {
		return x.NamePrefix
	}
	return ""
}

type GetCompanyNamesRequestV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req        *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	NamePrefix string                `protobuf:"bytes,2,opt,name=name_prefix,json=namePrefix,proto3" json:"name_prefix,omitempty"`
}

func (x *GetCompanyNamesRequestV2) Reset() {
	*x = GetCompanyNamesRequestV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyNamesRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyNamesRequestV2) ProtoMessage() {}

func (x *GetCompanyNamesRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyNamesRequestV2.ProtoReflect.Descriptor instead.
func (*GetCompanyNamesRequestV2) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetCompanyNamesRequestV2) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetCompanyNamesRequestV2) GetNamePrefix() string {
	if x != nil {
		return x.NamePrefix
	}
	return ""
}

type CompanyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	VendorId        string `protobuf:"bytes,2,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	IsEpfRegistered bool   `protobuf:"varint,3,opt,name=is_epf_registered,json=isEpfRegistered,proto3" json:"is_epf_registered,omitempty"`
	// text entered by the user in the company name input
	EnteredText string `protobuf:"bytes,4,opt,name=entered_text,json=enteredText,proto3" json:"entered_text,omitempty"`
}

func (x *CompanyInfo) Reset() {
	*x = CompanyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyInfo) ProtoMessage() {}

func (x *CompanyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyInfo.ProtoReflect.Descriptor instead.
func (*CompanyInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{12}
}

func (x *CompanyInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CompanyInfo) GetVendorId() string {
	if x != nil {
		return x.VendorId
	}
	return ""
}

func (x *CompanyInfo) GetIsEpfRegistered() bool {
	if x != nil {
		return x.IsEpfRegistered
	}
	return false
}

func (x *CompanyInfo) GetEnteredText() string {
	if x != nil {
		return x.EnteredText
	}
	return ""
}

type CompanyInfoV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique internal id of the Employer. can be empty if not a verified employer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// legal name, or name by source
	LegalName string `protobuf:"bytes,2,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	// trade name, can be empty
	TradeName string `protobuf:"bytes,3,opt,name=trade_name,json=tradeName,proto3" json:"trade_name,omitempty"`
	// whether employer is verified internally or not
	IsVerifiedEmployer bool `protobuf:"varint,4,opt,name=is_verified_employer,json=isVerifiedEmployer,proto3" json:"is_verified_employer,omitempty"`
}

func (x *CompanyInfoV2) Reset() {
	*x = CompanyInfoV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyInfoV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyInfoV2) ProtoMessage() {}

func (x *CompanyInfoV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyInfoV2.ProtoReflect.Descriptor instead.
func (*CompanyInfoV2) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{13}
}

func (x *CompanyInfoV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CompanyInfoV2) GetLegalName() string {
	if x != nil {
		return x.LegalName
	}
	return ""
}

func (x *CompanyInfoV2) GetTradeName() string {
	if x != nil {
		return x.TradeName
	}
	return ""
}

func (x *CompanyInfoV2) GetIsVerifiedEmployer() bool {
	if x != nil {
		return x.IsVerifiedEmployer
	}
	return false
}

type UserSelectedEmployerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique internal id of the Employer. can be empty if not a verified employer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// text entered by the user in the company name input
	EnteredText string `protobuf:"bytes,2,opt,name=entered_text,json=enteredText,proto3" json:"entered_text,omitempty"`
}

func (x *UserSelectedEmployerInfo) Reset() {
	*x = UserSelectedEmployerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSelectedEmployerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSelectedEmployerInfo) ProtoMessage() {}

func (x *UserSelectedEmployerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSelectedEmployerInfo.ProtoReflect.Descriptor instead.
func (*UserSelectedEmployerInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{14}
}

func (x *UserSelectedEmployerInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserSelectedEmployerInfo) GetEnteredText() string {
	if x != nil {
		return x.EnteredText
	}
	return ""
}

type GetCompanyNamesResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// companies matching user given input
	Companies []*CompanyInfoV2 `protobuf:"bytes,1,rep,name=companies,proto3" json:"companies,omitempty"`
}

func (x *GetCompanyNamesResponseV2) Reset() {
	*x = GetCompanyNamesResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyNamesResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyNamesResponseV2) ProtoMessage() {}

func (x *GetCompanyNamesResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyNamesResponseV2.ProtoReflect.Descriptor instead.
func (*GetCompanyNamesResponseV2) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetCompanyNamesResponseV2) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetCompanyNamesResponseV2) GetCompanies() []*CompanyInfoV2 {
	if x != nil {
		return x.Companies
	}
	return nil
}

type GetCompanyNamesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Companies  []*CompanyInfo         `protobuf:"bytes,1,rep,name=companies,proto3" json:"companies,omitempty"`
}

func (x *GetCompanyNamesResponse) Reset() {
	*x = GetCompanyNamesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyNamesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyNamesResponse) ProtoMessage() {}

func (x *GetCompanyNamesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyNamesResponse.ProtoReflect.Descriptor instead.
func (*GetCompanyNamesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetCompanyNamesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetCompanyNamesResponse) GetCompanies() []*CompanyInfo {
	if x != nil {
		return x.Companies
	}
	return nil
}

type ProcessEmploymentDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// client_req_id is the unique identifier of a process entry for a client
	ClientReqId         string                      `protobuf:"bytes,16,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	EmploymentType      uistate.EmploymentType      `protobuf:"varint,1,opt,name=employment_type,json=employmentType,proto3,enum=frontend.account.screening.EmploymentType" json:"employment_type,omitempty"`
	EmploymentProofType uistate.EmploymentProofType `protobuf:"varint,2,opt,name=employment_proof_type,json=employmentProofType,proto3,enum=frontend.account.screening.EmploymentProofType" json:"employment_proof_type,omitempty"`
	// Types that are assignable to Input:
	//
	//	*ProcessEmploymentDataRequest_EmployerInfo
	//	*ProcessEmploymentDataRequest_CompanyInfo
	//	*ProcessEmploymentDataRequest_PersonalInfo
	//	*ProcessEmploymentDataRequest_BusinessOwnerInfo
	//	*ProcessEmploymentDataRequest_EnrollmentNumber
	//	*ProcessEmploymentDataRequest_StudentDetails
	Input isProcessEmploymentDataRequest_Input `protobuf_oneof:"input"`
	// Deprecated: Marked as deprecated in api/frontend/account/screening/service.proto.
	AnnualSalaryRange *AnnualSalaryRange `protobuf:"bytes,6,opt,name=annual_salary_range,json=annualSalaryRange,proto3" json:"annual_salary_range,omitempty"` // consume annual_salary instead
	AnnualSalary      *AnnualSalary      `protobuf:"bytes,10,opt,name=annual_salary,json=annualSalary,proto3" json:"annual_salary,omitempty"`
	// valid only for cases where income discrepancy exist
	IncomeDiscrepancyConsentGiven bool `protobuf:"varint,11,opt,name=income_discrepancy_consent_given,json=incomeDiscrepancyConsentGiven,proto3" json:"income_discrepancy_consent_given,omitempty"`
	// update_source informs about trigger for RPC.
	// it maps to UpdateSource enum in employment service
	// sample values -> UPDATE_SOURCE_ONBOARDING
	UpdateSource string `protobuf:"bytes,13,opt,name=update_source,json=updateSource,proto3" json:"update_source,omitempty"`
	// maps to employment.OccupationType
	OccupationType string `protobuf:"bytes,14,opt,name=occupation_type,json=occupationType,proto3" json:"occupation_type,omitempty"`
	// maps to api.typesv2.Qualification
	Qualification string `protobuf:"bytes,17,opt,name=qualification,proto3" json:"qualification,omitempty"`
	// maps to api.typesv2.SourceOfFunds
	SourceOfFunds string `protobuf:"bytes,18,opt,name=source_of_funds,json=sourceOfFunds,proto3" json:"source_of_funds,omitempty"`
	// value selected by the user for EmploymentDeclarationOptions
	AnnualTransactionVolume *AnnualSalaryRange `protobuf:"bytes,19,opt,name=annual_transaction_volume,json=annualTransactionVolume,proto3" json:"annual_transaction_volume,omitempty"`
}

func (x *ProcessEmploymentDataRequest) Reset() {
	*x = ProcessEmploymentDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEmploymentDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEmploymentDataRequest) ProtoMessage() {}

func (x *ProcessEmploymentDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEmploymentDataRequest.ProtoReflect.Descriptor instead.
func (*ProcessEmploymentDataRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{17}
}

func (x *ProcessEmploymentDataRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *ProcessEmploymentDataRequest) GetEmploymentType() uistate.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return uistate.EmploymentType(0)
}

func (x *ProcessEmploymentDataRequest) GetEmploymentProofType() uistate.EmploymentProofType {
	if x != nil {
		return x.EmploymentProofType
	}
	return uistate.EmploymentProofType(0)
}

func (m *ProcessEmploymentDataRequest) GetInput() isProcessEmploymentDataRequest_Input {
	if m != nil {
		return m.Input
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetEmployerInfo() *UserSelectedEmployerInfo {
	if x, ok := x.GetInput().(*ProcessEmploymentDataRequest_EmployerInfo); ok {
		return x.EmployerInfo
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetCompanyInfo() *CompanyInfo {
	if x, ok := x.GetInput().(*ProcessEmploymentDataRequest_CompanyInfo); ok {
		return x.CompanyInfo
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetPersonalInfo() *PersonalProfileInfo {
	if x, ok := x.GetInput().(*ProcessEmploymentDataRequest_PersonalInfo); ok {
		return x.PersonalInfo
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetBusinessOwnerInfo() *BusinessOwnerInfo {
	if x, ok := x.GetInput().(*ProcessEmploymentDataRequest_BusinessOwnerInfo); ok {
		return x.BusinessOwnerInfo
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetEnrollmentNumber() *EnrollmentNumber {
	if x, ok := x.GetInput().(*ProcessEmploymentDataRequest_EnrollmentNumber); ok {
		return x.EnrollmentNumber
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetStudentDetails() *StudentDetails {
	if x, ok := x.GetInput().(*ProcessEmploymentDataRequest_StudentDetails); ok {
		return x.StudentDetails
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/account/screening/service.proto.
func (x *ProcessEmploymentDataRequest) GetAnnualSalaryRange() *AnnualSalaryRange {
	if x != nil {
		return x.AnnualSalaryRange
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetAnnualSalary() *AnnualSalary {
	if x != nil {
		return x.AnnualSalary
	}
	return nil
}

func (x *ProcessEmploymentDataRequest) GetIncomeDiscrepancyConsentGiven() bool {
	if x != nil {
		return x.IncomeDiscrepancyConsentGiven
	}
	return false
}

func (x *ProcessEmploymentDataRequest) GetUpdateSource() string {
	if x != nil {
		return x.UpdateSource
	}
	return ""
}

func (x *ProcessEmploymentDataRequest) GetOccupationType() string {
	if x != nil {
		return x.OccupationType
	}
	return ""
}

func (x *ProcessEmploymentDataRequest) GetQualification() string {
	if x != nil {
		return x.Qualification
	}
	return ""
}

func (x *ProcessEmploymentDataRequest) GetSourceOfFunds() string {
	if x != nil {
		return x.SourceOfFunds
	}
	return ""
}

func (x *ProcessEmploymentDataRequest) GetAnnualTransactionVolume() *AnnualSalaryRange {
	if x != nil {
		return x.AnnualTransactionVolume
	}
	return nil
}

type isProcessEmploymentDataRequest_Input interface {
	isProcessEmploymentDataRequest_Input()
}

type ProcessEmploymentDataRequest_EmployerInfo struct {
	// company info v2 update
	EmployerInfo *UserSelectedEmployerInfo `protobuf:"bytes,12,opt,name=employer_info,json=employerInfo,proto3,oneof"`
}

type ProcessEmploymentDataRequest_CompanyInfo struct {
	// expected when employment_proof_type is `COMPANY_NAME“
	CompanyInfo *CompanyInfo `protobuf:"bytes,3,opt,name=company_info,json=companyInfo,proto3,oneof"`
}

type ProcessEmploymentDataRequest_PersonalInfo struct {
	// expected when employment_proof_type is `PERSONAL_WEBSITE`
	PersonalInfo *PersonalProfileInfo `protobuf:"bytes,5,opt,name=personal_info,json=personalInfo,proto3,oneof"`
}

type ProcessEmploymentDataRequest_BusinessOwnerInfo struct {
	// details of business owner includes GSTIN number for now.
	// Expected when employment_proof_type is `BUSINESS_OWNER_DETAILS`
	BusinessOwnerInfo *BusinessOwnerInfo `protobuf:"bytes,7,opt,name=business_owner_info,json=businessOwnerInfo,proto3,oneof"`
}

type ProcessEmploymentDataRequest_EnrollmentNumber struct {
	// Expected when employment_proof_type is `ENROLLMENT_NUMBER`
	EnrollmentNumber *EnrollmentNumber `protobuf:"bytes,8,opt,name=enrollment_number,json=enrollmentNumber,proto3,oneof"`
}

type ProcessEmploymentDataRequest_StudentDetails struct {
	// Expected when employment_proof_type is `STUDENT_DETAILS`
	StudentDetails *StudentDetails `protobuf:"bytes,9,opt,name=student_details,json=studentDetails,proto3,oneof"`
}

func (*ProcessEmploymentDataRequest_EmployerInfo) isProcessEmploymentDataRequest_Input() {}

func (*ProcessEmploymentDataRequest_CompanyInfo) isProcessEmploymentDataRequest_Input() {}

func (*ProcessEmploymentDataRequest_PersonalInfo) isProcessEmploymentDataRequest_Input() {}

func (*ProcessEmploymentDataRequest_BusinessOwnerInfo) isProcessEmploymentDataRequest_Input() {}

func (*ProcessEmploymentDataRequest_EnrollmentNumber) isProcessEmploymentDataRequest_Input() {}

func (*ProcessEmploymentDataRequest_StudentDetails) isProcessEmploymentDataRequest_Input() {}

type ProcessEmploymentDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *ProcessEmploymentDataResponse) Reset() {
	*x = ProcessEmploymentDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEmploymentDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEmploymentDataResponse) ProtoMessage() {}

func (x *ProcessEmploymentDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEmploymentDataResponse.ProtoReflect.Descriptor instead.
func (*ProcessEmploymentDataResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{18}
}

func (x *ProcessEmploymentDataResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *ProcessEmploymentDataResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type CheckEmploymentVerificationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *CheckEmploymentVerificationStatusRequest) Reset() {
	*x = CheckEmploymentVerificationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEmploymentVerificationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEmploymentVerificationStatusRequest) ProtoMessage() {}

func (x *CheckEmploymentVerificationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEmploymentVerificationStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckEmploymentVerificationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{19}
}

func (x *CheckEmploymentVerificationStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type CheckEmploymentVerificationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// amount of time(in seconds) the UI is expected to wait for, before polling the api again.
	NextPollAfter int32 `protobuf:"varint,2,opt,name=next_poll_after,json=nextPollAfter,proto3" json:"next_poll_after,omitempty"`
	// text to be shown while in progress or transitioning
	TransitionTitleText string `protobuf:"bytes,3,opt,name=transition_title_text,json=transitionTitleText,proto3" json:"transition_title_text,omitempty"`
	// subtitle text to be shown while in progress or transitioning
	TransitionSubtitleText string `protobuf:"bytes,4,opt,name=transition_subtitle_text,json=transitionSubtitleText,proto3" json:"transition_subtitle_text,omitempty"`
	// url for display icon
	IconUrl string `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
}

func (x *CheckEmploymentVerificationStatusResponse) Reset() {
	*x = CheckEmploymentVerificationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEmploymentVerificationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEmploymentVerificationStatusResponse) ProtoMessage() {}

func (x *CheckEmploymentVerificationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEmploymentVerificationStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckEmploymentVerificationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{20}
}

func (x *CheckEmploymentVerificationStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CheckEmploymentVerificationStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *CheckEmploymentVerificationStatusResponse) GetNextPollAfter() int32 {
	if x != nil {
		return x.NextPollAfter
	}
	return 0
}

func (x *CheckEmploymentVerificationStatusResponse) GetTransitionTitleText() string {
	if x != nil {
		return x.TransitionTitleText
	}
	return ""
}

func (x *CheckEmploymentVerificationStatusResponse) GetTransitionSubtitleText() string {
	if x != nil {
		return x.TransitionSubtitleText
	}
	return ""
}

func (x *CheckEmploymentVerificationStatusResponse) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

// It could be linkedIn profile, any personal website, github profile etc
type PersonalProfileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *PersonalProfileInfo) Reset() {
	*x = PersonalProfileInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalProfileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalProfileInfo) ProtoMessage() {}

func (x *PersonalProfileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalProfileInfo.ProtoReflect.Descriptor instead.
func (*PersonalProfileInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{21}
}

func (x *PersonalProfileInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CheckGmailVerificationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *CheckGmailVerificationStatusRequest) Reset() {
	*x = CheckGmailVerificationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckGmailVerificationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGmailVerificationStatusRequest) ProtoMessage() {}

func (x *CheckGmailVerificationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGmailVerificationStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckGmailVerificationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{22}
}

func (x *CheckGmailVerificationStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type CheckGmailVerificationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deeplink to take the user to in cases of success failure etc
	NextAction *deeplink.Deeplink `protobuf:"bytes,1,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// amount of time(in seconds) the UI is expected to wait for, before polling the api again.
	NextPollAfter int32 `protobuf:"varint,2,opt,name=next_poll_after,json=nextPollAfter,proto3" json:"next_poll_after,omitempty"`
	// text to be shown while in progress or transitioning
	TransitionTitleText string `protobuf:"bytes,3,opt,name=transition_title_text,json=transitionTitleText,proto3" json:"transition_title_text,omitempty"`
	// subtitle text to be shown while in progress or transitioning
	TransitionSubtitleText string `protobuf:"bytes,4,opt,name=transition_subtitle_text,json=transitionSubtitleText,proto3" json:"transition_subtitle_text,omitempty"`
	// response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,5,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *CheckGmailVerificationStatusResponse) Reset() {
	*x = CheckGmailVerificationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckGmailVerificationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGmailVerificationStatusResponse) ProtoMessage() {}

func (x *CheckGmailVerificationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGmailVerificationStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckGmailVerificationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{23}
}

func (x *CheckGmailVerificationStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *CheckGmailVerificationStatusResponse) GetNextPollAfter() int32 {
	if x != nil {
		return x.NextPollAfter
	}
	return 0
}

func (x *CheckGmailVerificationStatusResponse) GetTransitionTitleText() string {
	if x != nil {
		return x.TransitionTitleText
	}
	return ""
}

func (x *CheckGmailVerificationStatusResponse) GetTransitionSubtitleText() string {
	if x != nil {
		return x.TransitionSubtitleText
	}
	return ""
}

func (x *CheckGmailVerificationStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type AnnualSalaryRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinValue int32 `protobuf:"varint,1,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`
	MaxValue int32 `protobuf:"varint,2,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`
	// if currency_code is not mentioned (unspecified), it should be considered as INR
	// Supported: INR, AED
	// Note: in case more enums are added to currency code, a client release is required to support this.
	CurrencyCode       typesv2.CurrencyCode                  `protobuf:"varint,3,opt,name=currency_code,json=currencyCode,proto3,enum=api.typesv2.CurrencyCode" json:"currency_code,omitempty"`
	AlternateDisplayed *AnnualSalaryRange_AlternateDisplayed `protobuf:"bytes,4,opt,name=alternate_displayed,json=alternateDisplayed,proto3" json:"alternate_displayed,omitempty"`
}

func (x *AnnualSalaryRange) Reset() {
	*x = AnnualSalaryRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualSalaryRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualSalaryRange) ProtoMessage() {}

func (x *AnnualSalaryRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualSalaryRange.ProtoReflect.Descriptor instead.
func (*AnnualSalaryRange) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{24}
}

func (x *AnnualSalaryRange) GetMinValue() int32 {
	if x != nil {
		return x.MinValue
	}
	return 0
}

func (x *AnnualSalaryRange) GetMaxValue() int32 {
	if x != nil {
		return x.MaxValue
	}
	return 0
}

func (x *AnnualSalaryRange) GetCurrencyCode() typesv2.CurrencyCode {
	if x != nil {
		return x.CurrencyCode
	}
	return typesv2.CurrencyCode(0)
}

func (x *AnnualSalaryRange) GetAlternateDisplayed() *AnnualSalaryRange_AlternateDisplayed {
	if x != nil {
		return x.AlternateDisplayed
	}
	return nil
}

type BusinessOwnerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GstinNo string `protobuf:"bytes,1,opt,name=gstin_no,json=gstinNo,proto3" json:"gstin_no,omitempty"`
}

func (x *BusinessOwnerInfo) Reset() {
	*x = BusinessOwnerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOwnerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOwnerInfo) ProtoMessage() {}

func (x *BusinessOwnerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOwnerInfo.ProtoReflect.Descriptor instead.
func (*BusinessOwnerInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{25}
}

func (x *BusinessOwnerInfo) GetGstinNo() string {
	if x != nil {
		return x.GstinNo
	}
	return ""
}

type EnrollmentNumber struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnrollmentNo string `protobuf:"bytes,1,opt,name=enrollment_no,json=enrollmentNo,proto3" json:"enrollment_no,omitempty"`
}

func (x *EnrollmentNumber) Reset() {
	*x = EnrollmentNumber{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollmentNumber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollmentNumber) ProtoMessage() {}

func (x *EnrollmentNumber) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollmentNumber.ProtoReflect.Descriptor instead.
func (*EnrollmentNumber) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{26}
}

func (x *EnrollmentNumber) GetEnrollmentNo() string {
	if x != nil {
		return x.EnrollmentNo
	}
	return ""
}

type StudentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	// student mail id
	MailId string `protobuf:"bytes,2,opt,name=mail_id,json=mailId,proto3" json:"mail_id,omitempty"`
}

func (x *StudentDetails) Reset() {
	*x = StudentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StudentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentDetails) ProtoMessage() {}

func (x *StudentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentDetails.ProtoReflect.Descriptor instead.
func (*StudentDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{27}
}

func (x *StudentDetails) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *StudentDetails) GetMailId() string {
	if x != nil {
		return x.MailId
	}
	return ""
}

type UpdateEmploymentDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req                 *header.RequestHeader       `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	EmploymentType      uistate.EmploymentType      `protobuf:"varint,2,opt,name=employment_type,json=employmentType,proto3,enum=frontend.account.screening.EmploymentType" json:"employment_type,omitempty"`
	EmploymentProofType uistate.EmploymentProofType `protobuf:"varint,3,opt,name=employment_proof_type,json=employmentProofType,proto3,enum=frontend.account.screening.EmploymentProofType" json:"employment_proof_type,omitempty"`
	// Types that are assignable to Input:
	//
	//	*UpdateEmploymentDataRequest_CompanyInfo
	//	*UpdateEmploymentDataRequest_EmployerInfo
	//	*UpdateEmploymentDataRequest_PersonalInfo
	//	*UpdateEmploymentDataRequest_BusinessOwnerInfo
	//	*UpdateEmploymentDataRequest_EnrollmentNumber
	//	*UpdateEmploymentDataRequest_StudentDetails
	Input             isUpdateEmploymentDataRequest_Input    `protobuf_oneof:"input"`
	AnnualSalaryRange *AnnualSalaryRange                     `protobuf:"bytes,9,opt,name=annual_salary_range,json=annualSalaryRange,proto3" json:"annual_salary_range,omitempty"`
	AnnualSalary      *AnnualSalary                          `protobuf:"bytes,10,opt,name=annual_salary,json=annualSalary,proto3" json:"annual_salary,omitempty"`
	Provenance        UpdateEmploymentDataRequest_Provenance `protobuf:"varint,12,opt,name=provenance,proto3,enum=frontend.account.screening.UpdateEmploymentDataRequest_Provenance" json:"provenance,omitempty"`
	// maps to employment.OccupationType
	OccupationType string `protobuf:"bytes,13,opt,name=occupation_type,json=occupationType,proto3" json:"occupation_type,omitempty"`
}

func (x *UpdateEmploymentDataRequest) Reset() {
	*x = UpdateEmploymentDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEmploymentDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEmploymentDataRequest) ProtoMessage() {}

func (x *UpdateEmploymentDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEmploymentDataRequest.ProtoReflect.Descriptor instead.
func (*UpdateEmploymentDataRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateEmploymentDataRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetEmploymentType() uistate.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return uistate.EmploymentType(0)
}

func (x *UpdateEmploymentDataRequest) GetEmploymentProofType() uistate.EmploymentProofType {
	if x != nil {
		return x.EmploymentProofType
	}
	return uistate.EmploymentProofType(0)
}

func (m *UpdateEmploymentDataRequest) GetInput() isUpdateEmploymentDataRequest_Input {
	if m != nil {
		return m.Input
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetCompanyInfo() *CompanyInfo {
	if x, ok := x.GetInput().(*UpdateEmploymentDataRequest_CompanyInfo); ok {
		return x.CompanyInfo
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetEmployerInfo() *UserSelectedEmployerInfo {
	if x, ok := x.GetInput().(*UpdateEmploymentDataRequest_EmployerInfo); ok {
		return x.EmployerInfo
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetPersonalInfo() *PersonalProfileInfo {
	if x, ok := x.GetInput().(*UpdateEmploymentDataRequest_PersonalInfo); ok {
		return x.PersonalInfo
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetBusinessOwnerInfo() *BusinessOwnerInfo {
	if x, ok := x.GetInput().(*UpdateEmploymentDataRequest_BusinessOwnerInfo); ok {
		return x.BusinessOwnerInfo
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetEnrollmentNumber() *EnrollmentNumber {
	if x, ok := x.GetInput().(*UpdateEmploymentDataRequest_EnrollmentNumber); ok {
		return x.EnrollmentNumber
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetStudentDetails() *StudentDetails {
	if x, ok := x.GetInput().(*UpdateEmploymentDataRequest_StudentDetails); ok {
		return x.StudentDetails
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetAnnualSalaryRange() *AnnualSalaryRange {
	if x != nil {
		return x.AnnualSalaryRange
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetAnnualSalary() *AnnualSalary {
	if x != nil {
		return x.AnnualSalary
	}
	return nil
}

func (x *UpdateEmploymentDataRequest) GetProvenance() UpdateEmploymentDataRequest_Provenance {
	if x != nil {
		return x.Provenance
	}
	return UpdateEmploymentDataRequest_PROVENANCE_UNSPECIFIED
}

func (x *UpdateEmploymentDataRequest) GetOccupationType() string {
	if x != nil {
		return x.OccupationType
	}
	return ""
}

type isUpdateEmploymentDataRequest_Input interface {
	isUpdateEmploymentDataRequest_Input()
}

type UpdateEmploymentDataRequest_CompanyInfo struct {
	// expected when employment_proof_type is `COMPANY_NAME`
	CompanyInfo *CompanyInfo `protobuf:"bytes,4,opt,name=company_info,json=companyInfo,proto3,oneof"`
}

type UpdateEmploymentDataRequest_EmployerInfo struct {
	// company info v2 update
	EmployerInfo *UserSelectedEmployerInfo `protobuf:"bytes,11,opt,name=employer_info,json=employerInfo,proto3,oneof"`
}

type UpdateEmploymentDataRequest_PersonalInfo struct {
	// expected when employment_proof_type is `PERSONAL_WEBSITE`
	PersonalInfo *PersonalProfileInfo `protobuf:"bytes,5,opt,name=personal_info,json=personalInfo,proto3,oneof"`
}

type UpdateEmploymentDataRequest_BusinessOwnerInfo struct {
	// details of business owner includes GSTIN number for now.
	// Expected when employment_proof_type is `BUSINESS_OWNER_DETAILS`
	BusinessOwnerInfo *BusinessOwnerInfo `protobuf:"bytes,6,opt,name=business_owner_info,json=businessOwnerInfo,proto3,oneof"`
}

type UpdateEmploymentDataRequest_EnrollmentNumber struct {
	// Expected when employment_proof_type is `ENROLLMENT_NUMBER`
	EnrollmentNumber *EnrollmentNumber `protobuf:"bytes,7,opt,name=enrollment_number,json=enrollmentNumber,proto3,oneof"`
}

type UpdateEmploymentDataRequest_StudentDetails struct {
	// Expected when employment_proof_type is `STUDENT_DETAILS`
	StudentDetails *StudentDetails `protobuf:"bytes,8,opt,name=student_details,json=studentDetails,proto3,oneof"`
}

func (*UpdateEmploymentDataRequest_CompanyInfo) isUpdateEmploymentDataRequest_Input() {}

func (*UpdateEmploymentDataRequest_EmployerInfo) isUpdateEmploymentDataRequest_Input() {}

func (*UpdateEmploymentDataRequest_PersonalInfo) isUpdateEmploymentDataRequest_Input() {}

func (*UpdateEmploymentDataRequest_BusinessOwnerInfo) isUpdateEmploymentDataRequest_Input() {}

func (*UpdateEmploymentDataRequest_EnrollmentNumber) isUpdateEmploymentDataRequest_Input() {}

func (*UpdateEmploymentDataRequest_StudentDetails) isUpdateEmploymentDataRequest_Input() {}

type AnnualSalary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Range *AnnualSalaryRange `protobuf:"bytes,1,opt,name=range,proto3" json:"range,omitempty"`
	// absolute salary
	//
	// Deprecated: Marked as deprecated in api/frontend/account/screening/service.proto.
	Absolute float32 `protobuf:"fixed32,2,opt,name=absolute,proto3" json:"absolute,omitempty"` // currency is always INR in case of absolute annual salary.
}

func (x *AnnualSalary) Reset() {
	*x = AnnualSalary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualSalary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualSalary) ProtoMessage() {}

func (x *AnnualSalary) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualSalary.ProtoReflect.Descriptor instead.
func (*AnnualSalary) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{29}
}

func (x *AnnualSalary) GetRange() *AnnualSalaryRange {
	if x != nil {
		return x.Range
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/account/screening/service.proto.
func (x *AnnualSalary) GetAbsolute() float32 {
	if x != nil {
		return x.Absolute
	}
	return 0
}

type UpdateEmploymentDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *UpdateEmploymentDataResponse) Reset() {
	*x = UpdateEmploymentDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEmploymentDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEmploymentDataResponse) ProtoMessage() {}

func (x *UpdateEmploymentDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEmploymentDataResponse.ProtoReflect.Descriptor instead.
func (*UpdateEmploymentDataResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateEmploymentDataResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *UpdateEmploymentDataResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetScreenerChoiceScreenOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetScreenerChoiceScreenOptionsRequest) Reset() {
	*x = GetScreenerChoiceScreenOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerChoiceScreenOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerChoiceScreenOptionsRequest) ProtoMessage() {}

func (x *GetScreenerChoiceScreenOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerChoiceScreenOptionsRequest.ProtoReflect.Descriptor instead.
func (*GetScreenerChoiceScreenOptionsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetScreenerChoiceScreenOptionsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetScreenerChoiceScreenOptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Deeplink   *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *GetScreenerChoiceScreenOptionsResponse) Reset() {
	*x = GetScreenerChoiceScreenOptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScreenerChoiceScreenOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScreenerChoiceScreenOptionsResponse) ProtoMessage() {}

func (x *GetScreenerChoiceScreenOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScreenerChoiceScreenOptionsResponse.ProtoReflect.Descriptor instead.
func (*GetScreenerChoiceScreenOptionsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetScreenerChoiceScreenOptionsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetScreenerChoiceScreenOptionsResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// alternate annual salary value that was displayed to the user in some other currency at the time of data declaration.
type AnnualSalaryRange_AlternateDisplayed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// min value of salary range
	MinVal int32 `protobuf:"varint,1,opt,name=min_val,json=minVal,proto3" json:"min_val,omitempty"`
	// max value of salary range
	MaxVal int32 `protobuf:"varint,2,opt,name=max_val,json=maxVal,proto3" json:"max_val,omitempty"`
	// currency_code of the annual salary
	// Supported: INR, AED.
	// Note: empty currency code can not be passed here.
	// Note 2: in case more enums are added to currency code, a client release is required to support this.
	CurrencyCode typesv2.CurrencyCode `protobuf:"varint,3,opt,name=currency_code,json=currencyCode,proto3,enum=api.typesv2.CurrencyCode" json:"currency_code,omitempty"`
}

func (x *AnnualSalaryRange_AlternateDisplayed) Reset() {
	*x = AnnualSalaryRange_AlternateDisplayed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_screening_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualSalaryRange_AlternateDisplayed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualSalaryRange_AlternateDisplayed) ProtoMessage() {}

func (x *AnnualSalaryRange_AlternateDisplayed) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_screening_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualSalaryRange_AlternateDisplayed.ProtoReflect.Descriptor instead.
func (*AnnualSalaryRange_AlternateDisplayed) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_screening_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *AnnualSalaryRange_AlternateDisplayed) GetMinVal() int32 {
	if x != nil {
		return x.MinVal
	}
	return 0
}

func (x *AnnualSalaryRange_AlternateDisplayed) GetMaxVal() int32 {
	if x != nil {
		return x.MaxVal
	}
	return 0
}

func (x *AnnualSalaryRange_AlternateDisplayed) GetCurrencyCode() typesv2.CurrencyCode {
	if x != nil {
		return x.CurrencyCode
	}
	return typesv2.CurrencyCode(0)
}

var File_api_frontend_account_screening_service_proto protoreflect.FileDescriptor

var file_api_frontend_account_screening_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x69, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x02, 0x0a,
	0x17, 0x53, 0x65, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x54,
	0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x7b, 0x0a, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x65, 0xfa, 0x42, 0x62, 0x72, 0x60, 0x32,
	0x5e, 0x28, 0x5e, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x38,
	0x7d, 0x2d, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d,
	0x2d, 0x34, 0x5b, 0x61, 0x2d, 0x66, 0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x7d,
	0x2d, 0x5b, 0x38, 0x7c, 0x39, 0x7c, 0x61, 0x41, 0x7c, 0x62, 0x42, 0x5d, 0x5b, 0x61, 0x2d, 0x66,
	0x41, 0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x7d, 0x2d, 0x5b, 0x61, 0x2d, 0x66, 0x41,
	0x2d, 0x46, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x31, 0x32, 0x7d, 0x24, 0x7c, 0x5e, 0x24, 0x29, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x22, 0xf2, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x74, 0x70, 0x5f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6f, 0x74,
	0x70, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x22, 0xe5, 0x01, 0x0a, 0x19, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x54, 0x50, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0xb0, 0x01, 0x01, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a,
	0x03, 0x6f, 0x74, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72,
	0x0d, 0x32, 0x08, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x98, 0x01, 0x06, 0x52, 0x03,
	0x6f, 0x74, 0x70, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x22,
	0xa5, 0x02, 0x0a, 0x1a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32,
	0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65,
	0x78, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x22, 0x5e, 0x0a, 0x2a, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xd5, 0x01, 0x0a, 0x2b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x41, 0x66, 0x74, 0x65, 0x72, 0x22,
	0x74, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x22, 0xa9, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xb1, 0x01, 0x0a, 0x2a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x12, 0x51, 0x0a, 0x14, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50,
	0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x12, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xc0, 0x04, 0x0a, 0x2b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x6f,
	0x6c, 0x6c, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x12, 0x85, 0x01, 0x0a, 0x21, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a,
	0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x12, 0x54, 0x0a, 0x15, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x13, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x50,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x22, 0x76, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x32, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x03, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x22, 0x8d, 0x01,
	0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x70, 0x66, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x45, 0x70, 0x66,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x22, 0x8f, 0x01,
	0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x14, 0x69, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x22,
	0x4d, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x22, 0xc6,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x47,
	0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xc2, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x22, 0x1e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xd1, 0x0a, 0x0a,
	0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a,
	0x0d, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x56, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x5f, 0x0a, 0x13, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x5b, 0x0a, 0x11, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x10, 0x65, 0x6e,
	0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x55,
	0x0a, 0x0f, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x61, 0x0a, 0x13, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x0d, 0x61, 0x6e, 0x6e, 0x75,
	0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x6e, 0x6e,
	0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x75, 0x61,
	0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x12, 0x47, 0x0a, 0x20, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x72, 0x65, 0x70, 0x61, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x1d, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x65, 0x70,
	0x61, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x47, 0x69, 0x76, 0x65, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x66, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x69, 0x0a, 0x19,
	0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x6e, 0x6e,
	0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x17,
	0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x22, 0xd5, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x5c, 0x0a, 0x28, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x8b, 0x03, 0x0a, 0x29, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x6f, 0x6c,
	0x6c, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x38, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75,
	0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63,
	0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x22, 0x27, 0x0a, 0x13, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x57, 0x0a,
	0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xbc, 0x02, 0x0a, 0x24, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x47, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x6f, 0x6c, 0x6c,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x89, 0x03, 0x0a, 0x11, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c,
	0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x71, 0x0a, 0x13, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x64, 0x52, 0x12, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x1a, 0x86, 0x01, 0x0a, 0x12, 0x41, 0x6c, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f,
	0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x56, 0x61,
	0x6c, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x2e, 0x0a, 0x11, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x73, 0x74, 0x69, 0x6e, 0x5f,
	0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x73, 0x74, 0x69, 0x6e, 0x4e,
	0x6f, 0x22, 0x37, 0x0a, 0x10, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e,
	0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x22, 0x3d, 0x0a, 0x0e, 0x53, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x84, 0x0a, 0x0a, 0x1b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x53, 0x0a, 0x0f, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x63, 0x0a, 0x15, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x72, 0x6f, 0x6f, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x13, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f,
	0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x0d, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0c, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x56, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5f, 0x0a, 0x13, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x11, 0x65, 0x6e, 0x72,
	0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x10, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x0f, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5d, 0x0a,
	0x13, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x11, 0x61, 0x6e, 0x6e, 0x75, 0x61,
	0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x0d,
	0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x0c, 0x61,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x12, 0x62, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x6f,
	0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45,
	0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x02,
	0x12, 0x28, 0x0a, 0x24, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x49,
	0x4e, 0x43, 0x5f, 0x4f, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x52, 0x45, 0x50, 0x41, 0x4e,
	0x43, 0x59, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52,
	0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x49,
	0x43, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x04, 0x42, 0x07, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x22, 0x73, 0x0a, 0x0c, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x12, 0x43, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x6e, 0x6e,
	0x75, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x05,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x61, 0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x61, 0x62, 0x73,
	0x6f, 0x6c, 0x75, 0x74, 0x65, 0x22, 0xd4, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x59, 0x0a, 0x25,
	0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x6f, 0x69, 0x63,
	0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xa3, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2a, 0xe6, 0x01,
	0x0a, 0x1e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x31, 0x0a, 0x2d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x32, 0x98, 0x10, 0x0a, 0x09, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0xc7, 0x01, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xbb,
	0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x6e,
	0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7,
	0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xc7, 0x01, 0x0a,
	0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9d, 0x01, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0xc1, 0x01, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xb2, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47,
	0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x6d, 0x61, 0x69, 0x6c, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x53,
	0x65, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x54, 0x50, 0x12,
	0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f,
	0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x94, 0x01, 0x0a, 0x12,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f,
	0x54, 0x50, 0x12, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f,
	0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x57, 0x6f, 0x72,
	0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x91, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x56, 0x32, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x1a, 0x35,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x32, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xb8, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_account_screening_service_proto_rawDescOnce sync.Once
	file_api_frontend_account_screening_service_proto_rawDescData = file_api_frontend_account_screening_service_proto_rawDesc
)

func file_api_frontend_account_screening_service_proto_rawDescGZIP() []byte {
	file_api_frontend_account_screening_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_account_screening_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_account_screening_service_proto_rawDescData)
	})
	return file_api_frontend_account_screening_service_proto_rawDescData
}

var file_api_frontend_account_screening_service_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_frontend_account_screening_service_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_api_frontend_account_screening_service_proto_goTypes = []interface{}{
	(CreditReportVerificationStatus)(0),                   // 0: frontend.account.screening.CreditReportVerificationStatus
	(GetCompanyNamesResponseV2_Status)(0),                 // 1: frontend.account.screening.GetCompanyNamesResponseV2.Status
	(GetCompanyNamesResponse_Status)(0),                   // 2: frontend.account.screening.GetCompanyNamesResponse.Status
	(ProcessEmploymentDataResponse_Status)(0),             // 3: frontend.account.screening.ProcessEmploymentDataResponse.Status
	(CheckEmploymentVerificationStatusResponse_Status)(0), // 4: frontend.account.screening.CheckEmploymentVerificationStatusResponse.Status
	(UpdateEmploymentDataRequest_Provenance)(0),           // 5: frontend.account.screening.UpdateEmploymentDataRequest.Provenance
	(UpdateEmploymentDataResponse_Status)(0),              // 6: frontend.account.screening.UpdateEmploymentDataResponse.Status
	(*SendWorkEmailOTPRequest)(nil),                       // 7: frontend.account.screening.SendWorkEmailOTPRequest
	(*SendWorkEmailOTPResponse)(nil),                      // 8: frontend.account.screening.SendWorkEmailOTPResponse
	(*VerifyWorkEmailOTPRequest)(nil),                     // 9: frontend.account.screening.VerifyWorkEmailOTPRequest
	(*VerifyWorkEmailOTPResponse)(nil),                    // 10: frontend.account.screening.VerifyWorkEmailOTPResponse
	(*CheckCreditReportAvailabilityStatusRequest)(nil),    // 11: frontend.account.screening.CheckCreditReportAvailabilityStatusRequest
	(*CheckCreditReportAvailabilityStatusResponse)(nil),   // 12: frontend.account.screening.CheckCreditReportAvailabilityStatusResponse
	(*GetConsentAndVerifyCreditReportRequest)(nil),        // 13: frontend.account.screening.GetConsentAndVerifyCreditReportRequest
	(*GetConsentAndVerifyCreditReportResponse)(nil),       // 14: frontend.account.screening.GetConsentAndVerifyCreditReportResponse
	(*CheckCreditReportVerificationStatusRequest)(nil),    // 15: frontend.account.screening.CheckCreditReportVerificationStatusRequest
	(*CheckCreditReportVerificationStatusResponse)(nil),   // 16: frontend.account.screening.CheckCreditReportVerificationStatusResponse
	(*GetCompanyNamesRequest)(nil),                        // 17: frontend.account.screening.GetCompanyNamesRequest
	(*GetCompanyNamesRequestV2)(nil),                      // 18: frontend.account.screening.GetCompanyNamesRequestV2
	(*CompanyInfo)(nil),                                   // 19: frontend.account.screening.CompanyInfo
	(*CompanyInfoV2)(nil),                                 // 20: frontend.account.screening.CompanyInfoV2
	(*UserSelectedEmployerInfo)(nil),                      // 21: frontend.account.screening.UserSelectedEmployerInfo
	(*GetCompanyNamesResponseV2)(nil),                     // 22: frontend.account.screening.GetCompanyNamesResponseV2
	(*GetCompanyNamesResponse)(nil),                       // 23: frontend.account.screening.GetCompanyNamesResponse
	(*ProcessEmploymentDataRequest)(nil),                  // 24: frontend.account.screening.ProcessEmploymentDataRequest
	(*ProcessEmploymentDataResponse)(nil),                 // 25: frontend.account.screening.ProcessEmploymentDataResponse
	(*CheckEmploymentVerificationStatusRequest)(nil),      // 26: frontend.account.screening.CheckEmploymentVerificationStatusRequest
	(*CheckEmploymentVerificationStatusResponse)(nil),     // 27: frontend.account.screening.CheckEmploymentVerificationStatusResponse
	(*PersonalProfileInfo)(nil),                           // 28: frontend.account.screening.PersonalProfileInfo
	(*CheckGmailVerificationStatusRequest)(nil),           // 29: frontend.account.screening.CheckGmailVerificationStatusRequest
	(*CheckGmailVerificationStatusResponse)(nil),          // 30: frontend.account.screening.CheckGmailVerificationStatusResponse
	(*AnnualSalaryRange)(nil),                             // 31: frontend.account.screening.AnnualSalaryRange
	(*BusinessOwnerInfo)(nil),                             // 32: frontend.account.screening.BusinessOwnerInfo
	(*EnrollmentNumber)(nil),                              // 33: frontend.account.screening.EnrollmentNumber
	(*StudentDetails)(nil),                                // 34: frontend.account.screening.StudentDetails
	(*UpdateEmploymentDataRequest)(nil),                   // 35: frontend.account.screening.UpdateEmploymentDataRequest
	(*AnnualSalary)(nil),                                  // 36: frontend.account.screening.AnnualSalary
	(*UpdateEmploymentDataResponse)(nil),                  // 37: frontend.account.screening.UpdateEmploymentDataResponse
	(*GetScreenerChoiceScreenOptionsRequest)(nil),         // 38: frontend.account.screening.GetScreenerChoiceScreenOptionsRequest
	(*GetScreenerChoiceScreenOptionsResponse)(nil),        // 39: frontend.account.screening.GetScreenerChoiceScreenOptionsResponse
	(*AnnualSalaryRange_AlternateDisplayed)(nil),          // 40: frontend.account.screening.AnnualSalaryRange.AlternateDisplayed
	(*header.RequestHeader)(nil),                          // 41: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil),                         // 42: frontend.header.ResponseHeader
	(*deeplink.Deeplink)(nil),                             // 43: frontend.deeplink.Deeplink
	(*typesv2.PollingRequestInfo)(nil),                    // 44: api.typesv2.PollingRequestInfo
	(*typesv2.PollingResponseInfo)(nil),                   // 45: api.typesv2.PollingResponseInfo
	(uistate.EmploymentType)(0),                           // 46: frontend.account.screening.EmploymentType
	(uistate.EmploymentProofType)(0),                      // 47: frontend.account.screening.EmploymentProofType
	(typesv2.CurrencyCode)(0),                             // 48: api.typesv2.CurrencyCode
}
var file_api_frontend_account_screening_service_proto_depIdxs = []int32{
	41, // 0: frontend.account.screening.SendWorkEmailOTPRequest.req:type_name -> frontend.header.RequestHeader
	42, // 1: frontend.account.screening.SendWorkEmailOTPResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 2: frontend.account.screening.SendWorkEmailOTPResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 3: frontend.account.screening.VerifyWorkEmailOTPRequest.req:type_name -> frontend.header.RequestHeader
	42, // 4: frontend.account.screening.VerifyWorkEmailOTPResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 5: frontend.account.screening.VerifyWorkEmailOTPResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 6: frontend.account.screening.CheckCreditReportAvailabilityStatusRequest.req:type_name -> frontend.header.RequestHeader
	42, // 7: frontend.account.screening.CheckCreditReportAvailabilityStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 8: frontend.account.screening.CheckCreditReportAvailabilityStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 9: frontend.account.screening.GetConsentAndVerifyCreditReportRequest.req:type_name -> frontend.header.RequestHeader
	42, // 10: frontend.account.screening.GetConsentAndVerifyCreditReportResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 11: frontend.account.screening.GetConsentAndVerifyCreditReportResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 12: frontend.account.screening.CheckCreditReportVerificationStatusRequest.req:type_name -> frontend.header.RequestHeader
	44, // 13: frontend.account.screening.CheckCreditReportVerificationStatusRequest.polling_request_info:type_name -> api.typesv2.PollingRequestInfo
	42, // 14: frontend.account.screening.CheckCreditReportVerificationStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 15: frontend.account.screening.CheckCreditReportVerificationStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	0,  // 16: frontend.account.screening.CheckCreditReportVerificationStatusResponse.credit_report_verification_status:type_name -> frontend.account.screening.CreditReportVerificationStatus
	45, // 17: frontend.account.screening.CheckCreditReportVerificationStatusResponse.polling_response_info:type_name -> api.typesv2.PollingResponseInfo
	41, // 18: frontend.account.screening.GetCompanyNamesRequest.req:type_name -> frontend.header.RequestHeader
	41, // 19: frontend.account.screening.GetCompanyNamesRequestV2.req:type_name -> frontend.header.RequestHeader
	42, // 20: frontend.account.screening.GetCompanyNamesResponseV2.resp_header:type_name -> frontend.header.ResponseHeader
	20, // 21: frontend.account.screening.GetCompanyNamesResponseV2.companies:type_name -> frontend.account.screening.CompanyInfoV2
	42, // 22: frontend.account.screening.GetCompanyNamesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	19, // 23: frontend.account.screening.GetCompanyNamesResponse.companies:type_name -> frontend.account.screening.CompanyInfo
	41, // 24: frontend.account.screening.ProcessEmploymentDataRequest.req:type_name -> frontend.header.RequestHeader
	46, // 25: frontend.account.screening.ProcessEmploymentDataRequest.employment_type:type_name -> frontend.account.screening.EmploymentType
	47, // 26: frontend.account.screening.ProcessEmploymentDataRequest.employment_proof_type:type_name -> frontend.account.screening.EmploymentProofType
	21, // 27: frontend.account.screening.ProcessEmploymentDataRequest.employer_info:type_name -> frontend.account.screening.UserSelectedEmployerInfo
	19, // 28: frontend.account.screening.ProcessEmploymentDataRequest.company_info:type_name -> frontend.account.screening.CompanyInfo
	28, // 29: frontend.account.screening.ProcessEmploymentDataRequest.personal_info:type_name -> frontend.account.screening.PersonalProfileInfo
	32, // 30: frontend.account.screening.ProcessEmploymentDataRequest.business_owner_info:type_name -> frontend.account.screening.BusinessOwnerInfo
	33, // 31: frontend.account.screening.ProcessEmploymentDataRequest.enrollment_number:type_name -> frontend.account.screening.EnrollmentNumber
	34, // 32: frontend.account.screening.ProcessEmploymentDataRequest.student_details:type_name -> frontend.account.screening.StudentDetails
	31, // 33: frontend.account.screening.ProcessEmploymentDataRequest.annual_salary_range:type_name -> frontend.account.screening.AnnualSalaryRange
	36, // 34: frontend.account.screening.ProcessEmploymentDataRequest.annual_salary:type_name -> frontend.account.screening.AnnualSalary
	31, // 35: frontend.account.screening.ProcessEmploymentDataRequest.annual_transaction_volume:type_name -> frontend.account.screening.AnnualSalaryRange
	42, // 36: frontend.account.screening.ProcessEmploymentDataResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 37: frontend.account.screening.ProcessEmploymentDataResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 38: frontend.account.screening.CheckEmploymentVerificationStatusRequest.req:type_name -> frontend.header.RequestHeader
	42, // 39: frontend.account.screening.CheckEmploymentVerificationStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 40: frontend.account.screening.CheckEmploymentVerificationStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 41: frontend.account.screening.CheckGmailVerificationStatusRequest.req:type_name -> frontend.header.RequestHeader
	43, // 42: frontend.account.screening.CheckGmailVerificationStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	42, // 43: frontend.account.screening.CheckGmailVerificationStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	48, // 44: frontend.account.screening.AnnualSalaryRange.currency_code:type_name -> api.typesv2.CurrencyCode
	40, // 45: frontend.account.screening.AnnualSalaryRange.alternate_displayed:type_name -> frontend.account.screening.AnnualSalaryRange.AlternateDisplayed
	41, // 46: frontend.account.screening.UpdateEmploymentDataRequest.req:type_name -> frontend.header.RequestHeader
	46, // 47: frontend.account.screening.UpdateEmploymentDataRequest.employment_type:type_name -> frontend.account.screening.EmploymentType
	47, // 48: frontend.account.screening.UpdateEmploymentDataRequest.employment_proof_type:type_name -> frontend.account.screening.EmploymentProofType
	19, // 49: frontend.account.screening.UpdateEmploymentDataRequest.company_info:type_name -> frontend.account.screening.CompanyInfo
	21, // 50: frontend.account.screening.UpdateEmploymentDataRequest.employer_info:type_name -> frontend.account.screening.UserSelectedEmployerInfo
	28, // 51: frontend.account.screening.UpdateEmploymentDataRequest.personal_info:type_name -> frontend.account.screening.PersonalProfileInfo
	32, // 52: frontend.account.screening.UpdateEmploymentDataRequest.business_owner_info:type_name -> frontend.account.screening.BusinessOwnerInfo
	33, // 53: frontend.account.screening.UpdateEmploymentDataRequest.enrollment_number:type_name -> frontend.account.screening.EnrollmentNumber
	34, // 54: frontend.account.screening.UpdateEmploymentDataRequest.student_details:type_name -> frontend.account.screening.StudentDetails
	31, // 55: frontend.account.screening.UpdateEmploymentDataRequest.annual_salary_range:type_name -> frontend.account.screening.AnnualSalaryRange
	36, // 56: frontend.account.screening.UpdateEmploymentDataRequest.annual_salary:type_name -> frontend.account.screening.AnnualSalary
	5,  // 57: frontend.account.screening.UpdateEmploymentDataRequest.provenance:type_name -> frontend.account.screening.UpdateEmploymentDataRequest.Provenance
	31, // 58: frontend.account.screening.AnnualSalary.range:type_name -> frontend.account.screening.AnnualSalaryRange
	42, // 59: frontend.account.screening.UpdateEmploymentDataResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 60: frontend.account.screening.UpdateEmploymentDataResponse.next_action:type_name -> frontend.deeplink.Deeplink
	41, // 61: frontend.account.screening.GetScreenerChoiceScreenOptionsRequest.req:type_name -> frontend.header.RequestHeader
	42, // 62: frontend.account.screening.GetScreenerChoiceScreenOptionsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 63: frontend.account.screening.GetScreenerChoiceScreenOptionsResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	48, // 64: frontend.account.screening.AnnualSalaryRange.AlternateDisplayed.currency_code:type_name -> api.typesv2.CurrencyCode
	11, // 65: frontend.account.screening.Screening.CheckCreditReportAvailabilityStatus:input_type -> frontend.account.screening.CheckCreditReportAvailabilityStatusRequest
	13, // 66: frontend.account.screening.Screening.GetConsentAndVerifyCreditReport:input_type -> frontend.account.screening.GetConsentAndVerifyCreditReportRequest
	15, // 67: frontend.account.screening.Screening.CheckCreditReportVerificationStatus:input_type -> frontend.account.screening.CheckCreditReportVerificationStatusRequest
	17, // 68: frontend.account.screening.Screening.GetCompanyNames:input_type -> frontend.account.screening.GetCompanyNamesRequest
	24, // 69: frontend.account.screening.Screening.ProcessEmploymentData:input_type -> frontend.account.screening.ProcessEmploymentDataRequest
	35, // 70: frontend.account.screening.Screening.UpdateEmploymentData:input_type -> frontend.account.screening.UpdateEmploymentDataRequest
	26, // 71: frontend.account.screening.Screening.CheckEmploymentVerificationStatus:input_type -> frontend.account.screening.CheckEmploymentVerificationStatusRequest
	29, // 72: frontend.account.screening.Screening.CheckGmailVerificationStatus:input_type -> frontend.account.screening.CheckGmailVerificationStatusRequest
	7,  // 73: frontend.account.screening.Screening.SendWorkEmailOTP:input_type -> frontend.account.screening.SendWorkEmailOTPRequest
	9,  // 74: frontend.account.screening.Screening.VerifyWorkEmailOTP:input_type -> frontend.account.screening.VerifyWorkEmailOTPRequest
	18, // 75: frontend.account.screening.Screening.GetCompanyNamesV2:input_type -> frontend.account.screening.GetCompanyNamesRequestV2
	38, // 76: frontend.account.screening.Screening.GetScreenerChoiceScreenOptions:input_type -> frontend.account.screening.GetScreenerChoiceScreenOptionsRequest
	12, // 77: frontend.account.screening.Screening.CheckCreditReportAvailabilityStatus:output_type -> frontend.account.screening.CheckCreditReportAvailabilityStatusResponse
	14, // 78: frontend.account.screening.Screening.GetConsentAndVerifyCreditReport:output_type -> frontend.account.screening.GetConsentAndVerifyCreditReportResponse
	16, // 79: frontend.account.screening.Screening.CheckCreditReportVerificationStatus:output_type -> frontend.account.screening.CheckCreditReportVerificationStatusResponse
	23, // 80: frontend.account.screening.Screening.GetCompanyNames:output_type -> frontend.account.screening.GetCompanyNamesResponse
	25, // 81: frontend.account.screening.Screening.ProcessEmploymentData:output_type -> frontend.account.screening.ProcessEmploymentDataResponse
	37, // 82: frontend.account.screening.Screening.UpdateEmploymentData:output_type -> frontend.account.screening.UpdateEmploymentDataResponse
	27, // 83: frontend.account.screening.Screening.CheckEmploymentVerificationStatus:output_type -> frontend.account.screening.CheckEmploymentVerificationStatusResponse
	30, // 84: frontend.account.screening.Screening.CheckGmailVerificationStatus:output_type -> frontend.account.screening.CheckGmailVerificationStatusResponse
	8,  // 85: frontend.account.screening.Screening.SendWorkEmailOTP:output_type -> frontend.account.screening.SendWorkEmailOTPResponse
	10, // 86: frontend.account.screening.Screening.VerifyWorkEmailOTP:output_type -> frontend.account.screening.VerifyWorkEmailOTPResponse
	22, // 87: frontend.account.screening.Screening.GetCompanyNamesV2:output_type -> frontend.account.screening.GetCompanyNamesResponseV2
	39, // 88: frontend.account.screening.Screening.GetScreenerChoiceScreenOptions:output_type -> frontend.account.screening.GetScreenerChoiceScreenOptionsResponse
	77, // [77:89] is the sub-list for method output_type
	65, // [65:77] is the sub-list for method input_type
	65, // [65:65] is the sub-list for extension type_name
	65, // [65:65] is the sub-list for extension extendee
	0,  // [0:65] is the sub-list for field type_name
}

func init() { file_api_frontend_account_screening_service_proto_init() }
func file_api_frontend_account_screening_service_proto_init() {
	if File_api_frontend_account_screening_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_account_screening_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendWorkEmailOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendWorkEmailOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyWorkEmailOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyWorkEmailOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreditReportAvailabilityStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreditReportAvailabilityStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConsentAndVerifyCreditReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConsentAndVerifyCreditReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreditReportVerificationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreditReportVerificationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyNamesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyNamesRequestV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyInfoV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSelectedEmployerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyNamesResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyNamesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEmploymentDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEmploymentDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEmploymentVerificationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEmploymentVerificationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalProfileInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckGmailVerificationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckGmailVerificationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualSalaryRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOwnerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollmentNumber); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StudentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEmploymentDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualSalary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEmploymentDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerChoiceScreenOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScreenerChoiceScreenOptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_screening_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualSalaryRange_AlternateDisplayed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_account_screening_service_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*ProcessEmploymentDataRequest_EmployerInfo)(nil),
		(*ProcessEmploymentDataRequest_CompanyInfo)(nil),
		(*ProcessEmploymentDataRequest_PersonalInfo)(nil),
		(*ProcessEmploymentDataRequest_BusinessOwnerInfo)(nil),
		(*ProcessEmploymentDataRequest_EnrollmentNumber)(nil),
		(*ProcessEmploymentDataRequest_StudentDetails)(nil),
	}
	file_api_frontend_account_screening_service_proto_msgTypes[28].OneofWrappers = []interface{}{
		(*UpdateEmploymentDataRequest_CompanyInfo)(nil),
		(*UpdateEmploymentDataRequest_EmployerInfo)(nil),
		(*UpdateEmploymentDataRequest_PersonalInfo)(nil),
		(*UpdateEmploymentDataRequest_BusinessOwnerInfo)(nil),
		(*UpdateEmploymentDataRequest_EnrollmentNumber)(nil),
		(*UpdateEmploymentDataRequest_StudentDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_account_screening_service_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_account_screening_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_account_screening_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_account_screening_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_account_screening_service_proto_msgTypes,
	}.Build()
	File_api_frontend_account_screening_service_proto = out.File
	file_api_frontend_account_screening_service_proto_rawDesc = nil
	file_api_frontend_account_screening_service_proto_goTypes = nil
	file_api_frontend_account_screening_service_proto_depIdxs = nil
}
