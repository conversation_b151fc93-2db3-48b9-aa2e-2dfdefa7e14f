// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/account/screening/service.proto

package screening

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.CurrencyCode(0)
)

// define the regex for a UUID once up-front
var _service_uuidPattern = regexp.MustCompile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")

// Validate checks the field values on SendWorkEmailOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendWorkEmailOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendWorkEmailOTPRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendWorkEmailOTPRequestMultiError, or nil if none found.
func (m *SendWorkEmailOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendWorkEmailOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendWorkEmailOTPRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendWorkEmailOTPRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendWorkEmailOTPRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetEmail()) < 1 {
		err := SendWorkEmailOTPRequestValidationError{
			field:  "Email",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SendWorkEmailOTPRequest_Token_Pattern.MatchString(m.GetToken()) {
		err := SendWorkEmailOTPRequestValidationError{
			field:  "Token",
			reason: "value does not match regex pattern \"(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ClientReqId

	// no validation rules for Client

	if len(errors) > 0 {
		return SendWorkEmailOTPRequestMultiError(errors)
	}

	return nil
}

// SendWorkEmailOTPRequestMultiError is an error wrapping multiple validation
// errors returned by SendWorkEmailOTPRequest.ValidateAll() if the designated
// constraints aren't met.
type SendWorkEmailOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendWorkEmailOTPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendWorkEmailOTPRequestMultiError) AllErrors() []error { return m }

// SendWorkEmailOTPRequestValidationError is the validation error returned by
// SendWorkEmailOTPRequest.Validate if the designated constraints aren't met.
type SendWorkEmailOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendWorkEmailOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendWorkEmailOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendWorkEmailOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendWorkEmailOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendWorkEmailOTPRequestValidationError) ErrorName() string {
	return "SendWorkEmailOTPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendWorkEmailOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendWorkEmailOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendWorkEmailOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendWorkEmailOTPRequestValidationError{}

var _SendWorkEmailOTPRequest_Token_Pattern = regexp.MustCompile("(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)")

// Validate checks the field values on SendWorkEmailOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendWorkEmailOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendWorkEmailOTPResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendWorkEmailOTPResponseMultiError, or nil if none found.
func (m *SendWorkEmailOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendWorkEmailOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendWorkEmailOTPResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendWorkEmailOTPResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendWorkEmailOTPResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendWorkEmailOTPResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendWorkEmailOTPResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendWorkEmailOTPResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for ResendAfter

	// no validation rules for OtpExpiry

	if len(errors) > 0 {
		return SendWorkEmailOTPResponseMultiError(errors)
	}

	return nil
}

// SendWorkEmailOTPResponseMultiError is an error wrapping multiple validation
// errors returned by SendWorkEmailOTPResponse.ValidateAll() if the designated
// constraints aren't met.
type SendWorkEmailOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendWorkEmailOTPResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendWorkEmailOTPResponseMultiError) AllErrors() []error { return m }

// SendWorkEmailOTPResponseValidationError is the validation error returned by
// SendWorkEmailOTPResponse.Validate if the designated constraints aren't met.
type SendWorkEmailOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendWorkEmailOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendWorkEmailOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendWorkEmailOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendWorkEmailOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendWorkEmailOTPResponseValidationError) ErrorName() string {
	return "SendWorkEmailOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendWorkEmailOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendWorkEmailOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendWorkEmailOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendWorkEmailOTPResponseValidationError{}

// Validate checks the field values on VerifyWorkEmailOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyWorkEmailOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyWorkEmailOTPRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyWorkEmailOTPRequestMultiError, or nil if none found.
func (m *VerifyWorkEmailOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyWorkEmailOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyWorkEmailOTPRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyWorkEmailOTPRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyWorkEmailOTPRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if err := m._validateUuid(m.GetToken()); err != nil {
		err = VerifyWorkEmailOTPRequestValidationError{
			field:  "Token",
			reason: "value must be a valid UUID",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOtp()) != 6 {
		err := VerifyWorkEmailOTPRequestValidationError{
			field:  "Otp",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if !_VerifyWorkEmailOTPRequest_Otp_Pattern.MatchString(m.GetOtp()) {
		err := VerifyWorkEmailOTPRequestValidationError{
			field:  "Otp",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ClientReqId

	// no validation rules for Client

	if len(errors) > 0 {
		return VerifyWorkEmailOTPRequestMultiError(errors)
	}

	return nil
}

func (m *VerifyWorkEmailOTPRequest) _validateUuid(uuid string) error {
	if matched := _service_uuidPattern.MatchString(uuid); !matched {
		return errors.New("invalid uuid format")
	}

	return nil
}

// VerifyWorkEmailOTPRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyWorkEmailOTPRequest.ValidateAll() if the
// designated constraints aren't met.
type VerifyWorkEmailOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyWorkEmailOTPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyWorkEmailOTPRequestMultiError) AllErrors() []error { return m }

// VerifyWorkEmailOTPRequestValidationError is the validation error returned by
// VerifyWorkEmailOTPRequest.Validate if the designated constraints aren't met.
type VerifyWorkEmailOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyWorkEmailOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyWorkEmailOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyWorkEmailOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyWorkEmailOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyWorkEmailOTPRequestValidationError) ErrorName() string {
	return "VerifyWorkEmailOTPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyWorkEmailOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyWorkEmailOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyWorkEmailOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyWorkEmailOTPRequestValidationError{}

var _VerifyWorkEmailOTPRequest_Otp_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on VerifyWorkEmailOTPResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyWorkEmailOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyWorkEmailOTPResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyWorkEmailOTPResponseMultiError, or nil if none found.
func (m *VerifyWorkEmailOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyWorkEmailOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyWorkEmailOTPResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyWorkEmailOTPResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyWorkEmailOTPResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyWorkEmailOTPResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyWorkEmailOTPResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyWorkEmailOTPResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransitionTitleText

	// no validation rules for TransitionSubtitleText

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return VerifyWorkEmailOTPResponseMultiError(errors)
	}

	return nil
}

// VerifyWorkEmailOTPResponseMultiError is an error wrapping multiple
// validation errors returned by VerifyWorkEmailOTPResponse.ValidateAll() if
// the designated constraints aren't met.
type VerifyWorkEmailOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyWorkEmailOTPResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyWorkEmailOTPResponseMultiError) AllErrors() []error { return m }

// VerifyWorkEmailOTPResponseValidationError is the validation error returned
// by VerifyWorkEmailOTPResponse.Validate if the designated constraints aren't met.
type VerifyWorkEmailOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyWorkEmailOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyWorkEmailOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyWorkEmailOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyWorkEmailOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyWorkEmailOTPResponseValidationError) ErrorName() string {
	return "VerifyWorkEmailOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyWorkEmailOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyWorkEmailOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyWorkEmailOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyWorkEmailOTPResponseValidationError{}

// Validate checks the field values on
// CheckCreditReportAvailabilityStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckCreditReportAvailabilityStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckCreditReportAvailabilityStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckCreditReportAvailabilityStatusRequestMultiError, or nil if none found.
func (m *CheckCreditReportAvailabilityStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCreditReportAvailabilityStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportAvailabilityStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportAvailabilityStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportAvailabilityStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckCreditReportAvailabilityStatusRequestMultiError(errors)
	}

	return nil
}

// CheckCreditReportAvailabilityStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckCreditReportAvailabilityStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckCreditReportAvailabilityStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCreditReportAvailabilityStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCreditReportAvailabilityStatusRequestMultiError) AllErrors() []error { return m }

// CheckCreditReportAvailabilityStatusRequestValidationError is the validation
// error returned by CheckCreditReportAvailabilityStatusRequest.Validate if
// the designated constraints aren't met.
type CheckCreditReportAvailabilityStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCreditReportAvailabilityStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCreditReportAvailabilityStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCreditReportAvailabilityStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCreditReportAvailabilityStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCreditReportAvailabilityStatusRequestValidationError) ErrorName() string {
	return "CheckCreditReportAvailabilityStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCreditReportAvailabilityStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCreditReportAvailabilityStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCreditReportAvailabilityStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCreditReportAvailabilityStatusRequestValidationError{}

// Validate checks the field values on
// CheckCreditReportAvailabilityStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckCreditReportAvailabilityStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckCreditReportAvailabilityStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckCreditReportAvailabilityStatusResponseMultiError, or nil if none found.
func (m *CheckCreditReportAvailabilityStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCreditReportAvailabilityStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportAvailabilityStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportAvailabilityStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportAvailabilityStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportAvailabilityStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportAvailabilityStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportAvailabilityStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextPollAfter

	if len(errors) > 0 {
		return CheckCreditReportAvailabilityStatusResponseMultiError(errors)
	}

	return nil
}

// CheckCreditReportAvailabilityStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// CheckCreditReportAvailabilityStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckCreditReportAvailabilityStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCreditReportAvailabilityStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCreditReportAvailabilityStatusResponseMultiError) AllErrors() []error { return m }

// CheckCreditReportAvailabilityStatusResponseValidationError is the validation
// error returned by CheckCreditReportAvailabilityStatusResponse.Validate if
// the designated constraints aren't met.
type CheckCreditReportAvailabilityStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCreditReportAvailabilityStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCreditReportAvailabilityStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCreditReportAvailabilityStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCreditReportAvailabilityStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCreditReportAvailabilityStatusResponseValidationError) ErrorName() string {
	return "CheckCreditReportAvailabilityStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCreditReportAvailabilityStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCreditReportAvailabilityStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCreditReportAvailabilityStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCreditReportAvailabilityStatusResponseValidationError{}

// Validate checks the field values on GetConsentAndVerifyCreditReportRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetConsentAndVerifyCreditReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetConsentAndVerifyCreditReportRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetConsentAndVerifyCreditReportRequestMultiError, or nil if none found.
func (m *GetConsentAndVerifyCreditReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConsentAndVerifyCreditReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentAndVerifyCreditReportRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentAndVerifyCreditReportRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentAndVerifyCreditReportRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Consent

	if len(errors) > 0 {
		return GetConsentAndVerifyCreditReportRequestMultiError(errors)
	}

	return nil
}

// GetConsentAndVerifyCreditReportRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetConsentAndVerifyCreditReportRequest.ValidateAll() if the designated
// constraints aren't met.
type GetConsentAndVerifyCreditReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConsentAndVerifyCreditReportRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConsentAndVerifyCreditReportRequestMultiError) AllErrors() []error { return m }

// GetConsentAndVerifyCreditReportRequestValidationError is the validation
// error returned by GetConsentAndVerifyCreditReportRequest.Validate if the
// designated constraints aren't met.
type GetConsentAndVerifyCreditReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConsentAndVerifyCreditReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConsentAndVerifyCreditReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConsentAndVerifyCreditReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConsentAndVerifyCreditReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConsentAndVerifyCreditReportRequestValidationError) ErrorName() string {
	return "GetConsentAndVerifyCreditReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConsentAndVerifyCreditReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConsentAndVerifyCreditReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConsentAndVerifyCreditReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConsentAndVerifyCreditReportRequestValidationError{}

// Validate checks the field values on GetConsentAndVerifyCreditReportResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetConsentAndVerifyCreditReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetConsentAndVerifyCreditReportResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetConsentAndVerifyCreditReportResponseMultiError, or nil if none found.
func (m *GetConsentAndVerifyCreditReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConsentAndVerifyCreditReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentAndVerifyCreditReportResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentAndVerifyCreditReportResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentAndVerifyCreditReportResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentAndVerifyCreditReportResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentAndVerifyCreditReportResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentAndVerifyCreditReportResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConsentAndVerifyCreditReportResponseMultiError(errors)
	}

	return nil
}

// GetConsentAndVerifyCreditReportResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetConsentAndVerifyCreditReportResponse.ValidateAll() if the designated
// constraints aren't met.
type GetConsentAndVerifyCreditReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConsentAndVerifyCreditReportResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConsentAndVerifyCreditReportResponseMultiError) AllErrors() []error { return m }

// GetConsentAndVerifyCreditReportResponseValidationError is the validation
// error returned by GetConsentAndVerifyCreditReportResponse.Validate if the
// designated constraints aren't met.
type GetConsentAndVerifyCreditReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConsentAndVerifyCreditReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConsentAndVerifyCreditReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConsentAndVerifyCreditReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConsentAndVerifyCreditReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConsentAndVerifyCreditReportResponseValidationError) ErrorName() string {
	return "GetConsentAndVerifyCreditReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConsentAndVerifyCreditReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConsentAndVerifyCreditReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConsentAndVerifyCreditReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConsentAndVerifyCreditReportResponseValidationError{}

// Validate checks the field values on
// CheckCreditReportVerificationStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckCreditReportVerificationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckCreditReportVerificationStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckCreditReportVerificationStatusRequestMultiError, or nil if none found.
func (m *CheckCreditReportVerificationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCreditReportVerificationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportVerificationStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPollingRequestInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusRequestValidationError{
					field:  "PollingRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusRequestValidationError{
					field:  "PollingRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingRequestInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportVerificationStatusRequestValidationError{
				field:  "PollingRequestInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckCreditReportVerificationStatusRequestMultiError(errors)
	}

	return nil
}

// CheckCreditReportVerificationStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckCreditReportVerificationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckCreditReportVerificationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCreditReportVerificationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCreditReportVerificationStatusRequestMultiError) AllErrors() []error { return m }

// CheckCreditReportVerificationStatusRequestValidationError is the validation
// error returned by CheckCreditReportVerificationStatusRequest.Validate if
// the designated constraints aren't met.
type CheckCreditReportVerificationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCreditReportVerificationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCreditReportVerificationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCreditReportVerificationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCreditReportVerificationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCreditReportVerificationStatusRequestValidationError) ErrorName() string {
	return "CheckCreditReportVerificationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCreditReportVerificationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCreditReportVerificationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCreditReportVerificationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCreditReportVerificationStatusRequestValidationError{}

// Validate checks the field values on
// CheckCreditReportVerificationStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckCreditReportVerificationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckCreditReportVerificationStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckCreditReportVerificationStatusResponseMultiError, or nil if none found.
func (m *CheckCreditReportVerificationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCreditReportVerificationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportVerificationStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportVerificationStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextPollAfter

	// no validation rules for CreditReportVerificationStatus

	// no validation rules for TransitionTitleText

	// no validation rules for TransitionSubtitleText

	// no validation rules for IconUrl

	if all {
		switch v := interface{}(m.GetPollingResponseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusResponseValidationError{
					field:  "PollingResponseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditReportVerificationStatusResponseValidationError{
					field:  "PollingResponseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingResponseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditReportVerificationStatusResponseValidationError{
				field:  "PollingResponseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckCreditReportVerificationStatusResponseMultiError(errors)
	}

	return nil
}

// CheckCreditReportVerificationStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// CheckCreditReportVerificationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckCreditReportVerificationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCreditReportVerificationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCreditReportVerificationStatusResponseMultiError) AllErrors() []error { return m }

// CheckCreditReportVerificationStatusResponseValidationError is the validation
// error returned by CheckCreditReportVerificationStatusResponse.Validate if
// the designated constraints aren't met.
type CheckCreditReportVerificationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCreditReportVerificationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCreditReportVerificationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCreditReportVerificationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCreditReportVerificationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCreditReportVerificationStatusResponseValidationError) ErrorName() string {
	return "CheckCreditReportVerificationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCreditReportVerificationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCreditReportVerificationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCreditReportVerificationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCreditReportVerificationStatusResponseValidationError{}

// Validate checks the field values on GetCompanyNamesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyNamesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyNamesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyNamesRequestMultiError, or nil if none found.
func (m *GetCompanyNamesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyNamesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyNamesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyNamesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyNamesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NamePrefix

	if len(errors) > 0 {
		return GetCompanyNamesRequestMultiError(errors)
	}

	return nil
}

// GetCompanyNamesRequestMultiError is an error wrapping multiple validation
// errors returned by GetCompanyNamesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyNamesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyNamesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyNamesRequestMultiError) AllErrors() []error { return m }

// GetCompanyNamesRequestValidationError is the validation error returned by
// GetCompanyNamesRequest.Validate if the designated constraints aren't met.
type GetCompanyNamesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyNamesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyNamesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyNamesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyNamesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyNamesRequestValidationError) ErrorName() string {
	return "GetCompanyNamesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyNamesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyNamesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyNamesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyNamesRequestValidationError{}

// Validate checks the field values on GetCompanyNamesRequestV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyNamesRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyNamesRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyNamesRequestV2MultiError, or nil if none found.
func (m *GetCompanyNamesRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyNamesRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyNamesRequestV2ValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyNamesRequestV2ValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyNamesRequestV2ValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetNamePrefix()) < 3 {
		err := GetCompanyNamesRequestV2ValidationError{
			field:  "NamePrefix",
			reason: "value length must be at least 3 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCompanyNamesRequestV2MultiError(errors)
	}

	return nil
}

// GetCompanyNamesRequestV2MultiError is an error wrapping multiple validation
// errors returned by GetCompanyNamesRequestV2.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyNamesRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyNamesRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyNamesRequestV2MultiError) AllErrors() []error { return m }

// GetCompanyNamesRequestV2ValidationError is the validation error returned by
// GetCompanyNamesRequestV2.Validate if the designated constraints aren't met.
type GetCompanyNamesRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyNamesRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyNamesRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyNamesRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyNamesRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyNamesRequestV2ValidationError) ErrorName() string {
	return "GetCompanyNamesRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyNamesRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyNamesRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyNamesRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyNamesRequestV2ValidationError{}

// Validate checks the field values on CompanyInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CompanyInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompanyInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CompanyInfoMultiError, or
// nil if none found.
func (m *CompanyInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CompanyInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for VendorId

	// no validation rules for IsEpfRegistered

	// no validation rules for EnteredText

	if len(errors) > 0 {
		return CompanyInfoMultiError(errors)
	}

	return nil
}

// CompanyInfoMultiError is an error wrapping multiple validation errors
// returned by CompanyInfo.ValidateAll() if the designated constraints aren't met.
type CompanyInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompanyInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompanyInfoMultiError) AllErrors() []error { return m }

// CompanyInfoValidationError is the validation error returned by
// CompanyInfo.Validate if the designated constraints aren't met.
type CompanyInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompanyInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompanyInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompanyInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompanyInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompanyInfoValidationError) ErrorName() string { return "CompanyInfoValidationError" }

// Error satisfies the builtin error interface
func (e CompanyInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompanyInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompanyInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompanyInfoValidationError{}

// Validate checks the field values on CompanyInfoV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CompanyInfoV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompanyInfoV2 with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CompanyInfoV2MultiError, or
// nil if none found.
func (m *CompanyInfoV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CompanyInfoV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for LegalName

	// no validation rules for TradeName

	// no validation rules for IsVerifiedEmployer

	if len(errors) > 0 {
		return CompanyInfoV2MultiError(errors)
	}

	return nil
}

// CompanyInfoV2MultiError is an error wrapping multiple validation errors
// returned by CompanyInfoV2.ValidateAll() if the designated constraints
// aren't met.
type CompanyInfoV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompanyInfoV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompanyInfoV2MultiError) AllErrors() []error { return m }

// CompanyInfoV2ValidationError is the validation error returned by
// CompanyInfoV2.Validate if the designated constraints aren't met.
type CompanyInfoV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompanyInfoV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompanyInfoV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompanyInfoV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompanyInfoV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompanyInfoV2ValidationError) ErrorName() string { return "CompanyInfoV2ValidationError" }

// Error satisfies the builtin error interface
func (e CompanyInfoV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompanyInfoV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompanyInfoV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompanyInfoV2ValidationError{}

// Validate checks the field values on UserSelectedEmployerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserSelectedEmployerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserSelectedEmployerInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserSelectedEmployerInfoMultiError, or nil if none found.
func (m *UserSelectedEmployerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserSelectedEmployerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for EnteredText

	if len(errors) > 0 {
		return UserSelectedEmployerInfoMultiError(errors)
	}

	return nil
}

// UserSelectedEmployerInfoMultiError is an error wrapping multiple validation
// errors returned by UserSelectedEmployerInfo.ValidateAll() if the designated
// constraints aren't met.
type UserSelectedEmployerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserSelectedEmployerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserSelectedEmployerInfoMultiError) AllErrors() []error { return m }

// UserSelectedEmployerInfoValidationError is the validation error returned by
// UserSelectedEmployerInfo.Validate if the designated constraints aren't met.
type UserSelectedEmployerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserSelectedEmployerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserSelectedEmployerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserSelectedEmployerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserSelectedEmployerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserSelectedEmployerInfoValidationError) ErrorName() string {
	return "UserSelectedEmployerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UserSelectedEmployerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserSelectedEmployerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserSelectedEmployerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserSelectedEmployerInfoValidationError{}

// Validate checks the field values on GetCompanyNamesResponseV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyNamesResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyNamesResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyNamesResponseV2MultiError, or nil if none found.
func (m *GetCompanyNamesResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyNamesResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyNamesResponseV2ValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyNamesResponseV2ValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyNamesResponseV2ValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCompanies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyNamesResponseV2ValidationError{
						field:  fmt.Sprintf("Companies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyNamesResponseV2ValidationError{
						field:  fmt.Sprintf("Companies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyNamesResponseV2ValidationError{
					field:  fmt.Sprintf("Companies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyNamesResponseV2MultiError(errors)
	}

	return nil
}

// GetCompanyNamesResponseV2MultiError is an error wrapping multiple validation
// errors returned by GetCompanyNamesResponseV2.ValidateAll() if the
// designated constraints aren't met.
type GetCompanyNamesResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyNamesResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyNamesResponseV2MultiError) AllErrors() []error { return m }

// GetCompanyNamesResponseV2ValidationError is the validation error returned by
// GetCompanyNamesResponseV2.Validate if the designated constraints aren't met.
type GetCompanyNamesResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyNamesResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyNamesResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyNamesResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyNamesResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyNamesResponseV2ValidationError) ErrorName() string {
	return "GetCompanyNamesResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyNamesResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyNamesResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyNamesResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyNamesResponseV2ValidationError{}

// Validate checks the field values on GetCompanyNamesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCompanyNamesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCompanyNamesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCompanyNamesResponseMultiError, or nil if none found.
func (m *GetCompanyNamesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCompanyNamesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCompanyNamesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCompanyNamesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCompanyNamesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCompanies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCompanyNamesResponseValidationError{
						field:  fmt.Sprintf("Companies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCompanyNamesResponseValidationError{
						field:  fmt.Sprintf("Companies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCompanyNamesResponseValidationError{
					field:  fmt.Sprintf("Companies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCompanyNamesResponseMultiError(errors)
	}

	return nil
}

// GetCompanyNamesResponseMultiError is an error wrapping multiple validation
// errors returned by GetCompanyNamesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCompanyNamesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCompanyNamesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCompanyNamesResponseMultiError) AllErrors() []error { return m }

// GetCompanyNamesResponseValidationError is the validation error returned by
// GetCompanyNamesResponse.Validate if the designated constraints aren't met.
type GetCompanyNamesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCompanyNamesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCompanyNamesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCompanyNamesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCompanyNamesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCompanyNamesResponseValidationError) ErrorName() string {
	return "GetCompanyNamesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCompanyNamesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCompanyNamesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCompanyNamesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCompanyNamesResponseValidationError{}

// Validate checks the field values on ProcessEmploymentDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessEmploymentDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessEmploymentDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessEmploymentDataRequestMultiError, or nil if none found.
func (m *ProcessEmploymentDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessEmploymentDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEmploymentDataRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for EmploymentType

	// no validation rules for EmploymentProofType

	if all {
		switch v := interface{}(m.GetAnnualSalaryRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "AnnualSalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "AnnualSalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualSalaryRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEmploymentDataRequestValidationError{
				field:  "AnnualSalaryRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnnualSalary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "AnnualSalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "AnnualSalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualSalary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEmploymentDataRequestValidationError{
				field:  "AnnualSalary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IncomeDiscrepancyConsentGiven

	// no validation rules for UpdateSource

	// no validation rules for OccupationType

	// no validation rules for Qualification

	// no validation rules for SourceOfFunds

	if all {
		switch v := interface{}(m.GetAnnualTransactionVolume()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "AnnualTransactionVolume",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEmploymentDataRequestValidationError{
					field:  "AnnualTransactionVolume",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualTransactionVolume()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEmploymentDataRequestValidationError{
				field:  "AnnualTransactionVolume",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Input.(type) {
	case *ProcessEmploymentDataRequest_EmployerInfo:
		if v == nil {
			err := ProcessEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmployerInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "EmployerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "EmployerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmployerInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEmploymentDataRequestValidationError{
					field:  "EmployerInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessEmploymentDataRequest_CompanyInfo:
		if v == nil {
			err := ProcessEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCompanyInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "CompanyInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "CompanyInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompanyInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEmploymentDataRequestValidationError{
					field:  "CompanyInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessEmploymentDataRequest_PersonalInfo:
		if v == nil {
			err := ProcessEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPersonalInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "PersonalInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "PersonalInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPersonalInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEmploymentDataRequestValidationError{
					field:  "PersonalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessEmploymentDataRequest_BusinessOwnerInfo:
		if v == nil {
			err := ProcessEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBusinessOwnerInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "BusinessOwnerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "BusinessOwnerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBusinessOwnerInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEmploymentDataRequestValidationError{
					field:  "BusinessOwnerInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessEmploymentDataRequest_EnrollmentNumber:
		if v == nil {
			err := ProcessEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnrollmentNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "EnrollmentNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "EnrollmentNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnrollmentNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEmploymentDataRequestValidationError{
					field:  "EnrollmentNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessEmploymentDataRequest_StudentDetails:
		if v == nil {
			err := ProcessEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStudentDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "StudentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEmploymentDataRequestValidationError{
						field:  "StudentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStudentDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEmploymentDataRequestValidationError{
					field:  "StudentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProcessEmploymentDataRequestMultiError(errors)
	}

	return nil
}

// ProcessEmploymentDataRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessEmploymentDataRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessEmploymentDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessEmploymentDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessEmploymentDataRequestMultiError) AllErrors() []error { return m }

// ProcessEmploymentDataRequestValidationError is the validation error returned
// by ProcessEmploymentDataRequest.Validate if the designated constraints
// aren't met.
type ProcessEmploymentDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessEmploymentDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessEmploymentDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessEmploymentDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessEmploymentDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessEmploymentDataRequestValidationError) ErrorName() string {
	return "ProcessEmploymentDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessEmploymentDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessEmploymentDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessEmploymentDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessEmploymentDataRequestValidationError{}

// Validate checks the field values on ProcessEmploymentDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessEmploymentDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessEmploymentDataResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessEmploymentDataResponseMultiError, or nil if none found.
func (m *ProcessEmploymentDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessEmploymentDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEmploymentDataResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEmploymentDataResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEmploymentDataResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEmploymentDataResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEmploymentDataResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEmploymentDataResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessEmploymentDataResponseMultiError(errors)
	}

	return nil
}

// ProcessEmploymentDataResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessEmploymentDataResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessEmploymentDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessEmploymentDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessEmploymentDataResponseMultiError) AllErrors() []error { return m }

// ProcessEmploymentDataResponseValidationError is the validation error
// returned by ProcessEmploymentDataResponse.Validate if the designated
// constraints aren't met.
type ProcessEmploymentDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessEmploymentDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessEmploymentDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessEmploymentDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessEmploymentDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessEmploymentDataResponseValidationError) ErrorName() string {
	return "ProcessEmploymentDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessEmploymentDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessEmploymentDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessEmploymentDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessEmploymentDataResponseValidationError{}

// Validate checks the field values on CheckEmploymentVerificationStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckEmploymentVerificationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckEmploymentVerificationStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckEmploymentVerificationStatusRequestMultiError, or nil if none found.
func (m *CheckEmploymentVerificationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEmploymentVerificationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEmploymentVerificationStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEmploymentVerificationStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEmploymentVerificationStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckEmploymentVerificationStatusRequestMultiError(errors)
	}

	return nil
}

// CheckEmploymentVerificationStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckEmploymentVerificationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckEmploymentVerificationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEmploymentVerificationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEmploymentVerificationStatusRequestMultiError) AllErrors() []error { return m }

// CheckEmploymentVerificationStatusRequestValidationError is the validation
// error returned by CheckEmploymentVerificationStatusRequest.Validate if the
// designated constraints aren't met.
type CheckEmploymentVerificationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEmploymentVerificationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEmploymentVerificationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEmploymentVerificationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEmploymentVerificationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEmploymentVerificationStatusRequestValidationError) ErrorName() string {
	return "CheckEmploymentVerificationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEmploymentVerificationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEmploymentVerificationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEmploymentVerificationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEmploymentVerificationStatusRequestValidationError{}

// Validate checks the field values on
// CheckEmploymentVerificationStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckEmploymentVerificationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckEmploymentVerificationStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckEmploymentVerificationStatusResponseMultiError, or nil if none found.
func (m *CheckEmploymentVerificationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEmploymentVerificationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEmploymentVerificationStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEmploymentVerificationStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEmploymentVerificationStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEmploymentVerificationStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEmploymentVerificationStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEmploymentVerificationStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextPollAfter

	// no validation rules for TransitionTitleText

	// no validation rules for TransitionSubtitleText

	// no validation rules for IconUrl

	if len(errors) > 0 {
		return CheckEmploymentVerificationStatusResponseMultiError(errors)
	}

	return nil
}

// CheckEmploymentVerificationStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// CheckEmploymentVerificationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckEmploymentVerificationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEmploymentVerificationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEmploymentVerificationStatusResponseMultiError) AllErrors() []error { return m }

// CheckEmploymentVerificationStatusResponseValidationError is the validation
// error returned by CheckEmploymentVerificationStatusResponse.Validate if the
// designated constraints aren't met.
type CheckEmploymentVerificationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEmploymentVerificationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEmploymentVerificationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEmploymentVerificationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEmploymentVerificationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEmploymentVerificationStatusResponseValidationError) ErrorName() string {
	return "CheckEmploymentVerificationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEmploymentVerificationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEmploymentVerificationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEmploymentVerificationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEmploymentVerificationStatusResponseValidationError{}

// Validate checks the field values on PersonalProfileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PersonalProfileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PersonalProfileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PersonalProfileInfoMultiError, or nil if none found.
func (m *PersonalProfileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PersonalProfileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return PersonalProfileInfoMultiError(errors)
	}

	return nil
}

// PersonalProfileInfoMultiError is an error wrapping multiple validation
// errors returned by PersonalProfileInfo.ValidateAll() if the designated
// constraints aren't met.
type PersonalProfileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PersonalProfileInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PersonalProfileInfoMultiError) AllErrors() []error { return m }

// PersonalProfileInfoValidationError is the validation error returned by
// PersonalProfileInfo.Validate if the designated constraints aren't met.
type PersonalProfileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PersonalProfileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PersonalProfileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PersonalProfileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PersonalProfileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PersonalProfileInfoValidationError) ErrorName() string {
	return "PersonalProfileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PersonalProfileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPersonalProfileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PersonalProfileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PersonalProfileInfoValidationError{}

// Validate checks the field values on CheckGmailVerificationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckGmailVerificationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckGmailVerificationStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckGmailVerificationStatusRequestMultiError, or nil if none found.
func (m *CheckGmailVerificationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckGmailVerificationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckGmailVerificationStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckGmailVerificationStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckGmailVerificationStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckGmailVerificationStatusRequestMultiError(errors)
	}

	return nil
}

// CheckGmailVerificationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// CheckGmailVerificationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckGmailVerificationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckGmailVerificationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckGmailVerificationStatusRequestMultiError) AllErrors() []error { return m }

// CheckGmailVerificationStatusRequestValidationError is the validation error
// returned by CheckGmailVerificationStatusRequest.Validate if the designated
// constraints aren't met.
type CheckGmailVerificationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckGmailVerificationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckGmailVerificationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckGmailVerificationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckGmailVerificationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckGmailVerificationStatusRequestValidationError) ErrorName() string {
	return "CheckGmailVerificationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckGmailVerificationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckGmailVerificationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckGmailVerificationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckGmailVerificationStatusRequestValidationError{}

// Validate checks the field values on CheckGmailVerificationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckGmailVerificationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckGmailVerificationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckGmailVerificationStatusResponseMultiError, or nil if none found.
func (m *CheckGmailVerificationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckGmailVerificationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckGmailVerificationStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckGmailVerificationStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckGmailVerificationStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextPollAfter

	// no validation rules for TransitionTitleText

	// no validation rules for TransitionSubtitleText

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckGmailVerificationStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckGmailVerificationStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckGmailVerificationStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckGmailVerificationStatusResponseMultiError(errors)
	}

	return nil
}

// CheckGmailVerificationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// CheckGmailVerificationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckGmailVerificationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckGmailVerificationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckGmailVerificationStatusResponseMultiError) AllErrors() []error { return m }

// CheckGmailVerificationStatusResponseValidationError is the validation error
// returned by CheckGmailVerificationStatusResponse.Validate if the designated
// constraints aren't met.
type CheckGmailVerificationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckGmailVerificationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckGmailVerificationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckGmailVerificationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckGmailVerificationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckGmailVerificationStatusResponseValidationError) ErrorName() string {
	return "CheckGmailVerificationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckGmailVerificationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckGmailVerificationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckGmailVerificationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckGmailVerificationStatusResponseValidationError{}

// Validate checks the field values on AnnualSalaryRange with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnnualSalaryRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualSalaryRange with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnnualSalaryRangeMultiError, or nil if none found.
func (m *AnnualSalaryRange) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualSalaryRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinValue

	// no validation rules for MaxValue

	// no validation rules for CurrencyCode

	if all {
		switch v := interface{}(m.GetAlternateDisplayed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnualSalaryRangeValidationError{
					field:  "AlternateDisplayed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnualSalaryRangeValidationError{
					field:  "AlternateDisplayed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlternateDisplayed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnualSalaryRangeValidationError{
				field:  "AlternateDisplayed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnnualSalaryRangeMultiError(errors)
	}

	return nil
}

// AnnualSalaryRangeMultiError is an error wrapping multiple validation errors
// returned by AnnualSalaryRange.ValidateAll() if the designated constraints
// aren't met.
type AnnualSalaryRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualSalaryRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualSalaryRangeMultiError) AllErrors() []error { return m }

// AnnualSalaryRangeValidationError is the validation error returned by
// AnnualSalaryRange.Validate if the designated constraints aren't met.
type AnnualSalaryRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualSalaryRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualSalaryRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualSalaryRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualSalaryRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualSalaryRangeValidationError) ErrorName() string {
	return "AnnualSalaryRangeValidationError"
}

// Error satisfies the builtin error interface
func (e AnnualSalaryRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualSalaryRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualSalaryRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualSalaryRangeValidationError{}

// Validate checks the field values on BusinessOwnerInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BusinessOwnerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BusinessOwnerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BusinessOwnerInfoMultiError, or nil if none found.
func (m *BusinessOwnerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BusinessOwnerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GstinNo

	if len(errors) > 0 {
		return BusinessOwnerInfoMultiError(errors)
	}

	return nil
}

// BusinessOwnerInfoMultiError is an error wrapping multiple validation errors
// returned by BusinessOwnerInfo.ValidateAll() if the designated constraints
// aren't met.
type BusinessOwnerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BusinessOwnerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BusinessOwnerInfoMultiError) AllErrors() []error { return m }

// BusinessOwnerInfoValidationError is the validation error returned by
// BusinessOwnerInfo.Validate if the designated constraints aren't met.
type BusinessOwnerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BusinessOwnerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BusinessOwnerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BusinessOwnerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BusinessOwnerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BusinessOwnerInfoValidationError) ErrorName() string {
	return "BusinessOwnerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e BusinessOwnerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBusinessOwnerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BusinessOwnerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BusinessOwnerInfoValidationError{}

// Validate checks the field values on EnrollmentNumber with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EnrollmentNumber) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrollmentNumber with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnrollmentNumberMultiError, or nil if none found.
func (m *EnrollmentNumber) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrollmentNumber) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EnrollmentNo

	if len(errors) > 0 {
		return EnrollmentNumberMultiError(errors)
	}

	return nil
}

// EnrollmentNumberMultiError is an error wrapping multiple validation errors
// returned by EnrollmentNumber.ValidateAll() if the designated constraints
// aren't met.
type EnrollmentNumberMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrollmentNumberMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrollmentNumberMultiError) AllErrors() []error { return m }

// EnrollmentNumberValidationError is the validation error returned by
// EnrollmentNumber.Validate if the designated constraints aren't met.
type EnrollmentNumberValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrollmentNumberValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrollmentNumberValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrollmentNumberValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrollmentNumberValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrollmentNumberValidationError) ErrorName() string { return "EnrollmentNumberValidationError" }

// Error satisfies the builtin error interface
func (e EnrollmentNumberValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrollmentNumber.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrollmentNumberValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrollmentNumberValidationError{}

// Validate checks the field values on StudentDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StudentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StudentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StudentDetailsMultiError,
// or nil if none found.
func (m *StudentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StudentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Year

	// no validation rules for MailId

	if len(errors) > 0 {
		return StudentDetailsMultiError(errors)
	}

	return nil
}

// StudentDetailsMultiError is an error wrapping multiple validation errors
// returned by StudentDetails.ValidateAll() if the designated constraints
// aren't met.
type StudentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StudentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StudentDetailsMultiError) AllErrors() []error { return m }

// StudentDetailsValidationError is the validation error returned by
// StudentDetails.Validate if the designated constraints aren't met.
type StudentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StudentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StudentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StudentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StudentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StudentDetailsValidationError) ErrorName() string { return "StudentDetailsValidationError" }

// Error satisfies the builtin error interface
func (e StudentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStudentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StudentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StudentDetailsValidationError{}

// Validate checks the field values on UpdateEmploymentDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEmploymentDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEmploymentDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEmploymentDataRequestMultiError, or nil if none found.
func (m *UpdateEmploymentDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEmploymentDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEmploymentDataRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEmploymentDataRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEmploymentDataRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmploymentType

	// no validation rules for EmploymentProofType

	if all {
		switch v := interface{}(m.GetAnnualSalaryRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEmploymentDataRequestValidationError{
					field:  "AnnualSalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEmploymentDataRequestValidationError{
					field:  "AnnualSalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualSalaryRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEmploymentDataRequestValidationError{
				field:  "AnnualSalaryRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnnualSalary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEmploymentDataRequestValidationError{
					field:  "AnnualSalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEmploymentDataRequestValidationError{
					field:  "AnnualSalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualSalary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEmploymentDataRequestValidationError{
				field:  "AnnualSalary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	// no validation rules for OccupationType

	switch v := m.Input.(type) {
	case *UpdateEmploymentDataRequest_CompanyInfo:
		if v == nil {
			err := UpdateEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCompanyInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "CompanyInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "CompanyInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompanyInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEmploymentDataRequestValidationError{
					field:  "CompanyInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateEmploymentDataRequest_EmployerInfo:
		if v == nil {
			err := UpdateEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmployerInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "EmployerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "EmployerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmployerInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEmploymentDataRequestValidationError{
					field:  "EmployerInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateEmploymentDataRequest_PersonalInfo:
		if v == nil {
			err := UpdateEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPersonalInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "PersonalInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "PersonalInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPersonalInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEmploymentDataRequestValidationError{
					field:  "PersonalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateEmploymentDataRequest_BusinessOwnerInfo:
		if v == nil {
			err := UpdateEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBusinessOwnerInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "BusinessOwnerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "BusinessOwnerInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBusinessOwnerInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEmploymentDataRequestValidationError{
					field:  "BusinessOwnerInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateEmploymentDataRequest_EnrollmentNumber:
		if v == nil {
			err := UpdateEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnrollmentNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "EnrollmentNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "EnrollmentNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnrollmentNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEmploymentDataRequestValidationError{
					field:  "EnrollmentNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateEmploymentDataRequest_StudentDetails:
		if v == nil {
			err := UpdateEmploymentDataRequestValidationError{
				field:  "Input",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStudentDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "StudentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEmploymentDataRequestValidationError{
						field:  "StudentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStudentDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEmploymentDataRequestValidationError{
					field:  "StudentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpdateEmploymentDataRequestMultiError(errors)
	}

	return nil
}

// UpdateEmploymentDataRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateEmploymentDataRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateEmploymentDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEmploymentDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEmploymentDataRequestMultiError) AllErrors() []error { return m }

// UpdateEmploymentDataRequestValidationError is the validation error returned
// by UpdateEmploymentDataRequest.Validate if the designated constraints
// aren't met.
type UpdateEmploymentDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEmploymentDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEmploymentDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEmploymentDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEmploymentDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEmploymentDataRequestValidationError) ErrorName() string {
	return "UpdateEmploymentDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEmploymentDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEmploymentDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEmploymentDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEmploymentDataRequestValidationError{}

// Validate checks the field values on AnnualSalary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnnualSalary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualSalary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnnualSalaryMultiError, or
// nil if none found.
func (m *AnnualSalary) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualSalary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnnualSalaryValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnnualSalaryValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnnualSalaryValidationError{
				field:  "Range",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Absolute

	if len(errors) > 0 {
		return AnnualSalaryMultiError(errors)
	}

	return nil
}

// AnnualSalaryMultiError is an error wrapping multiple validation errors
// returned by AnnualSalary.ValidateAll() if the designated constraints aren't met.
type AnnualSalaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualSalaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualSalaryMultiError) AllErrors() []error { return m }

// AnnualSalaryValidationError is the validation error returned by
// AnnualSalary.Validate if the designated constraints aren't met.
type AnnualSalaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualSalaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualSalaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualSalaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualSalaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualSalaryValidationError) ErrorName() string { return "AnnualSalaryValidationError" }

// Error satisfies the builtin error interface
func (e AnnualSalaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualSalary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualSalaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualSalaryValidationError{}

// Validate checks the field values on UpdateEmploymentDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEmploymentDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEmploymentDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEmploymentDataResponseMultiError, or nil if none found.
func (m *UpdateEmploymentDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEmploymentDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEmploymentDataResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEmploymentDataResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEmploymentDataResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEmploymentDataResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEmploymentDataResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEmploymentDataResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateEmploymentDataResponseMultiError(errors)
	}

	return nil
}

// UpdateEmploymentDataResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateEmploymentDataResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateEmploymentDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEmploymentDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEmploymentDataResponseMultiError) AllErrors() []error { return m }

// UpdateEmploymentDataResponseValidationError is the validation error returned
// by UpdateEmploymentDataResponse.Validate if the designated constraints
// aren't met.
type UpdateEmploymentDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEmploymentDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEmploymentDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEmploymentDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEmploymentDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEmploymentDataResponseValidationError) ErrorName() string {
	return "UpdateEmploymentDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEmploymentDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEmploymentDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEmploymentDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEmploymentDataResponseValidationError{}

// Validate checks the field values on GetScreenerChoiceScreenOptionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetScreenerChoiceScreenOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScreenerChoiceScreenOptionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetScreenerChoiceScreenOptionsRequestMultiError, or nil if none found.
func (m *GetScreenerChoiceScreenOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScreenerChoiceScreenOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreenerChoiceScreenOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreenerChoiceScreenOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreenerChoiceScreenOptionsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScreenerChoiceScreenOptionsRequestMultiError(errors)
	}

	return nil
}

// GetScreenerChoiceScreenOptionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetScreenerChoiceScreenOptionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetScreenerChoiceScreenOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScreenerChoiceScreenOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScreenerChoiceScreenOptionsRequestMultiError) AllErrors() []error { return m }

// GetScreenerChoiceScreenOptionsRequestValidationError is the validation error
// returned by GetScreenerChoiceScreenOptionsRequest.Validate if the
// designated constraints aren't met.
type GetScreenerChoiceScreenOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScreenerChoiceScreenOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScreenerChoiceScreenOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScreenerChoiceScreenOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScreenerChoiceScreenOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScreenerChoiceScreenOptionsRequestValidationError) ErrorName() string {
	return "GetScreenerChoiceScreenOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetScreenerChoiceScreenOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScreenerChoiceScreenOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScreenerChoiceScreenOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScreenerChoiceScreenOptionsRequestValidationError{}

// Validate checks the field values on GetScreenerChoiceScreenOptionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetScreenerChoiceScreenOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetScreenerChoiceScreenOptionsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetScreenerChoiceScreenOptionsResponseMultiError, or nil if none found.
func (m *GetScreenerChoiceScreenOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScreenerChoiceScreenOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreenerChoiceScreenOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreenerChoiceScreenOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreenerChoiceScreenOptionsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScreenerChoiceScreenOptionsResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScreenerChoiceScreenOptionsResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScreenerChoiceScreenOptionsResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScreenerChoiceScreenOptionsResponseMultiError(errors)
	}

	return nil
}

// GetScreenerChoiceScreenOptionsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetScreenerChoiceScreenOptionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetScreenerChoiceScreenOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScreenerChoiceScreenOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScreenerChoiceScreenOptionsResponseMultiError) AllErrors() []error { return m }

// GetScreenerChoiceScreenOptionsResponseValidationError is the validation
// error returned by GetScreenerChoiceScreenOptionsResponse.Validate if the
// designated constraints aren't met.
type GetScreenerChoiceScreenOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScreenerChoiceScreenOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScreenerChoiceScreenOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScreenerChoiceScreenOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScreenerChoiceScreenOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScreenerChoiceScreenOptionsResponseValidationError) ErrorName() string {
	return "GetScreenerChoiceScreenOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetScreenerChoiceScreenOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScreenerChoiceScreenOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScreenerChoiceScreenOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScreenerChoiceScreenOptionsResponseValidationError{}

// Validate checks the field values on AnnualSalaryRange_AlternateDisplayed
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AnnualSalaryRange_AlternateDisplayed) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnnualSalaryRange_AlternateDisplayed
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AnnualSalaryRange_AlternateDisplayedMultiError, or nil if none found.
func (m *AnnualSalaryRange_AlternateDisplayed) ValidateAll() error {
	return m.validate(true)
}

func (m *AnnualSalaryRange_AlternateDisplayed) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinVal

	// no validation rules for MaxVal

	// no validation rules for CurrencyCode

	if len(errors) > 0 {
		return AnnualSalaryRange_AlternateDisplayedMultiError(errors)
	}

	return nil
}

// AnnualSalaryRange_AlternateDisplayedMultiError is an error wrapping multiple
// validation errors returned by
// AnnualSalaryRange_AlternateDisplayed.ValidateAll() if the designated
// constraints aren't met.
type AnnualSalaryRange_AlternateDisplayedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnnualSalaryRange_AlternateDisplayedMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnnualSalaryRange_AlternateDisplayedMultiError) AllErrors() []error { return m }

// AnnualSalaryRange_AlternateDisplayedValidationError is the validation error
// returned by AnnualSalaryRange_AlternateDisplayed.Validate if the designated
// constraints aren't met.
type AnnualSalaryRange_AlternateDisplayedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnnualSalaryRange_AlternateDisplayedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnnualSalaryRange_AlternateDisplayedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnnualSalaryRange_AlternateDisplayedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnnualSalaryRange_AlternateDisplayedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnnualSalaryRange_AlternateDisplayedValidationError) ErrorName() string {
	return "AnnualSalaryRange_AlternateDisplayedValidationError"
}

// Error satisfies the builtin error interface
func (e AnnualSalaryRange_AlternateDisplayedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnnualSalaryRange_AlternateDisplayed.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnnualSalaryRange_AlternateDisplayedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnnualSalaryRange_AlternateDisplayedValidationError{}
