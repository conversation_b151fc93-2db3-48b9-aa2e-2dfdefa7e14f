// nolint: dupl
package stageproc

import (
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	colorPkg "github.com/epifi/be-common/pkg/colors"

	vkycDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/kyc/vkyc/events"

	"github.com/epifi/gamma/api/frontend/pay/transaction"
	formPkg "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/form"
	nrOnbPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/nr_onboarding"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	typeV2Form "github.com/epifi/gamma/api/typesv2/form"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"

	deeplink2 "github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error3 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/analyser"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	oafv2Pb "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/onboarding"
	appFeedbackPb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/auth"
	ffScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	feOnbPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	onPb "github.com/epifi/gamma/api/user/onboarding"
	vgPbCustomer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	kycPkg "github.com/epifi/gamma/pkg/kyc"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/helper"
)

var (
	debitCardFormToDeeplinkCardFormMap = map[card.CardForm]dlPb.DebitCardPinSetScreenOptions_CardForm{
		card.CardForm_CARD_FORM_UNSPECIFIED: dlPb.DebitCardPinSetScreenOptions_CARD_FORM_UNSPECIFIED,
		card.CardForm_PHYSICAL:              dlPb.DebitCardPinSetScreenOptions_PHYSICAL,
		card.CardForm_DIGITAL:               dlPb.DebitCardPinSetScreenOptions_DIGITAL,
	}
)

// Deeplink Generator Interface
type DeeplinkGenerator func() (*dlPb.Deeplink, error)

func NewPANNameCheckErrorDL(ctx context.Context, st onPb.OnboardingState) *dlPb.Deeplink {
	var (
		errOptsNew *error3.ErrorScreenOpts
	)

	switch st {
	case onPb.OnboardingState_FAILURE:
		errOptsNew = error3.OnbErrPANReject

	case onPb.OnboardingState_MANUAL_INTERVENTION:
		errOptsNew = error3.OnbErrPANMismatch

	default:
		logger.Error(ctx, fmt.Sprintf("unexpected state in pan name mismatch: %v", st))
		errOptsNew = error3.OnbErrPANReject
	}

	return deeplink2.NewErrorFullScreen(errOptsNew)
}

func NewCKYCFlagErrorDL(ctx context.Context, failureType kyc.FailureType) *dlPb.Deeplink {
	switch failureType {
	case kyc.FailureType_CKYC_L_FLAG:
		return deeplink2.NewErrorFullScreen(error3.OnbErrCKYCLFlag)
	case kyc.FailureType_CKYC_S_FLAG:
		return deeplink2.NewErrorFullScreen(error3.OnbErrCKYCSFlag)
	case kyc.FailureType_CKYC_O_FLAG:
		return deeplink2.NewErrorFullScreen(error3.OnbErrCKYCOFlag)
	default:
		logger.Error(ctx, "unhandled CKYC failure type")
		return deeplink2.NewErrorFullScreen(error3.OnbErrCKYCOFlag)
	}
}

// nolint: funlen
func StartEkycDLGen(ctx context.Context, inAppRefClient inAppReferralPb.InAppReferralClient, actorId string,
	cfg *genconf.OnboardingConfig, kycProc helper.IKYCHelper, feature onPb.Feature) DeeplinkGenerator {
	savingsLimit, _, _ := vkycPkg.GetMinKycUserBalanceLimits(timestamp.New(time.Now()))
	return func() (*dlPb.Deeplink, error) {
		if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, cfg.EKYCCertUpgradeFeatureConfig()) {
			logger.Info(ctx, "users app is not upgraded to the version supporting new UIDAI certs")
			return deeplink2.NewErrorFullScreen(error3.EKYCCertUpgradeError), nil
		}

		kycLevel, err := kycProc.GetKYCLevel(ctx, actorId)
		if err != nil {
			// Assuming min kyc in case of any error
			kycLevel = kyc.KYCLevel_MIN_KYC
		}

		ekycDL := &dlPb.Deeplink{
			Screen: dlPb.Screen_START_EKYC,
			ScreenOptions: &dlPb.Deeplink_StartEkycOptions{
				StartEkycOptions: &dlPb.StartEKYCOptions{
					KycLevel:    kycPkg.KycLevelMap[kycLevel],
					EkycSource:  kyc.EkycSource_EKYC_SOURCE_ONBOARDING.String(),
					NextAction:  deeplink2.NewActionToGetNextAction(),
					EkycFlow:    dlPb.StartEKYCOptions_EKYC_FLOW_ONBOARDING,
					OfferWidget: getOfferWidgetForOnbScreensViaFiniteCode(ctx, inAppRefClient, cfg, actorId),
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: "Use Aadhaar details to open an account",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
						},
						FontColor: "#333333",
					},
					Image: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/onboarding/aadhar_illustration.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Height: 240,
									Width:  240,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Description: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: fmt.Sprintf(constant.EKYCDescription, savingsLimit),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_S,
						},
						FontColor: "#878A8D",
					},
					ConsentText: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: constant.EKYCConsentText,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_XS,
						},
						FontColor: "#646464",
					},
					EkycDescriptionInfo: &dlPb.StartEKYCOptions_EKYCDescriptionInfo{
						BottomSheetTitle: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: "Minimum account details",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_3,
							},
							FontColor: "#333333",
						},
						BottomSheetDescription: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: fmt.Sprintf(constant.EKYCBottomSheetDesc, savingsLimit, savingsLimit),
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
							},
							FontColor: "#646464",
						},
					},
				},
			},
		}
		if feature == onPb.Feature_FEATURE_CC {
			ekycDL = &dlPb.Deeplink{
				Screen: dlPb.Screen_START_EKYC,
				ScreenOptions: &dlPb.Deeplink_StartEkycOptions{
					StartEkycOptions: &dlPb.StartEKYCOptions{
						KycLevel:    kycPkg.KycLevelMap[kycLevel],
						EkycSource:  kyc.EkycSource_EKYC_SOURCE_ONBOARDING.String(),
						NextAction:  deeplink2.NewActionToGetNextAction(),
						EkycFlow:    dlPb.StartEKYCOptions_EKYC_FLOW_ONBOARDING,
						OfferWidget: getOfferWidgetForOnbScreensViaFiniteCode(ctx, inAppRefClient, cfg, actorId),
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Provide your Aadhaar details to complete your KYC", "#333333", commontypes.FontStyle_SUBTITLE_1),
						Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/aadhar_illustration.png").WithProperties(&commontypes.VisualElementProperties{
							Width:  360,
							Height: 296,
						}).WithImageType(commontypes.ImageType_PNG),
						Description: commontypes.GetTextFromStringFontColourFontStyle("Our partner Federal Bank will complete your KYC and check serviceability using your Aadhaar. \nFi does not store or use your Aadhaar details.", "#878A8D", commontypes.FontStyle_BODY_S),
					},
				},
			}
		}
		if feature == onPb.Feature_FEATURE_PL {
			ekycDL = &dlPb.Deeplink{
				Screen: dlPb.Screen_START_EKYC,
				ScreenOptions: &dlPb.Deeplink_StartEkycOptions{
					StartEkycOptions: &dlPb.StartEKYCOptions{
						KycLevel:   kycPkg.KycLevelMap[kycLevel],
						EkycSource: kyc.EkycSource_EKYC_SOURCE_ONBOARDING.String(),
						NextAction: deeplink2.NewActionToGetNextAction(),
						EkycFlow:   dlPb.StartEKYCOptions_EKYC_FLOW_ONBOARDING,
						Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/aadhar_illustration.png").WithProperties(&commontypes.VisualElementProperties{
							Width:  360,
							Height: 296,
						}).WithImageType(commontypes.ImageType_PNG),
						// In case of PL feature, skip the ekyc info screen
						SkipEkycInfoScreen: true,
					},
				},
			}
		}

		return ekycDL, nil
	}
}

func StartCkycDLGen(ctx context.Context, kycClient kyc.KycClient, actorId string, userProc helper.UserProcessor) DeeplinkGenerator {
	return func() (*dlPb.Deeplink, error) {
		if !IsConsumerFlow(ctx) {
			logger.Info(ctx, "trying init ckyc from kyc liv completion")
			if err := tryInitCKYC(ctx, kycClient, actorId, userProc); err != nil {
				return nil, err
			}
		}
		return deeplink2.NewActionToGetNextAction(), nil
	}
}

func getKycType(ctx context.Context, kycClient kyc.KycClient, actorId string) (kyc.KycType, error) {
	kycResp, errResp := kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(kycResp, errResp); err != nil {
		logger.Error(ctx, "error while fetching kyc status", zap.Error(err))
		return kyc.KycType_KYC_TYPE_UNSPECIFIED, err
	}
	return kycResp.GetKycType(), nil
}

func getKycGender(ctx context.Context, kycClient kyc.KycClient, actorId string) (string, error) {
	kycRes, err := kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId:   actorId,
		SignImage: true,
	})
	if err = epifigrpc.RPCError(kycRes, err); err != nil {
		logger.Error(ctx, "error while fetching signed kyc record", zap.Error(err))
		return "", err
	}
	return kycRes.GetKycRecord().GetGender().String(), nil
}

// nolint: funlen
func ConfirmCardMailingAddressDLGen(ctx context.Context, bcClient bankcust.BankCustomerServiceClient, kycClient kyc.KycClient, actorId string, userId string, isNrUser bool, dynConf *genconf.OnboardingConfig) DeeplinkGenerator {
	return func() (*dlPb.Deeplink, error) {
		kycLevel, _ := getKYCLevel(ctx, bcClient, kycClient, actorId, userId)
		kycType, _ := getKycType(ctx, kycClient, actorId)
		screenParams := dynConf.PhysicalCardChargesMailingAddressScreenParams()
		if dynConf.Flags().EnableSaDeclarationstage() {
			kycGender, err := getKycGender(ctx, kycClient, actorId)
			if err != nil {
				logger.Error(ctx, "failed to get KYC gender", zap.Error(err))
				return nil, err
			}
			return deeplinkv3.GetDeeplinkV3WithoutError(dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS, &feOnbPb.ConfirmCardMailingAddressOptions{
				KycLevel:           kycPkg.KycLevelMap[kycLevel],
				PlaceHolderForName: "YOUR FULL NAME",
				Flow:               feOnbPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  constant.ConfirmCardMailingAddressIcon,
				},
				CheckboxTextColor: screenParams.CheckboxTextColor,
				PlaceHolderColor:  screenParams.PlaceholderColor,
				ContentColor:      screenParams.ContentColor,
				DividerColor:      screenParams.DividerColor,
				EditIconColor:     screenParams.EditIconColor,
				CardColor:         screenParams.CardColor,
				BackgroundColor:   screenParams.BackgroundColor,
				ScreenTitle:       commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingTitle, colorPkg.ColorDarkLayer2, commontypes.FontStyle_HEADLINE_L),
				ScreenSubtitle:    commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingSubTitle, colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_BODY_S),
				Cta: &dlPb.Cta{
					Type:         dlPb.Cta_CONTINUE,
					Text:         screenParams.CtaText,
					DisplayTheme: dlPb.Cta_PRIMARY,
					Status:       dlPb.Cta_CTA_STATUS_ENABLED,
				},
				AddressConfirmationMessage: "This name will be used in your debit card.",
				HideAddressField:           true,
				HeaderBar:                  deeplink.HeaderBarForFederalOwnedScreen(),
				DebitCardNameDescription:   commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingNameDescription, colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_BODY_S),
				Gender: &feOnbPb.ConfirmCardMailingAddressOptions_PlaceHolder{
					BgColor:         widget.GetBlockBackgroundColour("#FFFFFF"),
					BorderColor:     widget.GetBlockBackgroundColour("#FFFFFF"),
					Label:           commontypes.GetTextFromStringFontColourFontStyle("GENDER", colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_L),
					Value:           commontypes.GetTextFromStringFontColourFontStyle(kycGender, colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L),
					ExplanatoryText: commontypes.GetTextFromStringFontColourFontStyle("As per your KYC records", colorPkg.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S),
				},
				PurposeOfSavingsAccount: &feOnbPb.ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount{
					Title: commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingRadioTitle, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_S_CAPS),
					RadioOptions: []*feOnbPb.ConfirmCardMailingAddressOptions_RadioOption{
						{
							Options:                      commontypes.GetTextFromStringFontColourFontStyle("Savings", colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_HEADLINE_S),
							PurposeOfSavingsAccountValue: types.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_SAVINGS.String(),
						},
						{
							Options:                      commontypes.GetTextFromStringFontColourFontStyle("Loans", colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_HEADLINE_S),
							PurposeOfSavingsAccountValue: types.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_LOAN.String(),
						},
						{
							Options:                      commontypes.GetTextFromStringFontColourFontStyle("Investment", colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_HEADLINE_S),
							PurposeOfSavingsAccountValue: types.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_INVESTMENT.String(),
						},
					},
				},
				ConfirmBottomSheetHeader: &ui.IconTextComponent{
					LeftIcon: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/onboarding/usercheck",
						Width:     24,
						Height:    24,
					},
					Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Save and proceed?", colorPkg.ColorNight, commontypes.FontStyle_BODY_XS)},
					RightIcon:           nil,
					LeftImgTxtPadding:   0,
					RightImgTxtPadding:  0,
					Deeplink:            nil,
					ContainerProperties: nil,
					LeftVisualElement:   nil,
					RightVisualElement:  nil,
				},
			}), nil

		}

		switch kycType {
		case kyc.KycType_CKYC:
			return &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevel],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
						AddressConfirmationMessage: "This name will be used in your debit card.",
					},
				},
			}, nil
		default:
			return &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevel],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
					},
				},
			}, nil
		}
	}
}

func getFiLitePANDOBDropOffScreenOptions() *dlPb.FiLiteDropOffScreenOptions {
	return &dlPb.FiLiteDropOffScreenOptions{
		Title:        commontypes.GetTextFromHtmlStringFontColourFontStyle("Don't have your PAN card <br>details right now?", "#313234", commontypes.FontStyle_SUBTITLE_M),
		Description:  commontypes.GetTextFromStringFontColourFontStyle("You can still explore the app and all its benefits. When you're ready with your PAN card, you can set up your Savings Account.", "#606265", commontypes.FontStyle_BODY_S),
		BenefitTitle: commontypes.GetTextFromStringFontColourFontStyle("What you can do on Fi", "#606265", commontypes.FontStyle_SUBTITLE_S),
		BenefitsList: []*widget.ImageTitleSubtitleElement{
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_spends.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Get insights into your <br>other bank spends", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/credit_report_meter.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Learn how to improve<br>your credit score", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_link.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Track all your other bank<br>account balances & spends", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
		},
		CtaText:    commontypes.GetTextFromStringFontColourFontStyle("Continue to Fi", "#FFFFFF", commontypes.FontStyle_BUTTON_M),
		BottomText: commontypes.GetTextFromStringFontColourFontStyle("Retry entering PAN Details", "#00B899", commontypes.FontStyle_BUTTON_S),
		Source:     onPb.FiLiteSource_FI_LITE_SOURCE_PAN_DOB.String(),
	}
}

func getFiLiteScreenerDropOffScreenOptions() *dlPb.FiLiteDropOffScreenOptions {
	return &dlPb.FiLiteDropOffScreenOptions{
		Title:        commontypes.GetTextFromStringFontColourFontStyle("Having trouble verifying \nyour eligibility for Fi?", "#313234", commontypes.FontStyle_SUBTITLE_L),
		Description:  commontypes.GetTextFromStringFontColourFontStyle("You can still explore the app and all its benefits. When you're ready to verify your employment details, you can set up your Savings Account.", "#606265", commontypes.FontStyle_BODY_S),
		BenefitTitle: commontypes.GetTextFromStringFontColourFontStyle("What you can do on Fi", "#606265", commontypes.FontStyle_SUBTITLE_S),
		BenefitsList: []*widget.ImageTitleSubtitleElement{
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_spends.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Get insights into your <br>other bank spends", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/credit_report_meter.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Learn how to improve<br>your credit score", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_link.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Track all your other bank<br>account balances & spends", "#313234", commontypes.FontStyle_SUBTITLE_S)},
		},
		CtaText:    commontypes.GetTextFromStringFontColourFontStyle("Continue to Fi", "#FFFFFF", commontypes.FontStyle_BUTTON_M),
		BottomText: commontypes.GetTextFromStringFontColourFontStyle("Retry verifying eligibility", "#00B899", commontypes.FontStyle_BUTTON_S),
		Source:     onPb.FiLiteSource_FI_LITE_SOURCE_SCREENER.String(),
	}
}

// nolint:funlen
func NewPhonePermissionDl(ctx context.Context, showAttPromptIos, isContactPermEnabled, showWAConsent, showSmsPermission bool, ctaDeeplink *dlPb.Deeplink) (*dlPb.Deeplink, map[string]string) {
	var (
		notifPerm = &dlPb.PhonePermissionScreenOptions_PermissionItem{
			PermissionImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/notification_bell.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  52,
				Height: 52,
			}),
			PermissionTitle: commontypes.GetTextFromStringWithCustomFontStyle("Notification permission", "#333333", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
			PermissionSubtitle: commontypes.GetTextFromStringWithCustomFontStyle("To provide account creation updates & crucial personal finance reminders", "#A4A4A4", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
		}
		locPerm = &dlPb.PhonePermissionScreenOptions_PermissionItem{
			PermissionImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/location_pin.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  52,
				Height: 52,
			}),
			PermissionTitle: commontypes.GetTextFromStringWithCustomFontStyle("Location permission", "#333333", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
			PermissionSubtitle: commontypes.GetTextFromStringWithCustomFontStyle("To detect any unusual activity in your account and to keep it secure", "#A4A4A4", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
		}
		smsPerm = &dlPb.PhonePermissionScreenOptions_PermissionItem{
			PermissionImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/msg_bubble.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  52,
				Height: 52,
			}),
			PermissionTitle: commontypes.GetTextFromStringWithCustomFontStyle("SMS permission", "#333333", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
			PermissionSubtitle: commontypes.GetTextFromStringWithCustomFontStyle("To create your UPI ID & send you meaningful financial insights. Don't worry, your personal messages stay private", "#A4A4A4", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
		}
		contactPerm = &dlPb.PhonePermissionScreenOptions_PermissionItem{
			PermissionImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/contact.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  52,
				Height: 52,
			}),
			PermissionTitle: commontypes.GetTextFromStringWithCustomFontStyle("Contact permission", "#333333", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
			PermissionSubtitle: commontypes.GetTextFromStringWithCustomFontStyle("To sync contacts for payments and easily find your contacts to send money", "#A4A4A4", &commontypes.FontStyleInfo{
				FontFamily: "Inter",
				FontStyle:  "normal",
				FontSize:   "12",
			}),
		}
		analyticsEventProperties = make(map[string]string)
	)

	androidPerms := []*dlPb.PhonePermissionScreenOptions_PermissionItem{
		locPerm,
	}
	iosPerms := []*dlPb.PhonePermissionScreenOptions_PermissionItem{
		notifPerm, locPerm,
	}

	if showSmsPermission {
		androidPerms = append(androidPerms, smsPerm)
	}

	if isContactPermEnabled {
		androidPerms = append(androidPerms, contactPerm)
		iosPerms = append(iosPerms, contactPerm)
	}

	// Considering default to be android as there's a higher probability and erring on the side of caution with Android guidelines
	perms := androidPerms
	if epificontext.AppPlatformFromContext(ctx) == commontypes.Platform_IOS {
		perms = iosPerms
	}

	// Adding analytics properties for each permission
	for i, perm := range perms {
		analyticsEventProperties[fmt.Sprintf("permission_%d", i+1)] = perm.GetPermissionTitle().GetPlainString()
	}
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_PHONE_PERMISSION_SCREEN,
		ScreenOptions: &dlPb.Deeplink_WaConsentScreenOption{
			WaConsentScreenOption: &dlPb.PhonePermissionScreenOptions{
				ShowWaConsentOption: showWAConsent,
				WaConsentMessage:    constant.WAPreferenceMessage,
				ShowAttPromptIos:    showAttPromptIos,
				WAConsentTitle: commontypes.GetTextFromStringWithCustomFontStyle("Enable permissions", "#333333", &commontypes.FontStyleInfo{
					FontFamily: "Gilroy",
					FontStyle:  "normal",
					FontSize:   "20",
				}),
				WAConsentSubtitle: commontypes.GetTextFromStringWithCustomFontStyle("Know how each of these makes your savings account journey smoother and secured", "#A4A4A4", &commontypes.FontStyleInfo{
					FontFamily: "Inter",
					FontStyle:  "normal",
					FontSize:   "12",
				}),
				WAConsentPermissionItems: perms,
				WAConsentText: commontypes.GetTextFromStringWithCustomFontStyle("Get important updates on Whatsapp", "#333333", &commontypes.FontStyleInfo{
					FontFamily: "Inter",
					FontStyle:  "normal",
					FontSize:   "10",
				}),
				IsSmsPermissionMandatory: false,
				Cta: &dlPb.Cta{
					Type:     dlPb.Cta_CUSTOM,
					Text:     "Continue",
					Deeplink: ctaDeeplink,
				},
			},
		},
	}, analyticsEventProperties
}

func DeviceRegistrationDL(ctx context.Context, userClient userPb.UsersClient, actorId string, feat onPb.Feature) (*dlPb.Deeplink, error) {
	dl := &dlPb.Deeplink{
		Screen: dlPb.Screen_CREATE_ACCOUNT,
	}

	userResp, err := userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if grpcErr := epifigrpc.RPCError(userResp, err); grpcErr != nil {
		logger.Error(ctx, "error in get user", zap.Error(grpcErr))
		return nil, grpcErr
	}

	phNum := userResp.GetUser().GetProfile().GetPhoneNumber()
	phoneNum := strconv.FormatUint(phNum.GetNationalNumber(), 10)

	dl.ScreenOptions = &dlPb.Deeplink_CreateAccountScreenOptions{
		CreateAccountScreenOptions: &dlPb.CreateAccountScreenOptions{
			HeaderBar:          deeplink.HeaderBarForFederalOwnedScreen(),
			Title:              commontypes.GetTextFromStringFontColourFontStyle("You're at the home stretch", "#333333", commontypes.FontStyle_HEADLINE_XL),
			Subtitle:           commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Register your UPI ID and device. For this, an SMS will be sent from +%v %v to Federal Bank. Standard rates apply.", phNum.GetCountryCode(), phoneNum), "#A4A4A4", commontypes.FontStyle_BODY_S),
			TooltipDescription: commontypes.GetTextFromStringFontColourFontStyle("If you have 2 SIM cards, please make sure you pick the correct number to send an SMS from", "#333333", commontypes.FontStyle_BODY_XS),
			CtaText:            commontypes.GetTextFromStringFontColourFontStyle("Open account", "#FFFFFF", commontypes.FontStyle_BUTTON_1),
		},
	}

	if feat == onPb.Feature_FEATURE_CC {
		dl.ScreenOptions = &dlPb.Deeplink_CreateAccountScreenOptions{
			CreateAccountScreenOptions: &dlPb.CreateAccountScreenOptions{
				Title:              commontypes.GetTextFromStringFontColourFontStyle("You're at the home stretch", "#333333", commontypes.FontStyle_HEADLINE_XL),
				Subtitle:           commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Register your UPI ID and device. For this, an SMS will be sent from +%v %v to Federal Bank. Standard rates apply.", phNum.GetCountryCode(), phoneNum), "#A4A4A4", commontypes.FontStyle_BODY_S),
				TooltipDescription: commontypes.GetTextFromStringFontColourFontStyle("If you have 2 SIM cards, please make sure you pick the correct number to send an SMS from", "#333333", commontypes.FontStyle_BODY_XS),
				CtaText:            commontypes.GetTextFromStringFontColourFontStyle("Register device", "#FFFFFF", commontypes.FontStyle_BUTTON_M),
			},
		}
	}

	dl.ScreenOptionsV2 = deeplinkv3.GetScreenOptionV2WithoutError(getCreateAccountScreenOptionsV2(phNum, feat))
	return dl, nil
}

func getCreateAccountScreenOptionsV2(phoneNum *commontypes.PhoneNumber, feature onPb.Feature) *feOnbPb.CreateAccountScreenOptions {
	sc := &feOnbPb.CreateAccountScreenOptions{
		HeaderBar:          deeplink.HeaderBarForFederalOwnedScreen(),
		Title:              commontypes.GetTextFromStringFontColourFontStyle("You're at the home stretch", "#333333", commontypes.FontStyle_HEADLINE_XL),
		Subtitle:           commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("To register UPI ID & device, an SMS will be sent from +%v %v to Federal Bank. Standard rates apply.", phoneNum.GetCountryCode(), phoneNum.GetNationalNumber()), "#A4A4A4", commontypes.FontStyle_BODY_S),
		TooltipDescription: commontypes.GetTextFromStringFontColourFontStyle("If you have 2 SIM cards, please make sure you pick the correct number to send an SMS from", "#333333", commontypes.FontStyle_BODY_XS),
		Cta: &dlPb.Cta{
			Type: dlPb.Cta_CUSTOM,
			Text: "Open account",
			Deeplink: &dlPb.Deeplink{
				Screen: dlPb.Screen_REGISTER_DEVICE,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&auth.RegisterDeviceScreenOptions{
					HeaderBar: deeplink.HeaderBarForFederalOwnedScreen(),
					Source:    authPb.DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_FEDERAL_SAVINGS_ACCOUNT_ONBOARDING.String(),
				}),
			},
		},
		Image: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/home_stretch.png", 296, 360),
	}
	if feature == onPb.Feature_FEATURE_CC {
		sc.Subtitle = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("To register your device, an SMS will be sent from +91 %v to Federal Bank. Standard rates apply.", phoneNum), "#A4A4A4", commontypes.FontStyle_BODY_S)

	}
	if feature.IsNonResidentUserOnboarding() {
		sc.RemoveKycDetails = true
	}
	return sc
}

func NewDebitCardPinSetDeepLink(savings *savings.GetSavingsAccountEssentialsResponse, card *card.Card) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		ScreenOptions: &dlPb.Deeplink_DebitCardPinSetOptions{
			DebitCardPinSetOptions: &dlPb.DebitCardPinSetScreenOptions{
				AccountNumber:       savings.GetAccount().GetAccountNo(),
				AccountId:           savings.GetAccount().GetId(),
				CardId:              card.GetId(),
				CardNumber:          card.GetBasicInfo().GetCardNumber(),
				MaskedCardNumber:    card.GetBasicInfo().GetMaskedCardNumber(),
				Expiry:              card.GetBasicInfo().GetExpiry(),
				Cvv:                 card.GetBasicInfo().GetCvv(),
				Name:                card.GetBasicInfo().GetCustomerName(),
				PinSetTokenExpireAt: card.GetPinSetOtpToken().GetExpireAt(),
				CardForm:            debitCardFormToDeeplinkCardFormMap[card.GetForm()],
			},
		},
		Screen: dlPb.Screen_ONBOARDING_SET_DEBIT_CARD_PIN,
	}
}

func NewOnboardingVKYCStatusScreen(iconUrl, title, subTitle, ctaHeader, vkycNextAvailableStart string, ctaList []*dlPb.Cta) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ONBOARDING_VKYC_STATUS_SCREEN,
		ScreenOptions: &dlPb.Deeplink_OnboardingVkycStatusScreenOptions{
			OnboardingVkycStatusScreenOptions: &dlPb.OnboardingVKYCStatusScreenOptions{
				IconUrl:                iconUrl,
				Title:                  title,
				Subtitle:               subTitle,
				CtaList:                ctaList,
				CtaHeader:              ctaHeader,
				VkycNextAvailableStart: vkycNextAvailableStart,
			},
		},
	}
}

func NewVKYCIntroScreen(entryPoint vkyc.EntryPoint, vkycConfOption *genconf.VKYCOption, onb *onPb.OnboardingDetails) *dlPb.Deeplink {

	vkycNextActionDeeplink := &dlPb.Deeplink{
		Screen: dlPb.Screen_GET_VKYC_NEXT_ACTION_API,
		ScreenOptions: &dlPb.Deeplink_GetVkycNextActionApiScreenOptions{
			GetVkycNextActionApiScreenOptions: &dlPb.GetVKYCNextActionApiScreenOptions{
				ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
				EntryPoint:      entryPoint.String(),
				ShowCtaLoader:   true,
			},
		},
	}

	var skipOptions *dlPb.SkipCTAInfo
	if vkycConfOption.SkipOptionFlag() {
		skipOptions = &dlPb.SkipCTAInfo{
			ShowCta:             true,
			Stage:               onb.GetCurrentOnboardingStage().String(),
			VisibleAfterSeconds: 10,
		}
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_VKYC_INTRO,
		ScreenOptions: &dlPb.Deeplink_VkycIntroOptions{
			VkycIntroOptions: &dlPb.VKYCIntroOptions{
				Title:              constant.LandingPageTitle,
				Description:        constant.LandingPageDescription,
				BackgroundColorHex: constant.LandingPageBackgroundColorHex,
				Blocks: []*dlPb.LandingPageBlock{
					{
						Icon:                 constant.LandingPageBlockStarIcon,
						Text:                 constant.LandingPageBlockStarTitle,
						BackgroundColorHex:   constant.LandingPageBlockStarColorHex,
						LandingPageBlockType: dlPb.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
					{
						Icon:                 constant.LandingPageBlockDollarIcon,
						Text:                 constant.LandingPageBlockDollarTitle,
						BackgroundColorHex:   constant.LandingPageBlockDollarColorHex,
						LandingPageBlockType: dlPb.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
					{
						Icon:                 constant.LandingPageBlockRocketIcon,
						Text:                 constant.LandingPageBlockRocketTitle,
						BackgroundColorHex:   constant.LandingPageBlockRocketColorHex,
						LandingPageBlockType: dlPb.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
					{
						Icon:                 constant.LandingPageBlockThunderIcon,
						Text:                 constant.LandingPageBlockThunderTitle,
						BackgroundColorHex:   constant.LandingPageBlockThunderColorHex,
						LandingPageBlockType: dlPb.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
				},
				Ctas: []*dlPb.Cta{
					{
						Type:         dlPb.Cta_CUSTOM,
						Text:         constant.LandingPageCompleteCtaText,
						DisplayTheme: dlPb.Cta_PRIMARY,
						Deeplink:     vkycNextActionDeeplink,
					},
				},
				SkipCtaInfo: skipOptions,
			},
		},
	}
}

func VKYCIntroScreenV2(entryPoint vkyc.EntryPoint, vkycConfOption *genconf.VKYCOption, onb *onPb.OnboardingDetails) *dlPb.Deeplink {
	mainContentSection := &sections.VerticalListSection{
		Components: []*components.Component{
			// header bar
			{
				Content: GetAnyWithoutError(&sections.HorizontalListSection{
					VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
									WithPadding(0, 18, 0, 18),
							},
						},
					},
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(ui.NewITC().WithTexts(commontypes.GetPlainStringText(""))),
						},
						{
							Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/federal_bank_powered_by_fi.png", 40, 80)),
						},
						{
							Content: GetAnyWithoutError(ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 28, 28))),
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: GetAnyWithoutError(&dlPb.Deeplink{
												Screen: dlPb.Screen_FAQ_CATEGORY,
												ScreenOptions: &dlPb.Deeplink_FaqCategoryOptions{
													FaqCategoryOptions: &dlPb.FaqCategoryOptions{
														CategoryId: "***********",
													},
												},
											}),
										},
									},
									AnalyticsEvent: &analytics.AnalyticsEvent{
										EventName: events.EventVkycBenefitScreenClicked,
										Properties: map[string]string{
											events.PropertyEntryPoint: entryPoint.String(),
											events.PropertyViewName:   "help_icon",
										},
									},
								},
							},
						},
					},
				}),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XXL)),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Complete your Video KYC & unlock all benefits", "#313234", commontypes.FontStyle_HEADLINE_XL)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_S)),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Take a 3-min video call with our partner bank’s Relationship Manager", "#8D8D8D", commontypes.FontStyle_BODY_S)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_L)),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc_intro_screen_img.png", 380, 350)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XXXL)),
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
						WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
				},
			},
		},
		IsScrollable: true,
	}

	ctas := &sections.HorizontalListSection{
		VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
						WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
						WithBgColor(widget.GetBlockBackgroundColour("#00B899")).
						WithPadding(12, 12, 12, 12).
						WithAllCornerRadii(20, 20, 20, 20),
				},
			},
		},
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Complete Video KYC", "#FFFFFF", commontypes.FontStyle_BUTTON_M)),
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: GetAnyWithoutError(vkycPkg.GetVKYCNextActionDeeplink(&dlPb.GetVKYCNextActionApiScreenOptions{
							EntryPoint:      entryPoint.String(),
							ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
						})),
					},
				},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventVkycBenefitScreenClicked,
					Properties: map[string]string{
						events.PropertyEntryPoint: entryPoint.String(),
						events.PropertyViewName:   "primary_button",
					},
				},
			},
		},
	}

	if vkycConfOption.SkipOptionFlag() {
		ctas = &sections.HorizontalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(&sections.HorizontalListSection{
						VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
										WithWidthWeight(47).
										WithBgColor(widget.GetBlockBackgroundColour("#F6F9FD")).
										WithPadding(12, 12, 12, 12).
										WithAllCornerRadii(20, 20, 20, 20),
								},
							},
						},
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Skip", "#00B899", commontypes.FontStyle_BUTTON_M)),
							},
						},
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: GetAnyWithoutError(&dlPb.Deeplink{
											Screen: dlPb.Screen_SKIP_ONBOARDING_STAGE_API,
											ScreenOptions: &dlPb.Deeplink_SkipOnboardingStageApiOption{
												SkipOnboardingStageApiOption: &dlPb.SkipOnboardingStageApiOption{
													Stage: onb.GetCurrentOnboardingStage().String(),
												},
											},
										}),
									},
								},
								AnalyticsEvent: &analytics.AnalyticsEvent{
									EventName: events.EventVkycBenefitScreenClicked,
									Properties: map[string]string{
										events.PropertyEntryPoint: entryPoint.String(),
										events.PropertyViewName:   "skip_button",
									},
								},
							},
						},
					}),
				},
				{
					Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_M)),
				},
				{
					Content: GetAnyWithoutError(&sections.HorizontalListSection{
						VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
										WithWidthWeight(47).
										WithBgColor(widget.GetBlockBackgroundColour("#00B899")).
										WithPadding(12, 12, 12, 12).
										WithAllCornerRadii(20, 20, 20, 20),
								},
							},
						},
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Complete V-KYC", "#FFFFFF", commontypes.FontStyle_BUTTON_M)),
							},
						},
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: GetAnyWithoutError(vkycPkg.GetVKYCNextActionDeeplink(&dlPb.GetVKYCNextActionApiScreenOptions{
											EntryPoint:      entryPoint.String(),
											ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
										})),
									},
								},
								AnalyticsEvent: &analytics.AnalyticsEvent{
									EventName: events.EventVkycBenefitScreenClicked,
									Properties: map[string]string{
										events.PropertyEntryPoint: entryPoint.String(),
										events.PropertyViewName:   "primary_button",
									},
								},
							},
						},
					}),
				},
			},
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: properties.GetContainerProperty().
							WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
					},
				},
			},
		}

	}
	footorCtaSection := &sections.VerticalListSection{
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("8 lakh+ Fi users finished their V-KYC", "#648E4D", commontypes.FontStyle_SUBTITLE_XS)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XS)),
			},
			// Button
			{
				Content: GetAnyWithoutError(ctas),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().WithBgColor(widget.GetBlockBackgroundColour("#EEF2F6")),
				},
			},
		},
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_VKYC_INTRO_V2_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&vkycDlPb.VkycIntroV2ScreenOptions{
			ScreenDetails: &sections.Section{
				Content: &sections.Section_DepthWiseListSection{
					DepthWiseListSection: &sections.DepthWiseListSection{
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithBgColor(widget.GetBlockBackgroundColour("#EEF2F6")).
										WithPadding(16, 0, 16, 24).
										WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
										WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
								},
							},
						},
						Alignment: sections.DepthWiseListSection_BOTTOM_CENTER,
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(mainContentSection),
							},
							{
								Content: GetAnyWithoutError(footorCtaSection),
							},
						},
						LoadBehavior: &behaviors.LifecycleBehavior{
							Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
							AnalyticsEvent: &analytics.AnalyticsEvent{
								EventName: events.EventVkycBenefitScreenLoaded,
								Properties: map[string]string{
									events.PropertyEntryPoint: entryPoint.String(),
								},
							},
						},
					},
				},
			},
		}),
	}
}

// dedupe error deeplinks are used at KYC_DEDUPE_CHECK and PRE_CUSTOMER_CREATION_DEDUPE_CHECK stages
// nolint: funlen
func getDedupeErrorDL(dedupeStatus vgPbCustomer.DedupeStatus) *dlPb.Deeplink {
	deeplinkData, dataExists := error3.DedupeStatusToDeeplinkDataMapping[dedupeStatus]
	if !dataExists { // Handling in case of error
		deeplinkData = &error3.ErrorScreenOpts{
			Title:          constant.GenericDedupeCheckFailure,
			HasChat:        false,
			HasCall:        false,
			HasFeedback:    true,
			Category:       appFeedbackPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
			HeaderImageURL: constant.DedupeFailureImageURL,
		}
	}
	dedupeFailureImage := constant.DedupeFailureImageURL
	if deeplinkData.HeaderImageURL != "" {
		dedupeFailureImage = deeplinkData.HeaderImageURL
	}
	opts := &error3.ErrorScreenOpts{
		Title:          deeplinkData.Title,
		HasChat:        deeplinkData.HasChat,
		HasCall:        deeplinkData.HasCall,
		Subtitle:       deeplinkData.Subtitle,
		HasFeedback:    deeplinkData.HasFeedback,
		Category:       deeplinkData.Category,
		HeaderImageURL: dedupeFailureImage,
	}
	return deeplink2.NewErrorFullScreen(opts)
}

func NewNextToDoScreen(ctaText string, nextAction *dlPb.Deeplink) *dlPb.Deeplink {

	var ctas []*dlPb.Cta

	ctas = append(ctas, &dlPb.Cta{
		DisplayTheme: dlPb.Cta_DISPLAY_THEME_DELAY_ENABLE,
		Text:         ctaText,
		Deeplink:     nextAction,
		Type:         dlPb.Cta_CUSTOM,
	})

	screenContent := dlPb.InfoAcknowledgementScreenOptions_ScreenContentTheme2{
		Title:    "Here’s what you can do on Fi app 🎉",
		Subtitle: "Your one app for all things money",
		ContentList: []*dlPb.InfoAcknowledgementScreenOptions_ScreenContentTheme2_ContentObject{
			{
				ContentText: "<b>Add funds</b> to unlock a bunch of cool features on the app",
				IconUrl:     "https://epifi-icons.pointz.in/onboarding/money_bag.png",
			},
			{
				ContentText: "<b>Automate saving & investing</b> to grow your money with FIT Rules",
				IconUrl:     "https://epifi-icons.pointz.in/onboarding/wand.png",
			},
			{
				ContentText: "<b>Earn Fi coins & cash rewards</b> as you master your money",
				IconUrl:     "https://epifi-icons.pointz.in/onboarding/trophy.png",
			},
			{
				ContentText: "Activate & spend with your card to get <b>exclusive offers</b> ",
				IconUrl:     "https://epifi-icons.pointz.in/onboarding/debit_card.png",
			},
		},
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_INFO_ACKNOWLEDGEMENT_SCREEN,
		ScreenOptions: &dlPb.Deeplink_InfoAcknowledgementScreenOptions{
			InfoAcknowledgementScreenOptions: &dlPb.InfoAcknowledgementScreenOptions{
				Ctas:      ctas,
				CtaHeader: "",
				ScreenContent: &dlPb.InfoAcknowledgementScreenOptions_ScreenContentTheme2_{
					ScreenContentTheme2: &screenContent,
				},
				ScreenTheme: dlPb.InfoAcknowledgementScreenOptions_SCREEN_THEME_2,
			},
		},
	}
}

func getOnboardingAddMoneyDl(disableSkipCta bool) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ONBOARDING_ADD_MONEY,
		ScreenOptions: &dlPb.Deeplink_OnboardingAddMoneyScreenOptions{
			OnboardingAddMoneyScreenOptions: &dlPb.OnboardingAddMoneyScreenOptions{
				DisableSkipButton: disableSkipCta,
			},
		},
	}
}

func getOnboardingAddFundsV2Dl(ctx context.Context, actorId string, minorVersion int, disableSkipCta bool, uiEntryPoint transaction.UIEntryPoint) *dlPb.Deeplink {
	// TODO(Sainath): remove once stable in production
	logger.Info(ctx, "onb add funds version", zap.Int(logger.VERSION_ID, minorVersion), zap.String(logger.ACTOR_ID_V2, actorId))
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ONBOARDING_ADD_FUNDS_V2,
		ScreenOptions: &dlPb.Deeplink_OnboardingAddFundsV2ScreenOptions{
			OnboardingAddFundsV2ScreenOptions: &dlPb.OnboardingAddFundsV2ScreenOptions{
				DisableSkipButton: disableSkipCta,
				// passing this as true by default as client has the handling to prioritise skip-cta over this flag.
				// Thus, if `DisableSkipCta` is false, skip CTA will be shown instead of "I've already added money" cta
				ShowAccountBalanceCheckCtaOnLandingPage: getShowAccBalCheckCtaFlag(uiEntryPoint),
				MinorVersion:                            getMinorVersionStringForOnbAddFundsV2(minorVersion),
				UiEntryPoint:                            uiEntryPoint.String(),
			},
		},
	}
}

func getShowAccBalCheckCtaFlag(uiEntryPoint transaction.UIEntryPoint) bool {
	if uiEntryPoint == transaction.UIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS_PRE_FUNDING {
		return false
	}

	return true
}

// nolint: funlen
func getSaIntroScreen(intentSelectionDL *dlPb.Deeplink) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ONBOARDING_SAVINGS_ACCOUNT_INTRO,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&feOnbPb.SaIntroScreenOptions{
			ConsentTypes: []dlPb.ConsentTypeUrl_ConsentType{
				dlPb.ConsentTypeUrl_FED_TNC,
			},
			BackButton: &dlPb.BackAction{
				ShowButton: commontypes.BooleanEnum_TRUE,
				Deeplink:   intentSelectionDL,
			},
			LeftImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/green_fi.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  35,
				Height: 30,
			}).WithImageType(commontypes.ImageType_PNG),
			RightImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/federal_wide.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  114,
				Height: 29,
			}).WithImageType(commontypes.ImageType_PNG),
			TopImage: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/green_fi.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  35,
				Height: 30,
			}).WithImageType(commontypes.ImageType_PNG),
			TitleComponent: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Trusted by over 35,00,000+ Indians", "#28292B", commontypes.FontStyle_SUBTITLE_XS),
				},
				LeftImgTxtPadding: 4,
				LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/sav_intro_top_icon.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  26,
					Height: 16,
				}).WithImageType(commontypes.ImageType_PNG),
			},
			InformationGrid: &feOnbPb.SaIntroScreenOptions_SectionTypeGrid{
				SectionTitle: commontypes.GetTextFromStringFontColourFontStyle("Smarter Banking", "#6294A6", commontypes.FontStyle_DISPLAY_XL),
				InformationBlocks: []*ui.VerticalIconTextComponent{
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/debit_card_backside.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("International\nDebit Card", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/vault_with_wheel.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Fixed\nDeposits", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SA-US-Stocks.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("US Stocks", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/two_coins.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Upto 3% back\non Spends*", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/moneybag.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Instant\nLoans", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#E4F1F5",
					},
				},
				NumOfRows: 2,
				NumOfCols: 3,
				PartnerLogos: []*commontypes.VisualElement{
					commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/federal_wide.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  70,
						Height: 18,
					}).WithImageType(commontypes.ImageType_PNG),
					commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/visa_logo.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  37,
						Height: 12,
					}).WithImageType(commontypes.ImageType_PNG),
				},
			},
			InformationCarousel: &feOnbPb.SectionTypeCarousel{
				SectionTitle: commontypes.GetTextFromStringFontColourFontStyle("More\n on Fi", "#648E4D", commontypes.FontStyle_DISPLAY_XL),
				BenefitListItems: []*ui.VerticalIconTextComponent{
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/gold_bowl.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("0 Commission\n Mutual Funds", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/green_hook.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Track all your\n bank accounts", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/blue_bar_graph.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Portfolio\n Analysis", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/gold_pie_chart.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("AI-powered\n Insights", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/pink_bell.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Reminders &\n Budgeting", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/salary_star.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Salary\nProgramme", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#EDF5EB",
					},
				},
			},
			BankTnc: commontypes.GetTextFromHtmlStringFontColourFontStyle("By proceeding you agree to open a Federal Bank Savings Account\n and agree to <a href='https://www.federalbank.co.in/epifi-tandc#CASA'><font color=#00B899>Federal Bank’s T&C</font></a>.", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
			PrimaryCta: &dlPb.Cta{
				Type:         dlPb.Cta_CUSTOM,
				Text:         "Get a Savings Account",
				DisplayTheme: dlPb.Cta_PRIMARY,
			},
			BottomText: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Explore All Fi Products", "#00B899", commontypes.FontStyle_SUBTITLE_S),
				},
				LeftImgTxtPadding: 8,
				LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/sav_intro_bot_icon.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  42,
					Height: 20,
				}).WithImageType(commontypes.ImageType_PNG),
				Deeplink: intentSelectionDL,
			},
			StaticConsents: getStaticConsents(),
		}),
	}
}

// function to get minor version string of add funds v2 page from integer minor version
func getMinorVersionStringForOnbAddFundsV2(minorVersion int) string {
	switch minorVersion {
	case 0:
		return oafv2Pb.OnboardingAddFundsV2MinorVersion_ZERO.String()
	case 1:
		return oafv2Pb.OnboardingAddFundsV2MinorVersion_ONE.String()
	case 2:
		return oafv2Pb.OnboardingAddFundsV2MinorVersion_TWO.String()
	case 3:
		return oafv2Pb.OnboardingAddFundsV2MinorVersion_THREE.String()
	}
	return oafv2Pb.OnboardingAddFundsV2MinorVersion_ZERO.String()
}

// function to check whether the onboarding add funds v2 page is v2.1 or not
// True only if X > 0
func isOnboardingAddFundsV2_X(ctx context.Context, dl *dlPb.Deeplink) bool {
	if dl.GetScreen() != dlPb.Screen_ONBOARDING_ADD_FUNDS_V2 {
		logger.WarnWithCtx(ctx, "screen in deeplink is not onboarding add funds v2, returning false", zap.Any(logger.SCREEN, dl.GetScreen()))
		return false
	}
	return dl.GetOnboardingAddFundsV2ScreenOptions().GetMinorVersion() == oafv2Pb.OnboardingAddFundsV2MinorVersion_ONE.String() ||
		dl.GetOnboardingAddFundsV2ScreenOptions().GetMinorVersion() == oafv2Pb.OnboardingAddFundsV2MinorVersion_TWO.String()
}

// nolint:funlen
func getCreditCardUserIneligibleToOnboardScreen(intentDl *dlPb.Deeplink) *dlPb.Deeplink {
	screenOptions := &ffScreenTypes.CcIneligibleUserScreenOptions{
		Header: nil,
		TopSectionDetails: &dlPb.InfoItemV3{
			Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/black_yellow_card_4x.png").
				WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  115,
				Height: 85,
			}),
			Title: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "You’re not eligible for a <br>Credit Card at the moment"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
				},
			},
			Desc: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_Html{Html: "We will let you know once you are eligible"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_S,
				},
			},
		},
		SeparatorVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/user_ineligible_separator_4x.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  40,
			Height: 4,
		}),
		BottomSectionHeading: &commontypes.Text{
			FontColor:    "#F6F9FD",
			DisplayValue: &commontypes.Text_Html{Html: "Here are some factors that can <br>help improve eligibility"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
			},
		},
		IneligibleUserDetails: []*ffScreenTypes.IneligibleUserDetails{
			{
				BottomSectionDetails: &dlPb.InfoItemWithCtaV3{
					Info: &dlPb.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/credit_score_meter_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Good credit score"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "A good credit score can help you get a<br> credit card easily"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
					Cta: &dlPb.Cta{
						Type: dlPb.Cta_CUSTOM,
						Deeplink: &dlPb.Deeplink{
							Screen: dlPb.Screen_ANALYSER_SCREEN,
							ScreenOptions: &dlPb.Deeplink_AnalyserScreenOptions{
								AnalyserScreenOptions: &dlPb.AnalyserScreenOptions{
									AnalyserName: analyser.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
								},
							},
						},
						DisplayTheme: dlPb.Cta_SECONDARY,
						Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						Text:         "Check your credit score",
						IconUrl:      "https://epifi-icons.pointz.in/credit_card_images/chevron_right_green_4x.png",
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &dlPb.InfoItemWithCtaV3{
					Info: &dlPb.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/timely_repayments_green_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Timely repayments"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "Ensure timely credit card & loan<br> repayments"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &dlPb.InfoItemWithCtaV3{
					Info: &dlPb.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/green_download_arrow_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Reduce hard enquiries"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "Avoid multiple credit enquiries in a<br> short period of time"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &dlPb.InfoItemWithCtaV3{
					Info: &dlPb.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/red_clock_frame_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Long credit age"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "A long & healthy credit history<br> improves credit card approval chances"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
		},
		FooterText: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_Html{Html: "Note: This list is not exhaustive but covers <br>some common factors."},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		MainCta: &dlPb.Cta{
			Type:         dlPb.Cta_CUSTOM,
			Text:         "Explore other products",
			Deeplink:     intentDl,
			DisplayTheme: dlPb.Cta_PRIMARY,
			Status:       dlPb.Cta_CTA_STATUS_ENABLED,
		},
	}

	return &dlPb.Deeplink{
		Screen:          dlPb.Screen_CC_INELIGIBLE_USER_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(screenOptions),
	}
}

// hide learn more snippet for if NRO consent flow is enabled, show either learn more snippet or nro consent
func getGetLearnMoreSnippet(isNROConsentEnabled bool) *feOnbPb.ClickableSnippet {
	if isNROConsentEnabled {
		return nil
	}
	return &feOnbPb.ClickableSnippet{
		NonClickableText: commontypes.GetTextFromStringFontColourFontStyle("Want to open a NRO account? ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
		ClickableText: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Learn more", "#00B899", commontypes.FontStyle_SUBTITLE_XS),
			},
			Deeplink: &dlPb.Deeplink{
				Screen: dlPb.Screen_GENERIC_BOTTOM_SHEET,
				ScreenOptions: &dlPb.Deeplink_GenericBottomSheetScreenOptions{
					GenericBottomSheetScreenOptions: &dlPb.GenericBottomSheetScreenOptions{
						Title: commontypes.GetTextFromStringFontColourFontStyle("NRO Accounts arriving soon!", "#313234", commontypes.FontStyle_HEADLINE_L),
						Subtitle: commontypes.GetTextFromStringFontColourFontStyle("We're working with our partner bank, Federal Bank, to make this available soon.",
							"#606265", commontypes.FontStyle_BODY_S),
						Ctas: []*dlPb.Cta{
							{
								Type: dlPb.Cta_DONE,
								Text: "Ok, got it",
							},
						},
					},
				},
			},
		},
	}
}

func getNroAccountCreationConsents(isNROConsentEnabled bool) []*feOnbPb.CheckboxConsent {
	if !isNROConsentEnabled {
		return nil
	}
	return []*feOnbPb.CheckboxConsent{{
		ConsentIds: []string{consent.ConsentType_CONSENT_NRO_ACCOUNT_CREATION.String()},
		IsChecked:  false,
		Text: ui.NewITC().
			WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle("Want to open a NRO account?  <font color=#00B899>Learn more</font>", "#313234", commontypes.FontStyle_SUBTITLE_XS)).
			WithDeeplink(&dlPb.Deeplink{
				Screen: dlPb.Screen_GENERIC_BOTTOM_SHEET,
				ScreenOptions: &dlPb.Deeplink_GenericBottomSheetScreenOptions{
					GenericBottomSheetScreenOptions: &dlPb.GenericBottomSheetScreenOptions{
						Title: commontypes.GetTextFromStringFontColourFontStyle("Get a NRO account along with \nyour NRE account application", "#313234", commontypes.FontStyle_HEADLINE_L),
						Subtitle: commontypes.GetTextFromStringFontColourFontStyle("No additional process required.",
							"#606265", commontypes.FontStyle_BODY_S),
						Ctas: []*dlPb.Cta{
							{
								Type: dlPb.Cta_DONE,
								Text: "Ok, got it",
							},
						},
					},
				},
			}),
	}}
}

func getNrStaticConsents() []*feOnbPb.StaticConsent {
	return []*feOnbPb.StaticConsent{{
		ConsentIds: []string{consent.ConsentType_FED_TNC.String()},
		Text:       ui.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle("By proceeding you agree to open a Federal Bank Savings Account and agree to <a href='https://www.federalbank.co.in/epifi-tandc#CASA'><font color=#00B899>Federal Bank’s T&C</font></a>.", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS)),
	}}
}

func getStaticConsents() []*feOnbPb.StaticConsent {
	return []*feOnbPb.StaticConsent{{
		ConsentIds: []string{consent.ConsentType_FED_TNC.String(), consent.ConsentType_CONSENT_INDIAN_RESIDENCY.String()},
		Text:       ui.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle("By proceeding, I confirm that I am an Indian citizen, India is my country of tax residence, I am not (nor am I related to) a politically exposed person and I agree to open a Federal Bank Savings Account in accordance with <a href='https://www.federalbank.co.in/epifi-tandc#CASA'><font color=#00B899>Federal Bank’s T&C</font></a>.", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS)),
	}}
}

// nolint: funlen
func getSaIntroScreenForNr(isNROConsentEnabled bool) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ONBOARDING_SAVINGS_ACCOUNT_INTRO,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&feOnbPb.SaIntroScreenOptions{
			ConsentTypes: []dlPb.ConsentTypeUrl_ConsentType{
				dlPb.ConsentTypeUrl_FED_TNC,
			},
			HeaderBar: deeplink.HeaderBarForFederalOwnedScreen(),
			TitleComponent: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Open an NRE Account with\nFederal Bank", "#313234", commontypes.FontStyle_HEADLINE_L).
						WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
				},
			},
			BackButton: &dlPb.BackAction{
				ShowButton: commontypes.BooleanEnum_FALSE,
			},
			InformationGrid: &feOnbPb.SaIntroScreenOptions_SectionTypeGrid{
				SectionTitle: commontypes.GetTextFromStringFontColourFontStyle("Smarter Banking", "#6294A6", commontypes.FontStyle_DISPLAY_L),
				InformationBlocks: []*ui.VerticalIconTextComponent{
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/debit_card_backside.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("VISA Platinum\n Card*", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/UPI-1.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("UPI Payments", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/blue_bank.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("NRE Account", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#E4F1F5",
					},
				},
				NumOfRows: 1,
				NumOfCols: 3,
				PartnerLogos: []*commontypes.VisualElement{
					commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/federal_wide.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  70,
						Height: 18,
					}).WithImageType(commontypes.ImageType_PNG),
					commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/visa_logo.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  37,
						Height: 12,
					}).WithImageType(commontypes.ImageType_PNG),
				},
			},
			InformationCarousel: &feOnbPb.SectionTypeCarousel{
				SectionTitle: commontypes.GetTextFromStringFontColourFontStyle("Coming\nSoon", "#648E4D", commontypes.FontStyle_DISPLAY_L),
				BenefitListItems: []*ui.VerticalIconTextComponent{
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/smart_deposits_1.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Smart\nDeposits", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/vault_with_wheel.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Fixed\nDeposits", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
					{
						TopIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/spend_analyser.png").WithHeight(52).WithWidth(52),
						TopImgTxtPadding: 8,
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("AI-powered\n Insights", "#86BA6F", commontypes.FontStyle_SUBTITLE_2XS),
						},
						ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
							Height: 84,
							Width:  84,
						},
					},
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#EDF5EB",
					},
				},
			},
			LearnMoreSnippet: getGetLearnMoreSnippet(isNROConsentEnabled),
			CheckboxConsents: getNroAccountCreationConsents(isNROConsentEnabled),
			BankTnc:          commontypes.GetTextFromHtmlStringFontColourFontStyle("By proceeding you agree to open a Federal Bank Savings Account\n and agree to <a href='https://www.federalbank.co.in/epifi-tandc#CASA'><font color=#00B899>Federal Bank’s T&C</font></a>.", "#6294A6", commontypes.FontStyle_SUBTITLE_2XS),
			PrimaryCta: &dlPb.Cta{
				Type:         dlPb.Cta_CUSTOM,
				Text:         "Swipe to get started",
				DisplayTheme: dlPb.Cta_PRIMARY,
			},
			StaticConsents: getNrStaticConsents(),
		}),
	}
}

// getWaPANDOBDropOffScreenOptions returns wealth analyser screen options for PAN DOB drop off screen
func getWaPANDOBDropOffScreenOptions() *dlPb.FiLiteDropOffScreenOptions {
	return &dlPb.FiLiteDropOffScreenOptions{
		Title:        commontypes.GetTextFromHtmlStringFontColourFontStyle("Not interested in savings account?\nThere’s more to explore on Fi", "#313234", commontypes.FontStyle_SUBTITLE_L),
		Description:  commontypes.GetTextFromStringFontColourFontStyle("You can open a savings account later as well. Meanwhile, find out how Fi can help grow your wealth in 2 mins", "#606265", commontypes.FontStyle_BODY_S),
		BenefitTitle: commontypes.GetTextFromStringFontColourFontStyle("Insights no other app can give you", "#606265", commontypes.FontStyle_SUBTITLE_S),
		BenefitsList: []*widget.ImageTitleSubtitleElement{
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/wealthanalyser/epf.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Track all your money in one place– Mutual Funds, EPFs, Stocks & more", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/money_mistake_icon.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Avoid costly mistakes with 100+ personalised insights", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
			{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/wealthanalyser/ai_robot.png",
					Width:    50,
					Height:   50,
				},
				SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("AI-powered predictions of your wealth in 10 years & when you’ll hit 1 Crore", "#313234", commontypes.FontStyle_SUBTITLE_S),
			},
		},
		CtaText:    commontypes.GetTextFromStringFontColourFontStyle("Get insights in 2 mins", "#FFFFFF", commontypes.FontStyle_BUTTON_M),
		BottomText: commontypes.GetTextFromStringFontColourFontStyle("Continue opening savings a/c", "#00B899", commontypes.FontStyle_BUTTON_S),
		Source:     onPb.FiLiteSource_FI_LITE_SOURCE_PAN_DOB.String(),
	}
}

func GetAnyWithoutError(msg proto.Message) *anypb.Any {
	res, _ := anypb.New(msg)
	return res
}

func GetGlobalIssuedPassportDataVerificationScreenDeeplink(isPassportArnFlowEnable bool, alterOptionDl *dlPb.Deeplink) *dlPb.Deeplink {
	globalIssuedPassportARNInstruction := []string{
		"On the confirmation email or SMS sent by Passport Seva",
		"On any receipts or acknowledgement you received during application",
	}

	oldPassportFileNumInstruction := []string{
		"It’s available on the last page of the Passport",
		"On the confirmation email or SMS sent by Passport Seva",
		"On any receipts or acknowledgement you received during application",
	}

	inputFieldBlock := make([]*nrOnbPb.GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock, 0)

	if isPassportArnFlowEnable {
		inputFieldBlock = append(inputFieldBlock, &nrOnbPb.GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock{
			BackgroundColor: widget.GetBlockBackgroundColour("#F6F9FD"),
			BlockTitle:      commontypes.GetTextFromStringFontColourFontStyle("Enter current passport’s application reference number", "#6A6D70", commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT),
			InputFieldHint:  commontypes.GetTextFromStringFontColourFontStyle("Application reference number", "#B2B5B9", commontypes.FontStyle_SUBTITLE_S),
			Footer: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Where to find it?", "#929599", commontypes.FontStyle_BODY_XS)).
				WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/help_circle.png", 24, 24)).
				WithRightImagePadding(10).WithDeeplink(GetPassportHelpBottomSheet("Multiple ways to find your ‘Application Reference number’?", globalIssuedPassportARNInstruction, "21-2001884998")),
			FieldId: typeV2Form.FieldIdentifier_FIELD_IDENTIFIER_APPLICATION_REFERENCE_NUMBER.String(),
		})
	}

	inputFieldBlock = append(inputFieldBlock, &nrOnbPb.GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock{
		BackgroundColor: widget.GetBlockBackgroundColour("#F6F9FD"),
		BlockTitle:      commontypes.GetTextFromStringFontColourFontStyle("Enter previous passport’s file number", "#6A6D70", commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT),
		InputFieldHint:  commontypes.GetTextFromStringFontColourFontStyle("Previous passport file number", "#B2B5B9", commontypes.FontStyle_SUBTITLE_S),
		Footer: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Where to find it?", "#929599", commontypes.FontStyle_BODY_XS)).
			WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/help_circle.png", 24, 24)).
			WithRightImagePadding(10).WithDeeplink(GetPassportHelpBottomSheet("Multiple ways to find your ‘File number’?", oldPassportFileNumInstruction, "GUW00014381944")),
		FieldId: typeV2Form.FieldIdentifier_FIELD_IDENTIFIER_PASSPORT_FILE_NUMBER.String(),
	})

	ctas := []*dlPb.Cta{
		{
			DisplayTheme: dlPb.Cta_PRIMARY,
			Type:         dlPb.Cta_CUSTOM,
			Text:         "Submit",
			Deeplink:     deeplink2.NewActionToGetNextAction(),
		},
	}

	if alterOptionDl != nil {
		ctas = append(ctas, &dlPb.Cta{
			DisplayTheme: dlPb.Cta_TERTIARY,
			Type:         dlPb.Cta_CUSTOM,
			Text:         "Do not have old passport?",
			Deeplink:     alterOptionDl,
		})
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&nrOnbPb.GlobalIssuedPassportDataVerificationScreenOptions{
			Title:            commontypes.GetTextFromStringFontColourFontStyle("Passport issued outside India?", "#313234", commontypes.FontStyle_HEADLINE_L),
			Subtitle:         commontypes.GetTextFromStringFontColourFontStyle("We couldn’t verify your renewed passport. Add the below details to proceed.", "#878A8D", commontypes.FontStyle_BODY_S),
			HeaderBar:        deeplink.HeaderBarForFederalOwnedScreen(),
			Flow:             formPkg.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION.String(),
			Ctas:             ctas,
			Separator:        ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/or_separator.png", 20, 382)),
			InputFieldBlocks: inputFieldBlock,
		}),
	}
}

func GetPassportHelpBottomSheet(title string, instructionList []string, identifierHint string) *dlPb.Deeplink {
	footerHintComponent := &sections.VerticalListSection{
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("IT WILL BE A 15 DIGIT NUMBER", "#929599", commontypes.FontStyle_OVERLINE_S_CAPS)),
			},
			{
				Content: GetAnyWithoutError(&components.Spacer{
					SpacingValue: components.Spacing_SPACING_S,
				}),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("Eg : <b>%s</b>", identifierHint), "#6A6D70", commontypes.FontStyle_BODY_S)),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Left:   20,
							Right:  20,
							Top:    12,
							Bottom: 12,
						},
						Border: &properties.BorderProperty{
							BorderThickness: 1,
							BorderColor:     "#E7E7E7",
							CornerRadius:    20,
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  20,
							TopRightCornerRadius: 20,
							BottomLeftCorner:     20,
							BottomRightCorner:    20,
						},
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
					},
				},
			},
		},
	}

	instructionComponent := make([]*components.Component, 0)
	for i, instruction := range instructionList {
		instructionComponent = append(instructionComponent, &components.Component{
			Content: GetAnyWithoutError(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(instruction, "#6A6D70", commontypes.FontStyle_BODY_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)).
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(fmt.Sprintf("https://epifi-icons.pointz.in/onboarding/num_%v_icon.png", i+1), 24, 24)).WithLeftImagePadding(12)),
		}, &components.Component{
			Content: GetAnyWithoutError(&components.Spacer{
				SpacingValue: components.Spacing_SPACING_S,
			}),
		})
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_SDUI_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.SduiBottomSheetOptions{
			Section: &sections.Section{
				Content: &sections.Section_VerticalListSection{
					VerticalListSection: &sections.VerticalListSection{
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/help_circle_big.png", 60, 60)),
							},
							{
								Content: GetAnyWithoutError(&components.Spacer{
									SpacingValue: components.Spacing_SPACING_XL,
								}),
							},
							{
								Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(title, "#313234", commontypes.FontStyle_SUBTITLE_L).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
							},
							{
								Content: GetAnyWithoutError(&components.Spacer{
									SpacingValue: components.Spacing_SPACING_M,
								}),
							},
							{
								Content: GetAnyWithoutError(&sections.VerticalListSection{
									Components:          instructionComponent,
									HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
								}),
							},
							{
								Content: GetAnyWithoutError(&components.Spacer{
									SpacingValue: components.Spacing_SPACING_XS,
								}),
							},
							{
								Content: GetAnyWithoutError(footerHintComponent),
							},
						},
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: &properties.ContainerProperty{
										Padding: &properties.PaddingProperty{
											Left:   24,
											Right:  24,
											Bottom: 24,
										},
									},
								},
							},
						},
					},
				},
			},
		}),
	}
}

func GetPassportManualReviewScreen() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_INSTRUCTIONS_SCREEN,
		ScreenOptions: &dlPb.Deeplink_InstructionsScreenOptions{
			InstructionsScreenOptions: &dlPb.InstructionsScreenOptions{
				Ctas: []*dlPb.Cta{
					{
						Type: dlPb.Cta_CUSTOM,
						Text: "Get Help",
						Deeplink: &dlPb.Deeplink{
							Screen: dlPb.Screen_HELP_MAIN,
						},
						DisplayTheme: dlPb.Cta_TERTIARY,
						Status:       dlPb.Cta_CTA_STATUS_ENABLED,
					},
				},
				Title: "Your passport details are under review",
				Instructions: []*dlPb.Instruction{
					{
						Title:       "Your passport details are being verified by our team.",
						Description: "This may take some time. Please check back later.",
						IconUrl:     "https://epifi-icons.pointz.in/onboarding/clock.png",
					},
				},
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/man_with_flag.png",
			},
		},
	}
}
