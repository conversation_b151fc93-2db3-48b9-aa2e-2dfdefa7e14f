// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "recordhashedcontactlocktimeout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RecordHashedContactLockTimeout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RecordHashedContactLockTimeout, nil
	case "dedupecacheexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DedupeCacheExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DedupeCacheExpiry, nil
	case "accessrevokecooldownduration":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AccessRevokeCooldownDuration, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AccessRevokeCooldownDuration\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AccessRevokeCooldownDuration[dynamicFieldPath[1]], nil

		}
		return obj.AccessRevokeCooldownDuration, nil
	case "whitelistnumberhashesforuaenr":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WhitelistNumberHashesForUAENR\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WhitelistNumberHashesForUAENR, nil
	case "whitelistnumberhashesforqatarnr":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WhitelistNumberHashesForQatarNR\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WhitelistNumberHashesForQatarNR, nil
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "onboardinguserupdatevkycsubscriber":
		return obj.OnboardingUserUpdateVKYCSubscriber.Get(dynamicFieldPath[1:])
	case "onboarding":
		return obj.Onboarding.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "shippingaddressupdatesubscriber":
		return obj.ShippingAddressUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "shippingaddressupdatecallbacksubscriber":
		return obj.ShippingAddressUpdateCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "vkyc":
		return obj.VKYC.Get(dynamicFieldPath[1:])
	case "vkycupdatesubscriber":
		return obj.VKYCUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "ekycsuccesssubscriber":
		return obj.EKYCSuccessSubscriber.Get(dynamicFieldPath[1:])
	case "creditreportpresencesubscriber":
		return obj.CreditReportPresenceSubscriber.Get(dynamicFieldPath[1:])
	case "creditreportverificationsubscriber":
		return obj.CreditReportVerificationSubscriber.Get(dynamicFieldPath[1:])
	case "eventsafpurchasesubscriber":
		return obj.EventsAfPurchaseSubscriber.Get(dynamicFieldPath[1:])
	case "eventscompletedtncsubscriber":
		return obj.EventsCompletedTnCSubscriber.Get(dynamicFieldPath[1:])
	case "userupdateeventsubscriber":
		return obj.UserUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "bankcustomerupdateeventsubscriber":
		return obj.BankCustomerUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "creditreportverificationeventsubscriber":
		return obj.CreditReportVerificationEventSubscriber.Get(dynamicFieldPath[1:])
	case "livmanualrevieweventsubscriber":
		return obj.LivManualReviewEventSubscriber.Get(dynamicFieldPath[1:])
	case "usercacheconfig":
		return obj.UserCacheConfig.Get(dynamicFieldPath[1:])
	case "usergroupcacheconfig":
		return obj.UserGroupCacheConfig.Get(dynamicFieldPath[1:])
	case "minimalusercacheconfig":
		return obj.MinimalUserCacheConfig.Get(dynamicFieldPath[1:])
	case "creditreportconfig":
		return obj.CreditReportConfig.Get(dynamicFieldPath[1:])
	case "creditreportderivedattributessubscriber":
		return obj.CreditReportDerivedAttributesSubscriber.Get(dynamicFieldPath[1:])
	case "processonboardingeventusercontactsubscriber":
		return obj.ProcessOnboardingEventUserContactSubscriber.Get(dynamicFieldPath[1:])
	case "processafueventusercontactsubscriber":
		return obj.ProcessAfuEventUserContactSubscriber.Get(dynamicFieldPath[1:])
	case "processdeleteusersubscriber":
		return obj.ProcessDeleteUserSubscriber.Get(dynamicFieldPath[1:])
	case "synconboardingsubscriber":
		return obj.SyncOnboardingSubscriber.Get(dynamicFieldPath[1:])
	case "processcardcreationevent":
		return obj.ProcessCardCreationEvent.Get(dynamicFieldPath[1:])
	case "vkyccallcompletedeventsubscriber":
		return obj.VKYCCallCompletedEventSubscriber.Get(dynamicFieldPath[1:])
	case "inhousevkyccallcompletedeventsubscriber":
		return obj.InHouseVkycCallCompletedEventSubscriber.Get(dynamicFieldPath[1:])
	case "processsavingsaccountupdateevent":
		return obj.ProcessSavingsAccountUpdateEvent.Get(dynamicFieldPath[1:])
	case "userdevicepropertiescacheconfig":
		return obj.UserDevicePropertiesCacheConfig.Get(dynamicFieldPath[1:])
	case "processaccessrevokecooldownsubscriber":
		return obj.ProcessAccessRevokeCooldownSubscriber.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnboardingConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minandroidversionformanualbalancerefreshonbaddfunds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidVersionForManualBalanceRefreshOnbAddFunds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidVersionForManualBalanceRefreshOnbAddFunds, nil
	case "miniosversionformanualbalancerefreshonbaddfunds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinIosVersionForManualBalanceRefreshOnbAddFunds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinIosVersionForManualBalanceRefreshOnbAddFunds, nil
	case "filiterolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FiLiteRolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FiLiteRolloutPercentage, nil
	case "secureusageguidelineversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SecureUsageGuidelineVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SecureUsageGuidelineVersion, nil
	case "parentnameprefillfromckycrolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ParentNamePrefillFromCKYCRolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ParentNamePrefillFromCKYCRolloutPercentage, nil
	case "simulatebalancecheckfailureforaddfunds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SimulateBalanceCheckFailureForAddFunds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SimulateBalanceCheckFailureForAddFunds, nil
	case "totalamountviaorderaddfunds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TotalAmountViaOrderAddFunds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TotalAmountViaOrderAddFunds, nil
	case "blockonboardingduetounlinkedpanandaadhaar":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockOnboardingDueToUnlinkedPANAndAadhaar\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockOnboardingDueToUnlinkedPANAndAadhaar, nil
	case "ispandobdropofftowealthanalyserenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPanDOBDropOffToWealthAnalyserEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPanDOBDropOffToWealthAnalyserEnabled, nil
	case "durationtoskipaddmoney":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DurationToSkipAddMoney\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DurationToSkipAddMoney, nil
	case "riskscreeningexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RiskScreeningExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RiskScreeningExpiry, nil
	case "livenesssummaryexpiryduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LivenessSummaryExpiryDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LivenessSummaryExpiryDuration, nil
	case "secureusageguidelineconsentinterval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SecureUsageGuidelineConsentInterval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SecureUsageGuidelineConsentInterval, nil
	case "healthconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.HealthConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.HealthConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.HealthConfig, nil
	case "referraloffercodesduringonboarding":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ReferralOfferCodesDuringOnboarding, nil
		case len(dynamicFieldPath) > 1:

			return obj.ReferralOfferCodesDuringOnboarding[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.ReferralOfferCodesDuringOnboarding, nil
	case "referralofferwidgetsduringonboarding":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ReferralOfferWidgetsDuringOnboarding, nil
		case len(dynamicFieldPath) > 1:

			return obj.ReferralOfferWidgetsDuringOnboarding[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.ReferralOfferWidgetsDuringOnboarding, nil
	case "durationtoskipaddfundsforaffluenceclasses":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DurationToSkipAddFundsForAffluenceClasses, nil
		case len(dynamicFieldPath) > 1:

			return obj.DurationToSkipAddFundsForAffluenceClasses[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DurationToSkipAddFundsForAffluenceClasses, nil
	case "weburlsforsalaryb2bflows":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.WebUrlsForSalaryB2BFlows, nil
		case len(dynamicFieldPath) > 1:

			return obj.WebUrlsForSalaryB2BFlows[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.WebUrlsForSalaryB2BFlows, nil
	case "affluenceclasseseligibleforbonustransitionscreen":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AffluenceClassesEligibleForBonusTransitionScreen, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AffluenceClassesEligibleForBonusTransitionScreen\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AffluenceClassesEligibleForBonusTransitionScreen[dynamicFieldPath[1]], nil

		}
		return obj.AffluenceClassesEligibleForBonusTransitionScreen, nil
	case "actorswhitelistedforprefunding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActorsWhitelistedForPreFunding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActorsWhitelistedForPreFunding, nil
	case "blockonboardingfromtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockOnboardingFromTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockOnboardingFromTime, nil
	case "blockonboardingtilltime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockOnboardingTillTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockOnboardingTillTime, nil
	case "blockonboardingmsg":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockOnboardingMsg\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockOnboardingMsg, nil
	case "finitecodefromattributionparamskey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FiniteCodeFromAttributionParamsKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FiniteCodeFromAttributionParamsKey, nil
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "abfeaturereleaseconfig":
		return obj.ABFeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "onbdetailscacheconfig":
		return obj.OnbDetailsCacheConfig.Get(dynamicFieldPath[1:])
	case "nextactiondecisioncacheconfig":
		return obj.NextActionDecisionCacheConfig.Get(dynamicFieldPath[1:])
	case "appscreeningconfig":
		return obj.AppScreeningConfig.Get(dynamicFieldPath[1:])
	case "onboardingvelocityconfig":
		return obj.OnboardingVelocityConfig.Get(dynamicFieldPath[1:])
	case "referraloffercodesabreleaseconfig":
		return obj.ReferralOfferCodesABReleaseConfig.Get(dynamicFieldPath[1:])
	case "offerwidgetonbscreensviafinitecodeabreleaseconfig":
		return obj.OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig.Get(dynamicFieldPath[1:])
	case "ekyccertupgradefeatureconfig":
		return obj.EKYCCertUpgradeFeatureConfig.Get(dynamicFieldPath[1:])
	case "minkycmandatoryaddfundconfig":
		return obj.MinKycMandatoryAddFundConfig.Get(dynamicFieldPath[1:])
	case "editemploymentinscreener":
		return obj.EditEmploymentInScreener.Get(dynamicFieldPath[1:])
	case "addfundsconfig":
		return obj.AddFundsConfig.Get(dynamicFieldPath[1:])
	case "resetonboardingjourney":
		return obj.ResetOnboardingJourney.Get(dynamicFieldPath[1:])
	case "confirmcardmailingaddress":
		return obj.ConfirmCardMailingAddress.Get(dynamicFieldPath[1:])
	case "intentselectionconfigv2":
		return obj.IntentSelectionConfigV2.Get(dynamicFieldPath[1:])
	case "softintentselectionconfig":
		return obj.SoftIntentSelectionConfig.Get(dynamicFieldPath[1:])
	case "directtofilite":
		return obj.DirectToFiLite.Get(dynamicFieldPath[1:])
	case "panvalidatev3featureconfig":
		return obj.PanValidateV3FeatureConfig.Get(dynamicFieldPath[1:])
	case "shownewconsentinpanvalidatev2":
		return obj.ShowNewConsentInPanValidateV2.Get(dynamicFieldPath[1:])
	case "nonresidentcrossvalidationconfig":
		return obj.NonResidentCrossValidationConfig.Get(dynamicFieldPath[1:])
	case "passportverificationconfig":
		return obj.PassportVerificationConfig.Get(dynamicFieldPath[1:])
	case "enabletriggernroaccountcreation":
		return obj.EnableTriggerNROAccountCreation.Get(dynamicFieldPath[1:])
	case "prefillparentnamefrompassportocr":
		return obj.PrefillParentNameFromPassportOCR.Get(dynamicFieldPath[1:])
	case "orderphysicaldebitcardconfig":
		return obj.OrderPhysicalDebitCardConfig.Get(dynamicFieldPath[1:])
	case "uqudocountryidverificationconfig":
		return obj.UqudoCountryIdVerificationConfig.Get(dynamicFieldPath[1:])
	case "walkthroughscreenconfig":
		return obj.WalkthroughScreenConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnboardingConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnboardingFlags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ignoreerrorsingetdataforcrossvalidationmanualreview":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IgnoreErrorsInGetDataForCrossValidationManualReview\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IgnoreErrorsInGetDataForCrossValidationManualReview, nil
	case "enablesecureusageguidelinesconsent":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSecureUsageGuidelinesConsent\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSecureUsageGuidelinesConsent, nil
	case "allowmanualreviewusers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowManualReviewUsers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowManualReviewUsers, nil
	case "enableaffluencev2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableAffluenceV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableAffluenceV2, nil
	case "blockccuserforpanlinkage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockCCUserForPANLinkage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockCCUserForPANLinkage, nil
	case "markckycsuccesswithoutckycdownload":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MarkCKYCSuccessWithoutCKYCDownload\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MarkCKYCSuccessWithoutCKYCDownload, nil
	case "enableupdateprofiledetailsstage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableUpdateProfileDetailsStage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableUpdateProfileDetailsStage, nil
	case "enablegnoaonerror":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableGNOAOnError\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableGNOAOnError, nil
	case "enablenonresidentonboardingcrossvalidation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableNonResidentOnboardingCrossValidation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableNonResidentOnboardingCrossValidation, nil
	case "skiplocationcheckfornronboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipLocationCheckForNROnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipLocationCheckForNROnboarding, nil
	case "skipcountryidverification":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipCountryIdVerification\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipCountryIdVerification, nil
	case "skippassportverification":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipPassportVerification\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipPassportVerification, nil
	case "enableriskcheckfornruser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRiskCheckForNRUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRiskCheckForNRUser, nil
	case "enableriskscreeningford2h":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRiskScreeningForD2H\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRiskScreeningForD2H, nil
	case "enableparentnameprefillfromckyc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableParentNamePrefillFromCKYC\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableParentNamePrefillFromCKYC, nil
	case "enableckyc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCkyc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCkyc, nil
	case "allownridedupeusers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowNRIDedupeUsers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowNRIDedupeUsers, nil
	case "enableglobalissuedpassportarnflow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableGlobalIssuedPassportARNFlow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableGlobalIssuedPassportARNFlow, nil
	case "blocknronboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockNrOnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockNrOnboarding, nil
	case "enablepanaadharcheckinprecustomercreationcheckstage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePanAadharCheckInPreCustomerCreationCheckStage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePanAadharCheckInPreCustomerCreationCheckStage, nil
	case "enablesadeclarationstage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSaDeclarationstage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSaDeclarationstage, nil
	case "disabledstages":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DisabledStages, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"DisabledStages\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.DisabledStages[dynamicFieldPath[1]], nil

		}
		return obj.DisabledStages, nil
	case "usenewlivenessflow":
		return obj.UseNewLivenessFlow.Get(dynamicFieldPath[1:])
	case "enablesavingsintroscreen":
		return obj.EnableSavingsIntroScreen.Get(dynamicFieldPath[1:])
	case "enablesmsparserconsentscreen":
		return obj.EnableSMSParserConsentScreen.Get(dynamicFieldPath[1:])
	case "wealthanalyserfeature":
		return obj.WealthAnalyserFeature.Get(dynamicFieldPath[1:])
	case "enablecontactpermissioninonb":
		return obj.EnableContactPermissionInOnb.Get(dynamicFieldPath[1:])
	case "enableglobalissuedpassportverification":
		return obj.EnableGlobalIssuedPassportVerification.Get(dynamicFieldPath[1:])
	case "enablepermissionstage":
		return obj.EnablePermissionStage.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnboardingFlags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnbDetailsCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "onbdetailsmincacheconfig":
		return obj.OnbDetailsMinCacheConfig.Get(dynamicFieldPath[1:])
	case "onbdetailsttlconfig":
		return obj.OnbDetailsTTLConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnbDetailsCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnbDetailsMinCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "onbdoneminttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnbDoneMinTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnbDoneMinTTL, nil
	case "onbinprogressminttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnbInProgressMinTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnbInProgressMinTTL, nil
	case "actortoonbttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActorToOnbTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActorToOnbTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnbDetailsMinCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnbDetailsTTLConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "onbdonettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnbDoneTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnbDoneTTL, nil
	case "onbinprogressttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnbInProgressTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnbInProgressTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnbDetailsTTLConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NextActionDecisionCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscacheenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCacheEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCacheEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NextActionDecisionCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AppScreeningConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablechatbotinchoicescreen":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableChatbotInChoiceScreen\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableChatbotInChoiceScreen, nil
	case "enablefiliteentrypointinchoicescreen":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableFiLiteEntryPointInChoiceScreen\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableFiLiteEntryPointInChoiceScreen, nil
	case "forcescreenercheckttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ForceScreenerCheckTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ForceScreenerCheckTTL, nil
	case "uancheckconfig":
		return obj.UANCheckConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AppScreeningConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UANCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "creditscorethreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreditScoreThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreditScoreThreshold, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UANCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ServiceHealth) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "healthstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HealthStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HealthStatus, nil
	case "from":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"From\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.From, nil
	case "to":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"To\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.To, nil
	case "message":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Message\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Message, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ServiceHealth", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnboardingVelocityConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "threshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Threshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Threshold, nil
	case "bucketprecision":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BucketPrecision\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BucketPrecision, nil
	case "bucketexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BucketExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BucketExpiry, nil
	case "queryrangeduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QueryRangeDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QueryRangeDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnboardingVelocityConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReferralOfferCodeDuringOnboarding) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "beforeappliedtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BeforeAppliedTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BeforeAppliedTitle, nil
	case "afterappliedtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AfterAppliedTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AfterAppliedTitle, nil
	case "beforeapplieddesc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BeforeAppliedDesc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BeforeAppliedDesc, nil
	case "afterapplieddesc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AfterAppliedDesc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AfterAppliedDesc, nil
	case "beforeappliediconurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BeforeAppliedIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BeforeAppliedIconUrl, nil
	case "afterappliediconurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AfterAppliedIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AfterAppliedIconUrl, nil
	case "code":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Code\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Code, nil
	case "underlyingfinitecode":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UnderlyingFiniteCode\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UnderlyingFiniteCode, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReferralOfferCodeDuringOnboarding", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReferralOfferWidgetDuringOnboarding) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "lefticon":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LeftIcon\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LeftIcon, nil
	case "offertext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OfferText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OfferText, nil
	case "bgcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BgColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BgColor, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReferralOfferWidgetDuringOnboarding", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DurationToSkipAddFundsForAffluenceClass) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "duration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Duration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Duration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DurationToSkipAddFundsForAffluenceClass", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *WebUrlForSalaryB2BFlow) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "url":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Url\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Url, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for WebUrlForSalaryB2BFlow", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MandatoryMinKycAddFundConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MandatoryMinKycAddFundConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnbAddFundsConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "v2pageminorversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"V2PageMinorVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.V2PageMinorVersion, nil
	case "showskipctaviaquest":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowSkipCtaViaQuest\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowSkipCtaViaQuest, nil
	case "showv2page":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowV2Page\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowV2Page, nil
	case "skipdurationviaquest":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipDurationViaQuest\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipDurationViaQuest, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnbAddFundsConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ResetOnboardingJourneyConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disableforios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableForIOS\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableForIOS, nil
	case "onboardingjourneyresetthresholdtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnboardingJourneyResetThresholdTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnboardingJourneyResetThresholdTime, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ResetOnboardingJourneyConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ConfirmCardMailingAddressConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxkycnamelentoskipconfirmcardmailingaddr":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxKYCNameLenToSkipConfirmCardMailingAddr\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxKYCNameLenToSkipConfirmCardMailingAddr, nil
	case "enableconfirmcardmailingaddressv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableConfirmCardMailingAddressV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableConfirmCardMailingAddressV2, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ConfirmCardMailingAddressConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IntentSelectionConfigV2) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "intentcollectionscreenpercentagerollout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntentCollectionScreenPercentageRollout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntentCollectionScreenPercentageRollout, nil
	case "enabledefaultintentselection":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableDefaultIntentSelection\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableDefaultIntentSelection, nil
	case "intentconfigmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.IntentConfigMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.IntentConfigMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.IntentConfigMap, nil
	case "intentcollectionscreenfeatureconfig":
		return obj.IntentCollectionScreenFeatureConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IntentSelectionConfigV2", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IntentConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RolloutPercentage, nil
	case "allowedusergroups":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedUserGroups\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedUserGroups, nil
	case "featureconfig":
		return obj.FeatureConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IntentConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SoftIntentSelectionConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "softintentcollectionscreenpercentagerollout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SoftIntentCollectionScreenPercentageRollout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SoftIntentCollectionScreenPercentageRollout, nil
	case "allowedsoftintents":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AllowedSoftIntents, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AllowedSoftIntents\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AllowedSoftIntents[dynamicFieldPath[1]], nil

		}
		return obj.AllowedSoftIntents, nil
	case "softintentcollectionscreenfeatureconfig":
		return obj.SoftIntentCollectionScreenFeatureConfig.Get(dynamicFieldPath[1:])
	case "postonboardingsoftintentscreenconfig":
		return obj.PostOnboardingSoftIntentScreenConfig.Get(dynamicFieldPath[1:])
	case "preonboardingcompletionsoftintentscreenconfig":
		return obj.PreOnboardingCompletionSoftIntentScreenConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SoftIntentSelectionConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PostOnboardingSoftIntentScreenConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enabled, nil
	case "waitduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WaitDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WaitDuration, nil
	case "maxwaitduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxWaitDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxWaitDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PostOnboardingSoftIntentScreenConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PreOnboardingCompletionSoftIntentScreenConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PreOnboardingCompletionSoftIntentScreenConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DirectToFiLiteConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "variant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Variant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Variant, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DirectToFiLiteConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CrossValidationConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "dummydynamicflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DummyDynamicFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DummyDynamicFlag, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CrossValidationConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PassportVerificationConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ignorevendorerror":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IgnoreVendorError\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IgnoreVendorError, nil
	case "passportexpirythreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PassportExpiryThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PassportExpiryThreshold, nil
	case "detailsconfirmationfeatureconfig":
		return obj.DetailsConfirmationFeatureConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PassportVerificationConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OrderPhysicalDebitCardConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableviaquest":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableViaQuest\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableViaQuest, nil
	case "freephysicaldcrefereerewardconfig":
		return obj.FreePhysicalDCRefereeRewardConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OrderPhysicalDebitCardConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FreePhysicalDCRefereeRewardConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "activefrom":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActiveFrom\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActiveFrom, nil
	case "activetill":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActiveTill\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActiveTill, nil
	case "questexpvariablepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QuestExpVariablePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QuestExpVariablePath, nil
	case "questexpvariablevalue":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QuestExpVariableValue\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QuestExpVariableValue, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FreePhysicalDCRefereeRewardConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UqudoCountryIdVerificationConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "screendetectionthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ScreenDetectionThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ScreenDetectionThreshold, nil
	case "printdetectionthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PrintDetectionThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PrintDetectionThreshold, nil
	case "phototamperingthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PhotoTamperingThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PhotoTamperingThreshold, nil
	case "facematchthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FaceMatchThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FaceMatchThreshold, nil
	case "enabletampercheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableTamperCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableTamperCheck, nil
	case "idexpirythreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IdExpiryThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IdExpiryThreshold, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UqudoCountryIdVerificationConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *WalkthroughScreenConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "featureconfig":
		return obj.FeatureConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for WalkthroughScreenConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableactiveproductsbypancheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableActiveProductsByPANCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableActiveProductsByPANCheck, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VKYC) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "instructionpageskipoptiontime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InstructionPageSkipOptionTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InstructionPageSkipOptionTime, nil
	case "landingpageskipoptiontime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LandingPageSkipOptionTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LandingPageSkipOptionTime, nil
	case "enabledemandmanagement":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableDemandManagement\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableDemandManagement, nil
	case "enablepassportfaceimagecheckfornrexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePassportFaceImageCheckForNRExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePassportFaceImageCheckForNRExpiry, nil
	case "showauditoracceptedtiletime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowAuditorAcceptedTileTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowAuditorAcceptedTileTime, nil
	case "nrkycexpiryforvkyc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NRKYCExpiryForVKYC\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NRKYCExpiryForVKYC, nil
	case "option":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.Option, nil
		case len(dynamicFieldPath) > 1:

			return obj.Option[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.Option, nil
	case "enablevkycflowv2":
		return obj.EnableVKYCFlowV2.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VKYC", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VKYCOption) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vkycenablepercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VkycEnablePercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VkycEnablePercentage, nil
	case "minandroidversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidVersion, nil
	case "miniosversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinIOSVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinIOSVersion, nil
	case "isvkycapprovalblocking":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsVKYCApprovalBlocking\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsVKYCApprovalBlocking, nil
	case "enablevkycscheduleflow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableVKYCScheduleFlow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableVKYCScheduleFlow, nil
	case "skipoptionflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipOptionFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipOptionFlag, nil
	case "ignorefullkycuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IgnoreFullKYCUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IgnoreFullKYCUser, nil
	case "instructionpageskipoptionflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InstructionPageSkipOptionFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InstructionPageSkipOptionFlag, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VKYCOption", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UserCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UserCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UserGroupCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UserGroupCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MinimalUserCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MinimalUserCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CreditReportConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "creditreportpresenceenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreditReportPresenceEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreditReportPresenceEnabled, nil
	case "experianconsentconfig":
		return obj.ExperianConsentConfig.Get(dynamicFieldPath[1:])
	case "downloadwaitconfigforwealthbuilder":
		return obj.DownloadWaitConfigForWealthBuilder.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CreditReportConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExperianConsentConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "consentextension":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsentExtension\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsentExtension, nil
	case "consentexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsentExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsentExpiry, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExperianConsentConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DownloadWaitConfigForWealthBuilder) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxattemptsforcheckingdownloadstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxAttemptsForCheckingDownloadStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxAttemptsForCheckingDownloadStatus, nil
	case "sleepdurationbetweeneachattempt":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SleepDurationBetweenEachAttempt\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SleepDurationBetweenEachAttempt, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DownloadWaitConfigForWealthBuilder", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UserDevicePropertiesCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscacheenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCacheEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCacheEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UserDevicePropertiesCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
